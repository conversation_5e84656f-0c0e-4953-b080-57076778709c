{"version": 3, "names": ["_assert", "require", "_util", "mMap", "WeakMap", "m", "node", "has", "set", "get", "hasOwn", "Object", "prototype", "hasOwnProperty", "makePredicate", "propertyName", "knownTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t", "getTypes", "assertNode", "result", "check", "child", "Array", "isArray", "some", "isNode", "assert", "strictEqual", "predicate", "keys", "VISITOR_KEYS", "type", "i", "length", "key", "meta", "call", "opaqueTypes", "FunctionExpression", "ArrowFunctionExpression", "sideEffectTypes", "CallExpression", "ForInStatement", "UnaryExpression", "BinaryExpression", "AssignmentExpression", "UpdateExpression", "NewExpression", "leapTypes", "YieldExpression", "BreakStatement", "ContinueStatement", "ReturnStatement", "ThrowStatement", "hasSideEffects", "exports", "containsLeap"], "sources": ["../../src/regenerator/meta.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport { getTypes } from \"./util.ts\";\n\nconst mMap = new WeakMap();\nfunction m(node: any) {\n  if (!mMap.has(node)) {\n    mMap.set(node, {});\n  }\n  return mMap.get(node);\n}\n\nconst hasOwn = Object.prototype.hasOwnProperty;\n\nfunction makePredicate(propertyName: any, knownTypes: any) {\n  function onlyChildren(node: any) {\n    const t = getTypes();\n    t.assertNode(node);\n\n    // Assume no side effects until we find out otherwise.\n    let result = false;\n\n    function check(child: any) {\n      if (result) {\n        // Do nothing.\n      } else if (Array.isArray(child)) {\n        child.some(check);\n      } else if (t.isNode(child)) {\n        assert.strictEqual(result, false);\n        result = predicate(child);\n      }\n      return result;\n    }\n\n    const keys = t.VISITOR_KEYS[node.type];\n    if (keys) {\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const child = node[key];\n        check(child);\n      }\n    }\n\n    return result;\n  }\n\n  function predicate(node: any) {\n    getTypes().assertNode(node);\n\n    const meta = m(node);\n    if (hasOwn.call(meta, propertyName)) return meta[propertyName];\n\n    // Certain types are \"opaque,\" which means they have no side\n    // effects or leaps and we don't care about their subexpressions.\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    if (hasOwn.call(opaqueTypes, node.type))\n      return (meta[propertyName] = false);\n\n    if (hasOwn.call(knownTypes, node.type)) return (meta[propertyName] = true);\n\n    return (meta[propertyName] = onlyChildren(node));\n  }\n\n  predicate.onlyChildren = onlyChildren;\n\n  return predicate;\n}\n\nconst opaqueTypes = {\n  FunctionExpression: true,\n  ArrowFunctionExpression: true,\n};\n\n// These types potentially have side effects regardless of what side\n// effects their subexpressions have.\nconst sideEffectTypes = {\n  CallExpression: true, // Anything could happen!\n  ForInStatement: true, // Modifies the key variable.\n  UnaryExpression: true, // Think delete.\n  BinaryExpression: true, // Might invoke .toString() or .valueOf().\n  AssignmentExpression: true, // Side-effecting by definition.\n  UpdateExpression: true, // Updates are essentially assignments.\n  NewExpression: true, // Similar to CallExpression.\n};\n\n// These types are the direct cause of all leaps in control flow.\nconst leapTypes = {\n  YieldExpression: true,\n  BreakStatement: true,\n  ContinueStatement: true,\n  ReturnStatement: true,\n  ThrowStatement: true,\n};\n\n// All leap types are also side effect types.\nfor (const type in leapTypes) {\n  if (hasOwn.call(leapTypes, type)) {\n    sideEffectTypes[type as keyof typeof sideEffectTypes] =\n      leapTypes[type as keyof typeof leapTypes];\n  }\n}\n\nexport const hasSideEffects = makePredicate(\"hasSideEffects\", sideEffectTypes);\nexport const containsLeap = makePredicate(\"containsLeap\", leapTypes);\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,MAAME,IAAI,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC1B,SAASC,CAACA,CAACC,IAAS,EAAE;EACpB,IAAI,CAACH,IAAI,CAACI,GAAG,CAACD,IAAI,CAAC,EAAE;IACnBH,IAAI,CAACK,GAAG,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC;EACpB;EACA,OAAOH,IAAI,CAACM,GAAG,CAACH,IAAI,CAAC;AACvB;AAEA,MAAMI,MAAM,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAE9C,SAASC,aAAaA,CAACC,YAAiB,EAAEC,UAAe,EAAE;EACzD,SAASC,YAAYA,CAACX,IAAS,EAAE;IAC/B,MAAMY,CAAC,GAAG,IAAAC,cAAQ,EAAC,CAAC;IACpBD,CAAC,CAACE,UAAU,CAACd,IAAI,CAAC;IAGlB,IAAIe,MAAM,GAAG,KAAK;IAElB,SAASC,KAAKA,CAACC,KAAU,EAAE;MACzB,IAAIF,MAAM,EAAE,CAEZ,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;QAC/BA,KAAK,CAACG,IAAI,CAACJ,KAAK,CAAC;MACnB,CAAC,MAAM,IAAIJ,CAAC,CAACS,MAAM,CAACJ,KAAK,CAAC,EAAE;QAC1BK,OAAM,CAACC,WAAW,CAACR,MAAM,EAAE,KAAK,CAAC;QACjCA,MAAM,GAAGS,SAAS,CAACP,KAAK,CAAC;MAC3B;MACA,OAAOF,MAAM;IACf;IAEA,MAAMU,IAAI,GAAGb,CAAC,CAACc,YAAY,CAAC1B,IAAI,CAAC2B,IAAI,CAAC;IACtC,IAAIF,IAAI,EAAE;MACR,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,MAAME,GAAG,GAAGL,IAAI,CAACG,CAAC,CAAC;QACnB,MAAMX,KAAK,GAAGjB,IAAI,CAAC8B,GAAG,CAAC;QACvBd,KAAK,CAACC,KAAK,CAAC;MACd;IACF;IAEA,OAAOF,MAAM;EACf;EAEA,SAASS,SAASA,CAACxB,IAAS,EAAE;IAC5B,IAAAa,cAAQ,EAAC,CAAC,CAACC,UAAU,CAACd,IAAI,CAAC;IAE3B,MAAM+B,IAAI,GAAGhC,CAAC,CAACC,IAAI,CAAC;IACpB,IAAII,MAAM,CAAC4B,IAAI,CAACD,IAAI,EAAEtB,YAAY,CAAC,EAAE,OAAOsB,IAAI,CAACtB,YAAY,CAAC;IAK9D,IAAIL,MAAM,CAAC4B,IAAI,CAACC,WAAW,EAAEjC,IAAI,CAAC2B,IAAI,CAAC,EACrC,OAAQI,IAAI,CAACtB,YAAY,CAAC,GAAG,KAAK;IAEpC,IAAIL,MAAM,CAAC4B,IAAI,CAACtB,UAAU,EAAEV,IAAI,CAAC2B,IAAI,CAAC,EAAE,OAAQI,IAAI,CAACtB,YAAY,CAAC,GAAG,IAAI;IAEzE,OAAQsB,IAAI,CAACtB,YAAY,CAAC,GAAGE,YAAY,CAACX,IAAI,CAAC;EACjD;EAEAwB,SAAS,CAACb,YAAY,GAAGA,YAAY;EAErC,OAAOa,SAAS;AAClB;AAEA,MAAMS,WAAW,GAAG;EAClBC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE;AAC3B,CAAC;AAID,MAAMC,eAAe,GAAG;EACtBC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,eAAe,EAAE,IAAI;EACrBC,gBAAgB,EAAE,IAAI;EACtBC,oBAAoB,EAAE,IAAI;EAC1BC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE;AACjB,CAAC;AAGD,MAAMC,SAAS,GAAG;EAChBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE,IAAI;EACpBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,cAAc,EAAE;AAClB,CAAC;AAGD,KAAK,MAAMtB,IAAI,IAAIiB,SAAS,EAAE;EAC5B,IAAIxC,MAAM,CAAC4B,IAAI,CAACY,SAAS,EAAEjB,IAAI,CAAC,EAAE;IAChCS,eAAe,CAACT,IAAI,CAAiC,GACnDiB,SAAS,CAACjB,IAAI,CAA2B;EAC7C;AACF;AAEO,MAAMuB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG1C,aAAa,CAAC,gBAAgB,EAAE4B,eAAe,CAAC;AACvE,MAAMgB,YAAY,GAAAD,OAAA,CAAAC,YAAA,GAAG5C,aAAa,CAAC,cAAc,EAAEoC,SAAS,CAAC", "ignoreList": []}