{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperCreateClassFeaturesPlugin", "generateUid", "scope", "denyList", "name", "uid", "i", "has", "_default", "exports", "default", "declare", "types", "t", "template", "assertVersion", "manipulateOptions", "_", "parser", "plugins", "push", "pre", "enableFeature", "file", "FEATURES", "staticBlocks", "visitor", "ClassBody", "classBody", "privateNames", "Set", "body", "get", "path", "isPrivate", "add", "node", "isStaticBlock", "staticBlockPrivateId", "staticBlockRef", "privateName", "identifier", "replacement", "blockBody", "length", "isExpressionStatement", "inheritsComments", "expression", "ast", "replaceWith", "classPrivateProperty"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { Scope } from \"@babel/core\";\n\nimport {\n  enableFeature,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\n\n/**\n * Generate a uid that is not in `denyList`\n *\n * @param {Scope} scope\n * @param {Set<string>} denyList a deny list that the generated uid should avoid\n * @returns\n */\nfunction generateUid(scope: Scope, denyList: Set<string>) {\n  const name = \"\";\n  let uid;\n  let i = 1;\n  do {\n    uid = `_${name}`;\n    if (i > 1) uid += i;\n    i++;\n  } while (denyList.has(uid));\n  return uid;\n}\n\nexport default declare(({ types: t, template, assertVersion }) => {\n  assertVersion(REQUIRED_VERSION(\"^7.12.0\"));\n\n  return {\n    name: \"transform-class-static-block\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"classStaticBlock\"),\n\n    pre() {\n      // Enable this in @babel/helper-create-class-features-plugin, so that it\n      // can be handled by the private fields and methods transform.\n      enableFeature(this.file, FEATURES.staticBlocks, /* loose */ false);\n    },\n\n    visitor: {\n      // Run on ClassBody and not on class so that if @babel/helper-create-class-features-plugin\n      // is enabled it can generate optimized output without passing from the intermediate\n      // private fields representation.\n      ClassBody(classBody) {\n        const { scope } = classBody;\n        const privateNames = new Set<string>();\n        const body = classBody.get(\"body\");\n        for (const path of body) {\n          if (path.isPrivate()) {\n            privateNames.add(path.get(\"key.id\").node.name);\n          }\n        }\n        for (const path of body) {\n          if (!path.isStaticBlock()) continue;\n          const staticBlockPrivateId = generateUid(scope, privateNames);\n          privateNames.add(staticBlockPrivateId);\n          const staticBlockRef = t.privateName(\n            t.identifier(staticBlockPrivateId),\n          );\n\n          let replacement;\n          const blockBody = path.node.body;\n          // We special-case the single expression case to avoid the iife, since\n          // it's common.\n          if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n            replacement = t.inheritsComments(\n              blockBody[0].expression,\n              blockBody[0],\n            );\n          } else {\n            replacement = template.expression.ast`(() => { ${blockBody} })()`;\n          }\n\n          path.replaceWith(\n            t.classPrivateProperty(\n              staticBlockRef,\n              replacement,\n              [],\n              /* static */ true,\n            ),\n          );\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAGA,IAAAC,gCAAA,GAAAD,OAAA;AAYA,SAASE,WAAWA,CAACC,KAAY,EAAEC,QAAqB,EAAE;EACxD,MAAMC,IAAI,GAAG,EAAE;EACf,IAAIC,GAAG;EACP,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACDD,GAAG,GAAG,IAAID,IAAI,EAAE;IAChB,IAAIE,CAAC,GAAG,CAAC,EAAED,GAAG,IAAIC,CAAC;IACnBA,CAAC,EAAE;EACL,CAAC,QAAQH,QAAQ,CAACI,GAAG,CAACF,GAAG,CAAC;EAC1B,OAAOA,GAAG;AACZ;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC,QAAQ;EAAEC;AAAc,CAAC,KAAK;EAChEA,aAAa,sCAA4B,CAAC;EAE1C,OAAO;IACLX,IAAI,EAAE,8BAA8B;IACpCY,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,kBAAkB,CAAC;IAE1DC,GAAGA,CAAA,EAAG;MAGJ,IAAAC,8CAAa,EAAC,IAAI,CAACC,IAAI,EAAEC,yCAAQ,CAACC,YAAY,EAAc,KAAK,CAAC;IACpE,CAAC;IAEDC,OAAO,EAAE;MAIPC,SAASA,CAACC,SAAS,EAAE;QACnB,MAAM;UAAE1B;QAAM,CAAC,GAAG0B,SAAS;QAC3B,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;QACtC,MAAMC,IAAI,GAAGH,SAAS,CAACI,GAAG,CAAC,MAAM,CAAC;QAClC,KAAK,MAAMC,IAAI,IAAIF,IAAI,EAAE;UACvB,IAAIE,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;YACpBL,YAAY,CAACM,GAAG,CAACF,IAAI,CAACD,GAAG,CAAC,QAAQ,CAAC,CAACI,IAAI,CAAChC,IAAI,CAAC;UAChD;QACF;QACA,KAAK,MAAM6B,IAAI,IAAIF,IAAI,EAAE;UACvB,IAAI,CAACE,IAAI,CAACI,aAAa,CAAC,CAAC,EAAE;UAC3B,MAAMC,oBAAoB,GAAGrC,WAAW,CAACC,KAAK,EAAE2B,YAAY,CAAC;UAC7DA,YAAY,CAACM,GAAG,CAACG,oBAAoB,CAAC;UACtC,MAAMC,cAAc,GAAG1B,CAAC,CAAC2B,WAAW,CAClC3B,CAAC,CAAC4B,UAAU,CAACH,oBAAoB,CACnC,CAAC;UAED,IAAII,WAAW;UACf,MAAMC,SAAS,GAAGV,IAAI,CAACG,IAAI,CAACL,IAAI;UAGhC,IAAIY,SAAS,CAACC,MAAM,KAAK,CAAC,IAAI/B,CAAC,CAACgC,qBAAqB,CAACF,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;YACnED,WAAW,GAAG7B,CAAC,CAACiC,gBAAgB,CAC9BH,SAAS,CAAC,CAAC,CAAC,CAACI,UAAU,EACvBJ,SAAS,CAAC,CAAC,CACb,CAAC;UACH,CAAC,MAAM;YACLD,WAAW,GAAG5B,QAAQ,CAACiC,UAAU,CAACC,GAAG,YAAYL,SAAS,OAAO;UACnE;UAEAV,IAAI,CAACgB,WAAW,CACdpC,CAAC,CAACqC,oBAAoB,CACpBX,cAAc,EACdG,WAAW,EACX,EAAE,EACW,IACf,CACF,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}