"use strict";

exports.__esModule = true;
exports.default = void 0;
// This file is automatically generated by scripts/build-corejs3-shipped-proposals.mjs
var _default = new Set(["esnext.suppressed-error.constructor", "esnext.array.from-async", "esnext.array.group", "esnext.array.group-to-map", "esnext.data-view.get-float16", "esnext.data-view.set-float16", "esnext.error.is-error", "esnext.iterator.dispose", "esnext.json.is-raw-json", "esnext.json.parse", "esnext.json.raw-json", "esnext.math.f16round", "esnext.regexp.escape", "esnext.symbol.async-dispose", "esnext.symbol.dispose", "esnext.symbol.metadata", "esnext.uint8-array.from-base64", "esnext.uint8-array.from-hex", "esnext.uint8-array.set-from-base64", "esnext.uint8-array.set-from-hex", "esnext.uint8-array.to-base64", "esnext.uint8-array.to-hex"]);
exports.default = _default;