load("@fbsource//tools/build_defs/apple:flag_defs.bzl", "get_objc_arc_preprocessor_flags", "get_preprocessor_flags_for_build_mode")
load("@fbsource//tools/build_defs/oss:rn_defs.bzl", "react_native_xplat_target", "rn_apple_library", "rn_extra_build_flags")
load(
    "@fbsource//xplat/configurations/buck/apple/plugins/sad_xplat_hosted_configurations:react_module_registration.bzl",
    "react_module_plugin_providers",
)

rn_apple_library(
    name = "CoreModulesApple",
    srcs = glob(
        [
            "**/*.m",
            "**/*.mm",
        ],
    ),
    exported_headers = glob(["**/*.h"]),
    autoglob = False,
    compiler_flags = [
        "-Wno-error=unguarded-availability-new",
        "-Wno-unknown-warning-option",
        "-Wno-unused-property-ivar",
    ],
    contacts = ["<EMAIL>"],
    enable_exceptions = True,
    exported_preprocessor_flags = rn_extra_build_flags(),
    extension_api_only = True,
    frameworks = [
        "Foundation",
        "UIKit",
        "UserNotifications",
        "WebKit",
    ],
    header_path_prefix = "React",
    labels = [
        "depslint_never_remove",  # Some old NativeModule still relies on +load unfortunately.
        "disable_plugins_only_validation",
        "fbios_link_group:xplat/default/public.react_native.infra",
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
        "talkios_link_group:xplat/default/public.react_native.infra",
    ],
    link_whole = True,
    platform_preprocessor_flags = [(
        "linux",
        ["-D PIC_MODIFIER=@PLT"],
    )],
    plugins =
        react_module_plugin_providers(
            name = "AccessibilityManager",
            native_class_func = "RCTAccessibilityManagerCls",
        ) + react_module_plugin_providers(
            name = "Appearance",
            native_class_func = "RCTAppearanceCls",
        ) + react_module_plugin_providers(
            name = "DeviceInfo",
            native_class_func = "RCTDeviceInfoCls",
        ) + react_module_plugin_providers(
            name = "ExceptionsManager",
            native_class_func = "RCTExceptionsManagerCls",
        ) + react_module_plugin_providers(
            name = "PlatformConstants",
            native_class_func = "RCTPlatformCls",
        ) + react_module_plugin_providers(
            name = "Clipboard",
            native_class_func = "RCTClipboardCls",
        ) + react_module_plugin_providers(
            name = "I18nManager",
            native_class_func = "RCTI18nManagerCls",
        ) + react_module_plugin_providers(
            name = "SourceCode",
            native_class_func = "RCTSourceCodeCls",
        ) + react_module_plugin_providers(
            name = "ActionSheetManager",
            native_class_func = "RCTActionSheetManagerCls",
        ) + react_module_plugin_providers(
            name = "AlertManager",
            native_class_func = "RCTAlertManagerCls",
        ) + react_module_plugin_providers(
            name = "Timing",
            native_class_func = "RCTTimingCls",
        ) + react_module_plugin_providers(
            name = "StatusBarManager",
            native_class_func = "RCTStatusBarManagerCls",
        ) + react_module_plugin_providers(
            name = "KeyboardObserver",
            native_class_func = "RCTKeyboardObserverCls",
        ) + react_module_plugin_providers(
            name = "AppState",
            native_class_func = "RCTAppStateCls",
        ) + react_module_plugin_providers(
            name = "PerfMonitor",
            native_class_func = "RCTPerfMonitorCls",
        ) + react_module_plugin_providers(
            name = "DevMenu",
            native_class_func = "RCTDevMenuCls",
        ) + react_module_plugin_providers(
            name = "DevSettings",
            native_class_func = "RCTDevSettingsCls",
        ) + react_module_plugin_providers(
            name = "RedBox",
            native_class_func = "RCTRedBoxCls",
        ) + react_module_plugin_providers(
            name = "LogBox",
            native_class_func = "RCTLogBoxCls",
        ) + react_module_plugin_providers(
            name = "WebSocketExecutor",
            native_class_func = "RCTWebSocketExecutorCls",
        ) + react_module_plugin_providers(
            name = "WebSocketModule",
            native_class_func = "RCTWebSocketModuleCls",
        ) + react_module_plugin_providers(
            name = "DevLoadingView",
            native_class_func = "RCTDevLoadingViewCls",
        ) + react_module_plugin_providers(
            name = "DevSplitBundleLoader",
            native_class_func = "RCTDevSplitBundleLoaderCls",
        ) + react_module_plugin_providers(
            name = "EventDispatcher",
            native_class_func = "RCTEventDispatcherCls",
        ),
    plugins_header = "FBCoreModulesPlugins.h",
    preprocessor_flags = get_objc_arc_preprocessor_flags() + get_preprocessor_flags_for_build_mode() + rn_extra_build_flags() + [
        "-DRN_DISABLE_OSS_PLUGIN_HEADER",
    ],
    reexport_all_header_dependencies = True,
    visibility = ["PUBLIC"],
    deps = [
        react_native_xplat_target("react/renderer/mapbuffer:mapbufferApple"),
    ],
    exported_deps = [
        "//xplat/js/react-native-github:FBReactNativeSpecApple",
        "//xplat/js/react-native-github:RCTLinkingApple",
        "//xplat/js/react-native-github:RCTPushNotificationApple",
        "//xplat/js/react-native-github:RCTSettingsApple",
        "//xplat/js/react-native-github:ReactInternalApple",
    ],
)
