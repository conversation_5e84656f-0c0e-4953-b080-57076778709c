load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "FBJNI_TARGET", "get_objc_arc_preprocessor_flags", "get_preprocessor_flags_for_build_mode", "get_static_library_ios_flags", "react_native_target", "react_native_xplat_shared_library_target", "react_native_xplat_target", "rn_xplat_cxx_library", "subdir_glob")

oncall("react_native")

rn_xplat_cxx_library(
    name = "core",
    srcs = glob(
        ["ReactCommon/**/*.cpp"],
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    compiler_flags = [
        "-Wno-global-constructors",
    ],
    compiler_flags_pedantic = True,
    fbandroid_deps = [
        react_native_target("jni/react/jni:jni"),
        FBJNI_TARGET,
    ],
    fbandroid_exported_headers = subdir_glob(
        [
            ("platform/android/ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    fbandroid_srcs = glob(
        [
            "platform/android/ReactCommon/*.cpp",
        ],
    ),
    fbobjc_compiler_flags = [
        "-fobjc-arc-exceptions",
    ],
    fbobjc_inherited_buck_flags = get_static_library_ios_flags(),
    fbobjc_preprocessor_flags = get_objc_arc_preprocessor_flags() + get_preprocessor_flags_for_build_mode(),
    ios_deps = [
        "//xplat/FBBaseLite:FBBaseLite",
        "//xplat/js/react-native-github:RCTCxxModule",
        "//xplat/js/react-native-github:ReactInternal",
    ],
    ios_exported_headers = subdir_glob(
        [
            ("platform/ios/ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    ios_frameworks = [
        "$SDKROOT/System/Library/Frameworks/Foundation.framework",
    ],
    ios_srcs = glob(
        [
            "platform/ios/**/*.cpp",
            "platform/ios/**/*.mm",
        ],
    ),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/folly:memory",
        "//xplat/jsi:JSIDynamic",
        react_native_xplat_target("cxxreact:bridge"),
        react_native_xplat_target("cxxreact:module"),
        react_native_xplat_target("callinvoker:callinvoker"),
        react_native_xplat_target("react/debug:debug"),
        react_native_xplat_target("reactperflogger:reactperflogger"),
    ],
    exported_deps = [
        react_native_xplat_target("react/bridging:bridging"),
        react_native_xplat_shared_library_target("jsi:jsi"),
    ],
)
