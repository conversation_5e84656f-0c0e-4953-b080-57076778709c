load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "FBJNI_TARGET", "get_objc_arc_preprocessor_flags", "get_preprocessor_flags_for_build_mode", "get_static_library_ios_flags", "react_native_dep", "react_native_target", "react_native_xplat_target", "rn_android_library", "rn_xplat_cxx_library", "subdir_glob")

oncall("react_native")

rn_xplat_cxx_library(
    name = "samples",
    srcs = glob(
        ["ReactCommon/**/*.cpp"],
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    compiler_flags_pedantic = True,
    fbandroid_deps = [
        react_native_target("jni/react/jni:jni"),
        FBJNI_TARGET,
    ],
    fbandroid_exported_headers = subdir_glob(
        [
            ("platform/android/ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    fbandroid_srcs = glob(
        [
            "platform/android/**/*.cpp",
        ],
    ),
    fbobjc_compiler_flags = [
        "-fobjc-arc-exceptions",
    ],
    fbobjc_inherited_buck_flags = get_static_library_ios_flags(),
    fbobjc_preprocessor_flags = get_objc_arc_preprocessor_flags() + get_preprocessor_flags_for_build_mode(),
    force_static = True,
    ios_deps = [
        "//xplat/FBBaseLite:FBBaseLite",
        "//xplat/js/react-native-github:RCTCxxBridge",
        "//xplat/js/react-native-github:RCTCxxModule",
        "//xplat/js/react-native-github:RCTLinking",
        "//xplat/js/react-native-github:RCTPushNotification",
        "//xplat/js/react-native-github:ReactInternal",
    ],
    ios_exported_headers = subdir_glob(
        [
            ("platform/ios/ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    ios_frameworks = [
        "$SDKROOT/System/Library/Frameworks/Foundation.framework",
    ],
    ios_srcs = glob(
        [
            "platform/ios/**/*.cpp",
            "platform/ios/**/*.mm",
        ],
    ),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_xplat_target("cxxreact:module"),
    ],
    exported_deps = [
        "//xplat/jsi:jsi",
        react_native_xplat_target("react/nativemodule/core:core"),
    ],
)

rn_android_library(
    name = "impl",
    srcs = glob(["platform/android/*.java"]),
    autoglob = False,
    language = "JAVA",
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//fbandroid/java/com/facebook/debug/log:log",
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/jsr-330:jsr-330"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        ":samples",
    ],
    exported_deps = [
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
    ],
)
