load("@fbsource//tools/build_defs:glob_defs.bzl", "subdir_glob")
load("@fbsource//tools/build_defs:platform_defs.bzl", "ANDROID", "APPLE", "CXX")
load("@fbsource//tools/build_defs/apple:flag_defs.bzl", "flags", "get_preprocessor_flags_for_build_mode", "get_static_library_ios_flags")
load("@fbsource//tools/build_defs/oss:rn_defs.bzl", "get_apple_inspector_flags", "rn_xplat_cxx_library")

APPLE_COMPILER_FLAGS = flags.get_flag_value(
    get_static_library_ios_flags(),
    "compiler_flags",
)

rn_xplat_cxx_library(
    name = "config",
    srcs = glob(["**/*.cpp"]),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "**/*.h"),
        ],
        prefix = "react/config",
    ),
    compiler_flags_enable_exceptions = True,
    compiler_flags_enable_rtti = True,  # for ReactNativeConfig
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/folly:memory",
    ],
)
