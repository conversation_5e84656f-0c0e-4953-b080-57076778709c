load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "react_native_xplat_target", "rn_xplat_cxx_library")

# TODO: Expolre merging this module into venice so we don't to load this library separately
rn_xplat_cxx_library(
    name = "jserrorhandler",
    srcs = glob(["*.cpp"]),
    header_namespace = "",
    exported_headers = {"JsErrorHandler/JsErrorHandler.h": "JsErrorHandler.h"},
    compiler_flags = [
        "-fexceptions",
        "-frtti",
    ],
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//xplat/folly:dynamic",
        "//xplat/folly:json",
        "//xplat/jsi:jsi",
        react_native_xplat_target("react/renderer/mapbuffer:mapbuffer"),
    ],
)
