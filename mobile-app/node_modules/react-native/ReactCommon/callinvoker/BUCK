load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "rn_xplat_cxx_library", "subdir_glob")

rn_xplat_cxx_library(
    name = "callinvoker",
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("ReactCommon", "*.h"),
        ],
        prefix = "ReactCommon",
    ),
    compiler_flags_pedantic = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX),
    preferred_linkage = "static",
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
)
