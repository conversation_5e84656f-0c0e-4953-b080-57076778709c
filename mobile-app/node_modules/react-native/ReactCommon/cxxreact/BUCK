load("@fbsource//tools/build_defs:glob_defs.bzl", "subdir_glob")
load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "CXX", "get_android_inspector_flags", "get_apple_compiler_flags", "get_apple_inspector_flags", "get_preprocessor_flags_for_build_mode", "react_native_xplat_target", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "module",
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", "CxxModule.h"),
            ("", "JsArgumentHelpers.h"),
            ("", "JsArgumentHelpers-inl.h"),
        ],
        prefix = "cxxreact",
    ),
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = get_apple_compiler_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//xplat/folly:dynamic",
    ],
)

rn_xplat_cxx_library(
    name = "jsbigstring",
    srcs = [
        "JSBigString.cpp",
    ],
    header_namespace = "",
    exported_headers = subdir_glob(
        [("", "JSBigString.h")],
        prefix = "cxxreact",
    ),
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = get_apple_compiler_flags(),
    force_static = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    visibility = [
        "PUBLIC",
    ],
    deps = [
        "//third-party/glog:glog",
        "//xplat/folly:exception",
        "//xplat/folly:memory",
        "//xplat/folly:portability_fcntl",
        "//xplat/folly:portability_sys_mman",
        "//xplat/folly:portability_sys_stat",
        "//xplat/folly:portability_unistd",
        "//xplat/folly:scope_guard",
    ],
)

rn_xplat_cxx_library(
    name = "samplemodule",
    srcs = ["SampleCxxModule.cpp"],
    header_namespace = "",
    exported_headers = ["SampleCxxModule.h"],
    compiler_flags = [
        "-fno-omit-frame-pointer",
    ],
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = get_apple_compiler_flags(),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    soname = "libxplat_react_module_samplemodule.$(ext)",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        ":module",
        "//third-party/glog:glog",
        "//xplat/folly:memory",
    ],
)

CXXREACT_PUBLIC_HEADERS = [
    "CxxNativeModule.h",
    "ErrorUtils.h",
    "Instance.h",
    "JSBundleType.h",
    "JSExecutor.h",
    "JSIndexedRAMBundle.h",
    "JSModulesUnbundle.h",
    "MessageQueueThread.h",
    "MethodCall.h",
    "ModuleRegistry.h",
    "NativeModule.h",
    "NativeToJsBridge.h",
    "RAMBundleRegistry.h",
    "ReactMarker.h",
    "RecoverableError.h",
    "SharedProxyCxxModule.h",
    "SystraceSection.h",
]

rn_xplat_cxx_library(
    name = "bridge",
    srcs = glob(
        ["*.cpp"],
        exclude = [
            "JSBigString.cpp",
            "SampleCxxModule.cpp",
        ],
    ),
    headers = glob(
        ["*.h"],
        exclude = CXXREACT_PUBLIC_HEADERS,
    ),
    header_namespace = "",
    exported_headers = dict([
        (
            "cxxreact/%s" % header,
            header,
        )
        for header in CXXREACT_PUBLIC_HEADERS
    ]),
    compiler_flags_pedantic = True,
    exported_preprocessor_flags = [
        "-DWITH_FBSYSTRACE=1",
    ],
    fbandroid_preprocessor_flags = get_android_inspector_flags(),
    fbobjc_compiler_flags = get_apple_compiler_flags(),
    fbobjc_force_static = True,
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    macosx_tests_override = [],
    platforms = (ANDROID, APPLE, CXX),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
    ],
    tests = [
        react_native_xplat_target("cxxreact/tests:tests"),
    ],
    visibility = ["PUBLIC"],
    deps = [
        ":jsbigstring",
        ":module",
        "//third-party/glog:glog",
        "//xplat/fbsystrace:fbsystrace",
        "//xplat/folly:conv",
        "//xplat/folly:dynamic",
        "//xplat/folly:json",
        "//xplat/folly:memory",
        "//xplat/folly:move_wrapper",
        "//xplat/folly:optional",
        "//xplat/jsi:jsi",
        react_native_xplat_target("callinvoker:callinvoker"),
        react_native_xplat_target("jsinspector:jsinspector"),
        react_native_xplat_target("runtimeexecutor:runtimeexecutor"),
        react_native_xplat_target("reactperflogger:reactperflogger"),
        react_native_xplat_target("logger:logger"),
    ],
)
