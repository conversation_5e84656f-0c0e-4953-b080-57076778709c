load("@fbsource//tools/build_defs:glob_defs.bzl", "subdir_glob")
load("@fbsource//tools/build_defs:platform_defs.bzl", "ANDROID", "APPLE", "CXX", "FBCODE", "WINDOWS")
load("//tools/build_defs/oss:rn_defs.bzl", "get_hermes_shared_library_preprocessor_flags", "rn_xplat_cxx_library")

EXPORTED_HEADERS = [
    "InspectorInterfaces.h",
]

rn_xplat_cxx_library(
    name = "jsinspector",
    srcs = glob(
        ["*.cpp"],
    ),
    headers = subdir_glob(
        [
            ("", "*.h"),
        ],
        exclude = EXPORTED_HEADERS,
        prefix = "jsinspector",
    ),
    header_namespace = "",
    exported_headers = subdir_glob(
        [
            ("", header)
            for header in EXPORTED_HEADERS
        ],
        prefix = "jsinspector",
    ),
    compiler_flags_pedantic = True,
    fbandroid_preferred_linkage = "shared",
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = (ANDROID, APPLE, CXX, FBCODE, WINDOWS),
    preprocessor_flags = get_hermes_shared_library_preprocessor_flags(),
    visibility = [
        "PUBLIC",
    ],
    windows_compiler_flags = ["/WX-"],  # Do not treat all warnings as errors
)
