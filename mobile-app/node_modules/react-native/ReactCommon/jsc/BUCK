load("//tools/build_defs/oss:rn_defs.bzl", "APPLE", "IOS", "MACOSX", "get_apple_compiler_flags", "get_apple_inspector_flags", "get_preprocessor_flags_for_build_mode", "react_native_xplat_dep", "rn_xplat_cxx_library")

APPLE_COMPILER_FLAGS = get_apple_compiler_flags()

rn_xplat_cxx_library(
    name = "JSCRuntime",
    srcs = [
        "JSCRuntime.cpp",
    ],
    header_namespace = "jsc",
    exported_headers = [
        "JSCRuntime.h",
    ],
    apple_sdks = (IOS, MACOSX),
    compiler_flags_pedantic = True,
    fbobjc_compiler_flags = APPLE_COMPILER_FLAGS + [
        "-Os",
    ],
    fbobjc_frameworks = [
        "$SDKROOT/System/Library/Frameworks/JavaScriptCore.framework",
    ],
    fbobjc_preprocessor_flags = get_preprocessor_flags_for_build_mode() + get_apple_inspector_flags(),
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = APPLE,
    visibility = ["PUBLIC"],
    xplat_mangled_args = {
        "soname": "libjscjsi.$(ext)",
    },
    exported_deps = [
        react_native_xplat_dep("jsi:jsi"),
    ],
)
