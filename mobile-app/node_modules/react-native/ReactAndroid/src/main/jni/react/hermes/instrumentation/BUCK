load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "FBJNI_TARGET", "react_native_target", "react_native_xplat_dep", "rn_xplat_cxx_library")

rn_xplat_cxx_library(
    name = "jni_hermes_samplingprofiler",
    srcs = [
        "HermesSamplingProfiler.cpp",
        "OnLoad.cpp",
    ],
    headers = ["HermesSamplingProfiler.h"],
    header_namespace = "",
    compiler_flags_enable_exceptions = True,  # TODO: is this necessary?
    compiler_flags_enable_rtti = True,  # TODO: is this necessary?
    fbandroid_allow_jni_merging = True,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    platforms = ANDROID,
    soname = "libjsijniprofiler.$(ext)",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_target("jni/react/jni:jni"),
        FBJNI_TARGET,
        react_native_xplat_dep("hermes/API:HermesAPIFullOrLean"),
    ],
)
