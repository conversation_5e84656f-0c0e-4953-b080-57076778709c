load("//tools/build_defs/oss:rn_defs.bzl", "YOGA_TARGET", "react_native_dep", "react_native_target", "rn_android_library")

INTERFACES_FILES = [
    "BaseViewManagerDelegate.java",
    "BaseViewManagerInterface.java",
    "ViewManagerDelegate.java",
    "ViewProps.java",
    "Spacing.java",
    "FloatUtil.java",
]

rn_android_library(
    name = "interfaces",
    srcs = glob(INTERFACES_FILES),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        YOGA_TARGET,
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
    ],
)

rn_android_library(
    name = "uimanager",
    srcs = glob(
        [
            "*.java",
            "debug/*.java",
            "events/*.java",
            "layoutanimation/*.java",
        ],
        exclude = [
            "DisplayMetricsHolder.java",
        ] + INTERFACES_FILES,
    ),
    autoglob = False,
    exported_provided_deps = [
        react_native_dep("third-party/android/androidx:annotation"),
    ],
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        ":DisplayMetrics",
        YOGA_TARGET,
        react_native_dep("java/com/facebook/systrace:systrace"),
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_target("java/com/facebook/debug/holder:holder"),
        react_native_target("java/com/facebook/debug/tags:tags"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/common/mapbuffer:mapbuffer"),
        react_native_target("java/com/facebook/react/config:config"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/modules/i18nmanager:i18nmanager"),
        react_native_target("java/com/facebook/react/touch:touch"),
        react_native_target("java/com/facebook/react/uimanager/annotations:annotations"),
        react_native_target("java/com/facebook/react/uimanager/util:util"),
        react_native_target("jni/react/uimanager:jni"),
        react_native_target("res:uimanager"),
    ],
    exported_deps = [
        ":DisplayMetrics",
        ":interfaces",
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/uimanager/common:common"),
    ],
)

rn_android_library(
    name = "DisplayMetrics",
    srcs = [
        "DisplayMetricsHolder.java",
    ],
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
    ],
)
