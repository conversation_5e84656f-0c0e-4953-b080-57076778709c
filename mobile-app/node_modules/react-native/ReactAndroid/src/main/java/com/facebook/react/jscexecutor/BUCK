load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "jscexecutor",
    srcs = glob(["*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_target("jni/react/jscexecutor:jni"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
    ],
)
