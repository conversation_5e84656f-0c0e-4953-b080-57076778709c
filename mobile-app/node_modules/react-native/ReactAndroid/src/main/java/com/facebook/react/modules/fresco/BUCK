load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "fresco",
    srcs = glob(["**/*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("java/com/facebook/systrace:systrace"),
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("libraries/fresco/fresco-react-native:fbcore"),
        react_native_dep("libraries/fresco/fresco-react-native:fresco-drawee"),
        react_native_dep("libraries/fresco/fresco-react-native:fresco-react-native"),
        react_native_dep("libraries/fresco/fresco-react-native:imagepipeline-okhttp3"),
        react_native_dep("libraries/fresco/fresco-react-native:imagepipeline"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/okhttp:okhttp3-urlconnection"),
        react_native_dep("third-party/java/okhttp:okhttp3"),
        react_native_dep("third-party/java/okio:okio"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/modules/network:network"),
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
    ],
    exported_deps = [
        react_native_target("java/com/facebook/react/modules/common:common"),
        react_native_target("java/com/facebook/react/bridge:interfaces"),
    ],
)
