load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_root_target", "react_native_target", "rn_android_library")

rn_android_library(
    name = "devsupport",
    srcs = glob(["*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    manifest = "AndroidManifest.xml",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_dep("third-party/java/okhttp:okhttp3"),
        react_native_dep("third-party/java/okio:okio"),
        react_native_target("java/com/facebook/debug/holder:holder"),
        react_native_target("java/com/facebook/debug/tags:tags"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/common/network:network"),
        react_native_target("java/com/facebook/react/devsupport:interfaces"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/modules/debug:debug"),
        react_native_target("java/com/facebook/react/modules/debug:interfaces"),
        react_native_target("java/com/facebook/react/modules/systeminfo:systeminfo"),
        react_native_target("java/com/facebook/react/packagerconnection:packagerconnection"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
        react_native_target("java/com/facebook/react/util:util"),
        react_native_target("res:devsupport"),
    ],
    exported_deps = [
        react_native_root_target(":FBReactNativeSpec"),
    ],
)

rn_android_library(
    name = "interfaces",
    srcs = glob(["interfaces/*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/modules/debug:interfaces"),
    ],
)
