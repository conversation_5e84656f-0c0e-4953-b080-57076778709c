load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

oncall("react_native")

rn_android_library(
    name = "react",
    srcs = glob(["*.java"]),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("java/com/facebook/hermes/reactexecutor:reactexecutor"),
        react_native_dep("java/com/facebook/systrace:systrace"),
        react_native_dep("libraries/fbcore/src/main/java/com/facebook/common/logging:logging"),
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/android/androidx:annotation"),
        react_native_dep("third-party/android/androidx:appcompat"),
        react_native_dep("third-party/android/androidx:autofill"),
        react_native_dep("third-party/android/androidx:core"),
        react_native_dep("third-party/android/androidx:fragment"),
        react_native_dep("third-party/android/androidx:legacy-support-core-utils"),
        react_native_dep("third-party/java/infer-annotations:infer-annotations"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/debug/holder:holder"),
        react_native_target("java/com/facebook/debug/tags:tags"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("java/com/facebook/react/config:config"),
        react_native_target("java/com/facebook/react/devsupport:devsupport"),
        react_native_target("java/com/facebook/react/devsupport:interfaces"),
        react_native_target("java/com/facebook/react/jscexecutor:jscexecutor"),
        react_native_target("java/com/facebook/react/jstasks:jstasks"),
        react_native_target("java/com/facebook/react/module/annotations:annotations"),
        react_native_target("java/com/facebook/react/module/model:model"),
        react_native_target("java/com/facebook/react/modules/appearance:appearance"),
        react_native_target("java/com/facebook/react/modules/appregistry:appregistry"),
        react_native_target("java/com/facebook/react/modules/bundleloader:bundleloader"),
        react_native_target("java/com/facebook/react/modules/debug:debug"),
        react_native_target("java/com/facebook/react/modules/debug:interfaces"),
        react_native_target("java/com/facebook/react/modules/deviceinfo:deviceinfo"),
        react_native_target("java/com/facebook/react/modules/devtoolssettings:devtoolssettings"),
        react_native_target("java/com/facebook/react/modules/fabric:fabric"),
        react_native_target("java/com/facebook/react/modules/systeminfo:systeminfo"),
        react_native_target("java/com/facebook/react/modules/toast:toast"),
        react_native_target("java/com/facebook/react/surface:surface"),
        react_native_target("java/com/facebook/react/turbomodule/core:core"),
        react_native_target("java/com/facebook/react/turbomodule/core/interfaces:interfaces"),
        react_native_target("java/com/facebook/react/views/imagehelper:imagehelper"),
        react_native_target("java/com/facebook/react/views/traceupdateoverlay:traceupdateoverlay"),
    ],
    exported_deps = [
        react_native_target("java/com/facebook/react/modules/core:core"),
        react_native_target("java/com/facebook/react/packagerconnection:packagerconnection"),
        react_native_target("java/com/facebook/react/uimanager:uimanager"),
    ],
)
