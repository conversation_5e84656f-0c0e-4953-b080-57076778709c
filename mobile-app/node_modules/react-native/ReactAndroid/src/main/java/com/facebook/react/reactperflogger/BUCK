load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "reactperflogger",
    srcs = glob(
        [
            "*.java",
        ],
    ),
    autoglob = False,
    labels = [
        "pfh:ReactNative_CommonInfrastructurePlaceholder",
    ],
    language = "JAVA",
    required_for_source_only_abi = True,
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("libraries/fbjni:java"),
        react_native_target("jni/react/reactperflogger:jni"),
    ],
)
