load("//tools/build_defs/oss:rn_defs.bzl", "react_native_dep", "react_native_target", "rn_android_library")

rn_android_library(
    name = "reactexecutor",
    srcs = [
        "HermesExecutor.java",
        "HermesExecutorFactory.java",
    ],
    autoglob = False,
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("libraries/soloader/java/com/facebook/soloader:soloader"),
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/hermes/instrumentation:instrumentation"),
        react_native_target("java/com/facebook/hermes/instrumentation:hermes_samplingprofiler"),
        react_native_target("java/com/facebook/react/bridge:bridge"),
        react_native_target("java/com/facebook/react/common:common"),
        react_native_target("jni/react/hermes/reactexecutor:jni"),
        ":runtimeconfig",
    ],
)

rn_android_library(
    name = "runtimeconfig",
    srcs = [
        "RuntimeConfig.java",
    ],
    autoglob = False,
    language = "JAVA",
    visibility = [
        "PUBLIC",
    ],
    deps = [
        react_native_dep("third-party/java/jsr-305:jsr-305"),
        react_native_target("java/com/facebook/hermes/instrumentation:instrumentation"),
    ],
)
