load("//tools/build_defs:fb_native_wrapper.bzl", "fb_native")
load("//tools/build_defs/oss:rn_defs.bzl", "rn_android_library", "rn_android_prebuilt_aar", "rn_prebuilt_jar")

rn_android_prebuilt_aar(
    name = "fresco-react-native",
    aar = ":fresco-binary-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "fresco-binary-aar",
    sha1 = "b768459446d166d148aaf3b8edcdcecd59381190",
    url = "mvn:com.facebook.fresco:fresco:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "fresco-drawee",
    aar = ":drawee-binary-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "drawee-binary-aar",
    sha1 = "06971aca0134eafa61c1b81714c0954cb4eb4e10",
    url = "mvn:com.facebook.fresco:drawee:aar:2.5.0",
)

rn_android_library(
    name = "imagepipeline",
    autoglob = False,
    language = "JAVA",
    visibility = ["//packages/react-native/ReactAndroid/..."],
    exported_deps = [
        ":bolts",
        ":imagepipeline-base",
        ":imagepipeline-core",
        ":imagepipeline-native",
        ":memory-type-ashmem",
        ":memory-type-java",
        ":memory-type-native",
        ":native-filters",
        ":native-transcoder",
        ":ui-common",
    ],
)

rn_android_prebuilt_aar(
    name = "imagepipeline-base",
    aar = ":imagepipeline-base-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "imagepipeline-base-aar",
    sha1 = "6839693f5d3b6697cfabbf159d004dae2f465126",
    url = "mvn:com.facebook.fresco:imagepipeline-base:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "imagepipeline-core",
    aar = ":imagepipeline-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "imagepipeline-aar",
    sha1 = "dd62dd1607ade4532c1240f137ac3c2d7920c134",
    url = "mvn:com.facebook.fresco:imagepipeline:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "native-filters",
    aar = ":nativeimagefilters-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "nativeimagefilters-aar",
    sha1 = "4a30438df8d960000b37b1f8cdba9d7996d57ea9",
    url = "mvn:com.facebook.fresco:nativeimagefilters:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "native-transcoder",
    aar = ":nativeimagetranscoder-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "nativeimagetranscoder-aar",
    sha1 = "7db353c68b41e2ad502a959c554387d59e75eb75",
    url = "mvn:com.facebook.fresco:nativeimagetranscoder:aar:2.5.0",
)

rn_prebuilt_jar(
    name = "bolts",
    binary_jar = ":download-bolts.jar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "download-bolts.jar",
    sha1 = "d85884acf6810a3bbbecb587f239005cbc846dc4",
    url = "mvn:com.parse.bolts:bolts-tasks:jar:1.4.0",
)

rn_android_prebuilt_aar(
    name = "fbcore",
    aar = ":fbcore-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "fbcore-aar",
    sha1 = "ffe378b572055e0600377c000d390fb46bf278a5",
    url = "mvn:com.facebook.fresco:fbcore:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "imagepipeline-native",
    aar = ":imagepipeline-native-binary-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "imagepipeline-native-binary-aar",
    sha1 = "f8e65e897a883ac4f754d57897aaba0016c8beba",
    url = "mvn:com.facebook.fresco:imagepipeline-native:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "imagepipeline-okhttp3",
    aar = ":imagepipeline-okhttp3-binary-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "imagepipeline-okhttp3-binary-aar",
    sha1 = "8e274a77ffbe9f8334e09855b2a811b2a63a5708",
    url = "mvn:com.facebook.fresco:imagepipeline-okhttp3:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "memory-type-ashmem",
    aar = ":memory-type-ashmem-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "memory-type-ashmem-aar",
    sha1 = "b43b53aca89569fd2e8e964b71dc7afbf62204e8",
    url = "mvn:com.facebook.fresco:memory-type-ashmem:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "memory-type-java",
    aar = ":memory-type-java-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "memory-type-java-aar",
    sha1 = "57537a8b69dbad44fafdaea3067a776e11586465",
    url = "mvn:com.facebook.fresco:memory-type-java:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "memory-type-native",
    aar = ":memory-type-native-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "memory-type-native-aar",
    sha1 = "7eb7c52ee2a4d40b7f706c90069549410236aaa5",
    url = "mvn:com.facebook.fresco:memory-type-native:aar:2.5.0",
)

rn_android_prebuilt_aar(
    name = "ui-common",
    aar = ":ui-common-aar",
    visibility = ["//packages/react-native/ReactAndroid/..."],
)

fb_native.remote_file(
    name = "ui-common-aar",
    sha1 = "224851cf4c074aeff5dc42861b557870459a9d28",
    url = "mvn:com.facebook.fresco:ui-common:aar:2.5.0",
)
