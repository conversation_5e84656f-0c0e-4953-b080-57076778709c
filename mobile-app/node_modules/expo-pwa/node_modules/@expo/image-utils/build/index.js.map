{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AA2B/B,sBAAK;AA1BP,mCAAyF;AA2BvF,qGA3BO,4BAAoB,OA2BP;AAFpB,qGAzB6B,4BAAoB,OAyB7B;AADpB,mGAxBmD,0BAAkB,OAwBnD;AAtBpB,iCAAkD;AAkBhD,0FAlBsB,gBAAS,OAkBtB;AAjBX,mCAA+E;AAkB7E,uGAlBO,8BAAsB,OAkBP;AACtB,iGAnB+B,wBAAgB,OAmB/B;AAChB,2FApBiD,kBAAU,OAoBjD;AAjBL,KAAK,UAAU,UAAU,CAC9B,OAA2B,EAC3B,WAAkC,EAAE;IAEpC,IAAI,MAAM,IAAA,wBAAgB,GAAE,EAAE;QAC5B,OAAO,IAAA,kBAAU,EAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KACtC;IACD,OAAO,IAAA,gBAAS,EACd,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,IAAA,oBAAa,EAAC,OAAO,CAAC,MAAM,CAAC,EAAE,aAAa,EAAE,OAAO,CAAC,KAAK,EAAE,EACnF,QAAQ,CACT,CAAC;AACJ,CAAC;AAXD,gCAWC", "sourcesContent": ["import * as Cache from './Cache';\nimport { compositeImagesAsync, generateFaviconAsync, generateImageAsync } from './Image';\nimport { ImageFormat, ImageOptions, ResizeMode } from './Image.types';\nimport { convertFormat, jimpAsync } from './jimp';\nimport { findSharpInstanceAsync, isAvailableAsync, sharpAsync } from './sharp';\nimport { SharpCommandOptions, SharpGlobalOptions } from './sharp.types';\n\nexport async function imageAsync(\n  options: SharpGlobalOptions,\n  commands: SharpCommandOptions[] = []\n) {\n  if (await isAvailableAsync()) {\n    return sharpAsync(options, commands);\n  }\n  return jimpAsync(\n    { ...options, format: convertFormat(options.format), originalInput: options.input },\n    commands\n  );\n}\n\nexport {\n  jimpAsync,\n  findSharpInstanceAsync,\n  isAvailableAsync,\n  sharpAsync,\n  generateImageAsync,\n  generateFaviconAsync,\n  Cache,\n  compositeImagesAsync,\n};\n\nexport { SharpGlobalOptions, SharpCommandOptions, ResizeMode, ImageFormat, ImageOptions };\n"]}