{"version": 3, "file": "Image.types.js", "sourceRoot": "", "sources": ["../src/Image.types.ts"], "names": [], "mappings": "", "sourcesContent": ["export type ResizeMode = 'contain' | 'cover' | 'fill' | 'inside' | 'outside';\n\nexport type ImageFormat = 'input' | 'jpeg' | 'jpg' | 'png' | 'raw' | 'tiff' | 'webp';\n\nexport type ImageOptions = {\n  src: string;\n  name?: string;\n  resizeMode: ResizeMode;\n  backgroundColor: string;\n  removeTransparency?: boolean;\n  width: number;\n  height: number;\n  padding?: number;\n  borderRadius?: number;\n};\n"]}