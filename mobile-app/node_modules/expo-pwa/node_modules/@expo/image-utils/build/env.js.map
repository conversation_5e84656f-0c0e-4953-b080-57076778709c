{"version": 3, "file": "env.js", "sourceRoot": "", "sources": ["../src/env.ts"], "names": [], "mappings": ";;;AAAA,mCAAiC;AAEjC,MAAM,GAAG;IACP,oDAAoD;IACpD,IAAI,sBAAsB;QACxB,OAAO,IAAA,gBAAO,EAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,+CAA+C;IAC/C,IAAI,yBAAyB;QAC3B,OAAO,IAAA,gBAAO,EAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;CACF;AAEY,QAAA,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC", "sourcesContent": ["import { boolish } from 'getenv';\n\nclass Env {\n  /** Enable image utils related debugging messages */\n  get EXPO_IMAGE_UTILS_DEBUG() {\n    return boolish('EXPO_IMAGE_UTILS_DEBUG', false);\n  }\n\n  /** Disable all Sharp related functionality. */\n  get EXPO_IMAGE_UTILS_NO_SHARP() {\n    return boolish('EXPO_IMAGE_UTILS_NO_SHARP', false);\n  }\n}\n\nexport const env = new Env();\n"]}