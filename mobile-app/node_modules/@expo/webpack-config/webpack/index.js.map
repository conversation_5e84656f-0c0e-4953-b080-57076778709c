{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;AAAA,4CAAoB;AAGpB,+BAA4C;AAE5C,sEAA6C;AAE7C,SAAS,sBAAsB;IAC7B,OAAO,YAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AACxC,CAAC;AAED;;;;;;GAMG;AACY,KAAK,UAAU,wBAAwB,CACpD,MAAwB,EAAE,EAC1B,OAAkB,EAAE;IAEpB,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;QACpB,GAAG,CAAC,WAAW,GAAG,sBAAsB,EAAE,CAAC;KAC5C;IAED,MAAM,WAAW,GAAgB,IAAA,yBAAmB,EAAC,GAAG,CAAC,CAAC;IAE1D,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAa,EAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAEtD,yBAAyB;IACzB,IAAI,WAAW,CAAC,IAAI,EAAE;QACpB,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;KAChD;IAED,IAAI,SAAS,IAAI,WAAW,EAAE;QAC5B,MAAM,IAAI,KAAK,CACb,gGAAgG,CACjG,CAAC;KACH;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAvBD,2CAuBC", "sourcesContent": ["import fs from 'fs';\nimport { Configuration } from 'webpack';\n\nimport { validateEnvironment } from './env';\nimport { Arguments, Environment, InputEnvironment } from './types';\nimport webpackConfig from './webpack.config';\n\nfunction getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\n/**\n * Create the official Webpack config for loading Expo web apps.\n *\n * @param env Environment props used to configure features.\n * @param argv\n * @category default\n */\nexport default async function createWebpackConfigAsync(\n  env: InputEnvironment = {},\n  argv: Arguments = {}\n): Promise<Configuration> {\n  if (!env.projectRoot) {\n    env.projectRoot = getPossibleProjectRoot();\n  }\n\n  const environment: Environment = validateEnvironment(env);\n\n  const config = await webpackConfig(environment, argv);\n\n  // @ts-ignore: deprecated\n  if (environment.info) {\n    console.warn('environment.info is deprecated');\n  }\n\n  if ('offline' in environment) {\n    throw new Error(\n      'The `offline` flag is deprecated. Please setup a service worker for your web project manually.'\n    );\n  }\n  return config;\n}\n"]}