{"version": 3, "file": "webpack.config.js", "sourceRoot": "", "sources": ["../src/webpack.config.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,+DAA0D;AAC1D,8EAAoD;AACpD,mCAAoC;AACpC,uCAMkB;AAClB,4CAAoB;AACpB,uCAAwC;AACxC,sFAA2D;AAC3D,uDAAyC;AACzC,gDAAwB;AACxB,gEAAuC;AAEvC,qEAAgE;AAEhE,qCAAuE;AACvE,+BAOe;AACf,6CAAsE;AACtE,uCAA6C;AAC7C,uCAUmB;AAEnB,yEAAsE;AAEtE,yCAAuC;AAEvC,SAAS,UAAU,CACjB,EAAE,UAAU,EAAE,WAAW,EAAiD,EAC1E,EAAE,OAAO,EAAgC;IAEzC,IAAI,UAAU,EAAE;QACd,kBAAkB;QAClB,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,8GAA8G;YAC9G,OAAO,OAAO,CAAC;SAChB;QACD,OAAO,6BAAkB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;KAClD;IACD,IAAI,WAAW,EAAE;QACf,OAAO,yBAAyB,CAAC;KAClC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAKD,SAAS,SAAS,CAChB,SAAoB,EACpB,IAAU,EACV,UAAkB,EAClB,QAAiC,EACjC,OAAe,IAAI;IAEnB,MAAM,YAAY,GAAW;QAC3B,0EAA0E;QAC1E,6BAA6B;QAC7B,UAAU;QACV,qCAAqC;QACrC,IAAI,EAAE,SAAS,CAAC,UAAU,CAAC,MAAM;QACjC,mBAAmB,EAAE,iCAAiC;QACtD,wDAAwD;QACxD,+CAA+C;QAC/C,UAAU,EAAE,QAAQ;KACrB,CAAC;IAEF,IAAI,IAAI,KAAK,YAAY,EAAE;QACzB,YAAY,CAAC,QAAQ,GAAG,qCAAqC,CAAC;QAC9D,sEAAsE;QACtE,YAAY,CAAC,aAAa,GAAG,2CAA2C,CAAC;QACzE,+EAA+E;QAC/E,YAAY,CAAC,6BAA6B,GAAG,CAC3C,IAAuC,EAC/B,EAAE,CAAC,cAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC3F;SAAM;QACL,sDAAsD;QACtD,qCAAqC;QACrC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC7B,6DAA6D;QAC7D,mDAAmD;QACnD,YAAY,CAAC,QAAQ,GAAG,qBAAqB,CAAC;QAC9C,sEAAsE;QACtE,YAAY,CAAC,aAAa,GAAG,2BAA2B,CAAC;QACzD,+EAA+E;QAC/E,YAAY,CAAC,6BAA6B,GAAG,CAC3C,IAAuC;QACvC,wBAAwB;UAChB,EAAE,CAAC,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KAC1E;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,sBAAsB,CAAC,QAAgB;IAC9C,OAAO,IAAA,6BAAuB,EAAC,QAAQ,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,qBAAqB,CAAC,GAAuB;IACpD,OAAO,IAAA,mBAAU,EAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrE,CAAC;AAEc,KAAK,oBAAW,GAAgB,EAAE,OAAkB,EAAE;;IACnE,IAAI,QAAQ,IAAI,GAAG,EAAE;QACnB,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,KAAK,CAAC,KAAK,CACf,gIAAgI,CACjI,CACF,CAAC;KACH;IACD,MAAM,MAAM,GAAG,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,IAAI,GAAG,IAAA,aAAO,EAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,KAAK,GAAG,IAAI,KAAK,aAAa,CAAC;IACrC,MAAM,MAAM,GAAG,IAAI,KAAK,YAAY,CAAC;IAErC,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,CAAC,MAAM,IAAA,mBAAa,EAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAE1E,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,IAAA,oBAAc,EAAC,GAAG,CAAC,CAAC;IAEtD,MAAM,EAAE,KAAK,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC;IAErD,MAAM,OAAO,GAAG,UAAU,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,WAAW,CAAC,CAAC;IAEpF,MAAM,QAAQ,GAAa,EAAE,CAAC;IAE9B,0EAA0E;IAC1E,IAAI,SAAS,CAAC,OAAO,EAAE;QACrB,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAClC;IAED,8EAA8E;IAC9E,MAAM,sBAAsB,GAAG,sBAAW,CAAC,MAAM,CAC/C,GAAG,CAAC,WAAW,EACf,qDAAqD,CACtD,CAAC;IACF,IAAI,sBAAsB,EAAE;QAC1B,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;KAC1C;IAED,IAAI,sBAAsB,GAAY,CAAC,KAAK,CAAC;IAC7C,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,CAAC,GAAG,KAAK,WAAW,EAAE;QAC5C,sBAAsB,GAAG,GAAG,CAAC,GAAG,CAAC;KAClC;IAED,MAAM,WAAW,GAAU;QACzB;YACE,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;YAC/B,EAAE,EAAE,SAAS,CAAC,UAAU,CAAC,MAAM;YAC/B,MAAM,EAAE,KAAK;YACb,gBAAgB,EAAE,IAAI;YACtB,WAAW,EAAE;gBACX,GAAG,EAAE,IAAI;gBACT,2DAA2D;gBAC3D,MAAM,EAAE;oBACN,mBAAmB;oBACnB,eAAe;oBACf,aAAa;iBACd;aACF;SACF;QACD;YACE,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS;YAClC,EAAE,EAAE,SAAS,CAAC,UAAU,CAAC,SAAS;SACnC;KACF,CAAC;IAEF,MAAM,aAAa,GAAG,IAAA,wBAAK,EAAC,IAAA,uBAAY,EAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAE9F,aAAa;IACb,MAAM,aAAa,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC7D,MAAM,KAAK,GAAmB,aAAa,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;QAC/D,GAAG,EAAE,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC;QAC9B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;QAClC,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;QAChC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC;QAClC,IAAI,EAAE,KAAK;KACZ,CAAC,CAAC,CAAC;IACJ,aAAa;IACb,MAAM,YAAY,GAAG,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC5D,MAAM,IAAI,GAAmB,YAAY,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;QAC7D,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC;QAChC,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC;QACtC,IAAI,EAAE,KAAK;KACZ,CAAC,CAAC,CAAC;IAEJ,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,MAAM,CACjC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CACjF,CAAC;IACF,IAAI,gBAAgB,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC;IACnD,6DAA6D;IAC7D,+CAA+C;IAC/C,MAAM,uBAAuB,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,EAAE;QACtB,uFAAuF;QACvF,8GAA8G;QAC9G,mGAAmG;QACnG,EAAE;QACF,gHAAgH;QAChH,uEAAuE;QACvE,IAAI,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACrC,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACpD;QACD,gBAAgB,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;KAC9D;IAED,MAAM,oBAAoB,GAAG,CAAC,KAAyB,EAAsB,EAAE;QAC7E,IAAI,CAAC,KAAK;YAAE,OAAO,KAAK,CAAC;QACzB,OAAO;YACL,GAAG,KAAK;YACR,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC;SACnC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,QAAQ,CAAC;IAEtC,MAAM,UAAU,GAAG,IAAA,0BAAgB,EAAC,GAAG,CAAC,CAAC;IACzC,IAAI,aAAa,GAAkB;QACjC,iCAAiC;QACjC,IAAI,EAAE,GAAG,CAAC,QAAQ;QAElB,MAAM,EAAE,CAAC,KAAK,CAAC;QAEf,YAAY,EAAE;YACZ,gBAAgB,EAAE,CAAC;YACnB,OAAO,EAAE;gBACP,YAAY;gBACZ,oBAAoB;gBACpB,aAAa;gBACb,oBAAoB;gBACpB,iBAAiB;gBACjB,oFAAoF;gBACpF,oBAAoB;aACrB;SACF;QACD,IAAI;QACJ,KAAK,EAAE,QAAQ;QAEf,2DAA2D;QAC3D,wDAAwD;QACxD,IAAI,EAAE,MAAM;QACZ,OAAO;QACP,+FAA+F;QAC/F,iFAAiF;QACjF,OAAO,EAAE,MAAA,GAAG,CAAC,WAAW,mCAAI,SAAS;QACrC,qCAAqC;QACrC,MAAM,EAAE,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC;QAEtE,0BAA0B;QAC1B,KAAK,EAAE,qBAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB;QAElD,KAAK,EAAE;YACL,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,qBAAqB,CAAC,OAAO,CAAC,GAAG,CAAC;YAC3C,cAAc,EAAE,cAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,GAAG,CAAC,QAAQ,CAAC;YACtE,KAAK,EAAE,MAAM;YACb,iBAAiB,EAAE;gBACjB,cAAc,EAAE,CAAC,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;gBAC1F,MAAM,EAAE,CAAC,UAAU,CAAC;gBACpB,QAAQ,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC1E,YAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CACjB;aACF;SACF;QACD,qBAAqB,EAAE;YACrB,KAAK,EAAE,qBAAU;YACjB,KAAK,EAAE,qBAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM;SACvC;QAED,OAAO,EAAE;YACP,0BAA0B;YAC1B,MAAM;gBACJ,IAAI,yCAAkB,CAAC;oBACrB,2CAA2C,EAAE,IAAI;oBACjD,4BAA4B,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC;oBAC3D,GAAG,EAAE,KAAK;oBACV,OAAO,EAAE,KAAK;iBACf,CAAC;YACJ,+BAA+B;YAC/B,MAAM,IAAI,IAAI,6BAAiB,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;YAE1D,4BAA4B;YAC5B,IAAI,+BAAqB,CAAC,GAAG,EAAE,aAAa,CAAC;YAE7C,mCAAyB,CAAC,OAAO,CAAC,GAAG,EAAE,+BAAqB,CAAC;YAE7D,GAAG,CAAC,GAAG;gBACL,IAAI,sCAA4B,CAC9B;oBACE,QAAQ,EAAE,gBAAgB;oBAC1B,IAAI,EAAE,eAAe;oBACrB,UAAU;oBACV,MAAM,EAAE,uBAAuB;iBAChC,EACD,MAAM,CACP;YAEH,IAAI,8BAAoB,CACtB;gBACE,WAAW,EAAE,GAAG,CAAC,WAAW;gBAC5B,UAAU;gBACV,KAAK;aACN,EACD,oBAAoB,CAAC,IAAA,+BAAoB,EAAC,MAAM,CAAC,CAAC,CACnD;YACD,sBAAsB;gBACpB,IAAI,+BAAqB,CACvB;oBACE,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,UAAU;oBACV,KAAK;oBACL,IAAI;iBACL,EACD;oBACE,IAAI,EAAE,MAAA,GAAG,CAAC,MAAM,CAAC,GAAG,0CAAE,SAAS;oBAC/B,YAAY,EAAE,MAAA,MAAA,MAAA,GAAG,CAAC,MAAM,CAAC,GAAG,0CAAE,IAAI,0CAAE,KAAK,0CAAE,eAAe;oBAC1D,eAAe,EAAE,CAAC,CAAC,CAAA,MAAA,MAAA,MAAA,GAAG,CAAC,MAAM,CAAC,GAAG,0CAAE,IAAI,0CAAE,KAAK,0CAAE,mBAAmB,CAAA;oBACnE,QAAQ,EAAE,MAAA,MAAA,MAAA,GAAG,CAAC,MAAM,CAAC,GAAG,0CAAE,IAAI,0CAAE,KAAK,0CAAE,QAAQ;iBAChD,EACD,oBAAoB,CAAC,IAAA,8BAAmB,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EACrD,oBAAoB,CAAC,IAAA,sCAA2B,EAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAC9D;YACH,sBAAsB;gBACpB,IAAI,kCAAwB,CAC1B;oBACE,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,UAAU;iBACX,EACD,oBAAoB,CAAC,IAAA,8BAAmB,EAAC,MAAM,CAAC,CAAC,CAClD;YAEH,wEAAwE;YACxE,2BAA2B;YAC3B,IAAI,2CAAoB,CAAC,SAAS,CAAC,IAAI,CAAC;YAExC,IAAI,0BAAgB,CAAC;gBACnB,IAAI;gBACJ,SAAS;gBACT,MAAM;gBACN,aAAa;gBACb,QAAQ,EAAE,GAAG,CAAC,QAAQ;aACvB,CAAC;YAEF,MAAM;gBACJ,IAAI,iCAAoB,CAAC;oBACvB,+DAA+D;oBAC/D,4BAA4B;oBAC5B,QAAQ,EAAE,uCAAuC;oBACjD,aAAa,EAAE,6CAA6C;iBAC7D,CAAC;YAEJ,8DAA8D;YAC9D,uEAAuE;YACvE,qEAAqE;YACrE,iBAAiB;YACjB,0EAA0E;YAC1E,qDAAqD;YAErD,IAAI,+CAAqB,CAAC;gBACxB,QAAQ,EAAE,qBAAqB;gBAC/B,UAAU;gBACV,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;oBACnB,IACE,IAAI,CAAC,KAAK,CAAC,4EAA4E,CAAC,EACxF;wBACA,OAAO,KAAK,CAAC;qBACd;oBACD,iDAAiD;oBACjD,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC/D,CAAC;gBACD,QAAQ,EAAE,CAAC,IAAyB,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE;oBAC1D,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAyB,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE;wBAC5E,IAAI,IAAI,CAAC,IAAI,EAAE;4BACb,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;yBACjC;wBACD,OAAO,QAAQ,CAAC;oBAClB,CAAC,EAAE,IAAI,CAAC,CAAC;oBACT,MAAM,eAAe,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;oBAExF,OAAO;wBACL,KAAK,EAAE,aAAa;wBACpB,WAAW,EAAE,eAAe;qBAC7B,CAAC;gBACJ,CAAC;aACF,CAAC;YAEF,aAAa;YACb,IAAI,8BAAoB,EAAE;YAE1B,kCAAkC;YAClC,GAAG,CAAC,MAAM;gBACR,IAAI,+BAAqB,CAAC;oBACxB,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,cAAc,EAAE,eAAI;oBACpB,aAAa,EAAE;wBACb,UAAU,EAAE,QAAQ;wBACpB,QAAQ,EAAE,GAAG,CAAC,QAAQ;wBACtB,SAAS,EAAE,SAAS,CAAC,OAAO;wBAC5B,GAAG,EAAE,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,KAAK;wBACnB,MAAM,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,KAAK;qBACxB;iBACF,CAAC;SACL,CAAC,MAAM,CAAC,cAAM,CAAC;QAEhB,MAAM,EAAE;YACN,oBAAoB,EAAE,KAAK;YAC3B,aAAa;YACb,KAAK,EAAE;gBACL,uDAAuD;gBACvD,6BAAkB,IAAI;oBACpB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,6BAA6B;oBACtC,IAAI,EAAE,4BAA4B;oBAClC,GAAG,EAAE;wBACH;4BACE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC;4BAC5C,OAAO,EAAE;gCACP,sBAAsB,CAAC,GAAW,EAAE,YAAoB;oCACtD,iDAAiD;oCACjD,IAAI,YAAY,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE;wCAC3C,OAAO,QAAQ,CAAC;qCACjB;oCACD,OAAO,IAAI,CAAC;gCACd,CAAC;6BACF;yBACF;qBACF;oBAED,OAAO,EAAE;wBACP,cAAc,EAAE,KAAK;qBACtB;iBACF;gBACD;oBACE,KAAK,EAAE,UAAU;iBAClB;aACF,CAAC,MAAM,CAAC,cAAM,CAAC;SACjB;QACD,OAAO,EAAE;YACP,6BAA6B;YAC7B,UAAU,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;YACzC,WAAW,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;YAC1C,UAAU,EAAE,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC;YAChD,QAAQ,EAAE,KAAK;SAChB;QACD,qDAAqD;QACrD,+CAA+C;QAE/C,wCAAwC;QACxC,WAAW,EAAE,eAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE;KAChF,CAAC;IAEF,IAAI,MAAM,EAAE;QACV,aAAa,GAAG,IAAA,0BAAiB,EAAC,aAAa,CAAC,CAAC;KAClD;SAAM;QACL,aAAa,GAAG,IAAA,sBAAa,EAAC,aAAa,EAAE,GAAG,EAAE;YAChD,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAC;KACJ;IAED,aAAa,GAAG,IAAA,kBAAS,EAAC,aAAa,EAAE,IAAA,gBAAU,EAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;IAEtE,OAAO,aAAa,CAAC;AACvB,CAAC;AAxWD,4BAwWC", "sourcesContent": ["import chalk from 'chalk';\nimport { CleanWebpackPlugin } from 'clean-webpack-plugin';\nimport CopyWebpackPlugin from 'copy-webpack-plugin';\nimport { createHash } from 'crypto';\nimport {\n  getChromeIconConfig,\n  getFaviconIconConfig,\n  getSafariIconConfig,\n  getSafariStartupImageConfig,\n  IconOptions,\n} from 'expo-pwa';\nimport fs from 'fs';\nimport { readFileSync } from 'fs-extra';\nimport MiniCssExtractPlugin from 'mini-css-extract-plugin';\nimport { parse } from 'node-html-parser';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { Configuration } from 'webpack';\nimport { WebpackManifestPlugin } from 'webpack-manifest-plugin';\n\nimport { withAlias, withDevServer, withOptimizations } from './addons';\nimport {\n  getAliases,\n  getConfig,\n  getMode,\n  getModuleFileExtensions,\n  getPathsAsync,\n  getPublicPaths,\n} from './env';\nimport { EXPO_DEBUG, isCI, shouldUseSourceMap } from './env/defaults';\nimport { createAllLoaders } from './loaders';\nimport {\n  ApplePwaWebpackPlugin,\n  ChromeIconsWebpackPlugin,\n  ExpectedErrorsPlugin,\n  ExpoDefinePlugin,\n  ExpoHtmlWebpackPlugin,\n  ExpoInterpolateHtmlPlugin,\n  ExpoProgressBarPlugin,\n  ExpoPwaManifestWebpackPlugin,\n  FaviconWebpackPlugin,\n} from './plugins';\nimport { HTMLLinkNode } from './plugins/ModifyHtmlWebpackPlugin';\nimport { ModuleNotFoundPlugin } from './plugins/ModuleNotFoundPlugin';\nimport { Arguments, Environment, FilePaths, Mode } from './types';\nimport { truthy } from './utils/array';\n\nfunction getDevtool(\n  { production, development }: { production: boolean; development: boolean },\n  { devtool }: { devtool?: string | false }\n): string | false {\n  if (production) {\n    // string or false\n    if (devtool !== undefined) {\n      // When big assets are involved sources maps can become expensive and cause your process to run out of memory.\n      return devtool;\n    }\n    return shouldUseSourceMap ? 'source-map' : false;\n  }\n  if (development) {\n    return 'cheap-module-source-map';\n  }\n  return false;\n}\n\ntype Output = Configuration['output'];\ntype DevtoolModuleFilenameTemplateInfo = { root: string; absoluteResourcePath: string };\n\nfunction getOutput(\n  locations: FilePaths,\n  mode: Mode,\n  publicPath: string,\n  platform: Environment['platform'],\n  port: number = 8081\n): Output {\n  const commonOutput: Output = {\n    // We inferred the \"public path\" (such as / or /my-project) from homepage.\n    // We use \"/\" in development.\n    publicPath,\n    // Build folder (default `web-build`)\n    path: locations.production.folder,\n    assetModuleFilename: 'static/media/[name].[hash][ext]',\n    // Prevent chunk naming collision across platforms since\n    // they're all coming from the same dev server.\n    uniqueName: platform,\n  };\n\n  if (mode === 'production') {\n    commonOutput.filename = 'static/js/[name].[contenthash:8].js';\n    // There are also additional JS chunk files if you use code splitting.\n    commonOutput.chunkFilename = 'static/js/[name].[contenthash:8].chunk.js';\n    // Point sourcemap entries to original disk location (format as URL on Windows)\n    commonOutput.devtoolModuleFilenameTemplate = (\n      info: DevtoolModuleFilenameTemplateInfo\n    ): string => path.relative(locations.root, info.absoluteResourcePath).replace(/\\\\/g, '/');\n  } else {\n    // Add comments that describe the file import/exports.\n    // This will make it easier to debug.\n    commonOutput.pathinfo = true;\n    // Give the output bundle a constant name to prevent caching.\n    // Also there are no actual files generated in dev.\n    commonOutput.filename = 'static/js/bundle.js';\n    // There are also additional JS chunk files if you use code splitting.\n    commonOutput.chunkFilename = 'static/js/[name].chunk.js';\n    // Point sourcemap entries to original disk location (format as URL on Windows)\n    commonOutput.devtoolModuleFilenameTemplate = (\n      info: DevtoolModuleFilenameTemplateInfo\n      // TODO: revisit for web\n    ): string => path.resolve(info.absoluteResourcePath).replace(/\\\\/g, '/');\n  }\n\n  return commonOutput;\n}\n\nfunction getPlatformsExtensions(platform: string): string[] {\n  return getModuleFileExtensions(platform);\n}\n\nfunction createEnvironmentHash(env: typeof process.env) {\n  return createHash('md5').update(JSON.stringify(env)).digest('hex');\n}\n\nexport default async function (env: Environment, argv: Arguments = {}): Promise<Configuration> {\n  if ('report' in env) {\n    console.warn(\n      chalk.bgRed.black(\n        `The \\`report\\` property of \\`@expo/webpack-config\\` is now deprecated.\\nhttps://expo.fyi/webpack-report-property-is-deprecated`\n      )\n    );\n  }\n  const config = getConfig(env);\n  const mode = getMode(env);\n  const isDev = mode === 'development';\n  const isProd = mode === 'production';\n\n  const locations = env.locations || (await getPathsAsync(env.projectRoot));\n\n  const { publicPath, publicUrl } = getPublicPaths(env);\n\n  const { build: buildConfig = {} } = config.web || {};\n\n  const devtool = getDevtool({ production: isProd, development: isDev }, buildConfig);\n\n  const appEntry: string[] = [];\n\n  // In solutions like Gatsby the main entry point doesn't need to be known.\n  if (locations.appMain) {\n    appEntry.push(locations.appMain);\n  }\n\n  // Add a loose requirement on the ResizeObserver polyfill if it's installed...\n  const resizeObserverPolyfill = resolveFrom.silent(\n    env.projectRoot,\n    'resize-observer-polyfill/dist/ResizeObserver.global'\n  );\n  if (resizeObserverPolyfill) {\n    appEntry.unshift(resizeObserverPolyfill);\n  }\n\n  let generatePWAImageAssets: boolean = !isDev;\n  if (!isDev && typeof env.pwa !== 'undefined') {\n    generatePWAImageAssets = env.pwa;\n  }\n\n  const filesToCopy: any[] = [\n    {\n      from: locations.template.folder,\n      to: locations.production.folder,\n      toType: 'dir',\n      noErrorOnMissing: true,\n      globOptions: {\n        dot: true,\n        // We generate new versions of these based on the templates\n        ignore: [\n          // '**/serve.json',\n          '**/index.html',\n          '**/icon.png',\n        ],\n      },\n    },\n    {\n      from: locations.template.serveJson,\n      to: locations.production.serveJson,\n    },\n  ];\n\n  const templateIndex = parse(readFileSync(locations.template.indexHtml, { encoding: 'utf8' }));\n\n  // @ts-ignore\n  const templateLinks = templateIndex.querySelectorAll('link');\n  const links: HTMLLinkNode[] = templateLinks.map((value: any) => ({\n    rel: value.getAttribute('rel'),\n    media: value.getAttribute('media'),\n    href: value.getAttribute('href'),\n    sizes: value.getAttribute('sizes'),\n    node: value,\n  }));\n  // @ts-ignore\n  const templateMeta = templateIndex.querySelectorAll('meta');\n  const meta: HTMLLinkNode[] = templateMeta.map((value: any) => ({\n    name: value.getAttribute('name'),\n    content: value.getAttribute('content'),\n    node: value,\n  }));\n\n  const [manifestLink] = links.filter(\n    link => typeof link.rel === 'string' && link.rel.split(' ').includes('manifest')\n  );\n  let templateManifest = locations.template.manifest;\n  // Only inject a manifest tag if the template is missing one.\n  // This enables users to disable tag injection.\n  const shouldInjectManifestTag = !manifestLink;\n  if (manifestLink?.href) {\n    // Often the manifest will be referenced as `/manifest.json` (to support public paths).\n    // We've detected that the user has manually added a manifest tag to their template index.html which according\n    // to our docs means they want to use a custom manifest.json instead of having a new one generated.\n    //\n    // Normalize the link (removing the beginning slash) so it can be resolved relative to the user's static folder.\n    // Ref: https://docs.expo.dev/guides/progressive-web-apps/#manual-setup\n    if (manifestLink.href.startsWith('/')) {\n      manifestLink.href = manifestLink.href.substring(1);\n    }\n    templateManifest = locations.template.get(manifestLink.href);\n  }\n\n  const ensureSourceAbsolute = (input: IconOptions | null): IconOptions | null => {\n    if (!input) return input;\n    return {\n      ...input,\n      src: locations.absolute(input.src),\n    };\n  };\n\n  const emacsLockfilePattern = '**/.#*';\n\n  const allLoaders = createAllLoaders(env);\n  let webpackConfig: Configuration = {\n    // Used to identify the compiler.\n    name: env.platform,\n\n    target: ['web'],\n\n    watchOptions: {\n      aggregateTimeout: 5,\n      ignored: [\n        '**/.git/**',\n        '**/node_modules/**',\n        '**/.expo/**',\n        '**/.expo-shared/**',\n        '**/web-build/**',\n        // can be removed after https://github.com/paulmillr/chokidar/issues/955 is released\n        emacsLockfilePattern,\n      ],\n    },\n    mode,\n    entry: appEntry,\n\n    // https://webpack.js.org/configuration/other-options/#bail\n    // Fail out on the first error instead of tolerating it.\n    bail: isProd,\n    devtool,\n    // This must point to the project root (where the webpack.config.js would normally be located).\n    // If this is anywhere else, the source maps and errors won't show correct paths.\n    context: env.projectRoot ?? __dirname,\n    // configures where the build ends up\n    output: getOutput(locations, mode, publicPath, env.platform, env.port),\n\n    // Disable file info logs.\n    stats: EXPO_DEBUG ? 'detailed' : 'errors-warnings',\n\n    cache: {\n      type: 'filesystem',\n      version: createEnvironmentHash(process.env),\n      cacheDirectory: path.join(env.locations.appWebpackCache, env.platform),\n      store: 'pack',\n      buildDependencies: {\n        defaultWebpack: [path.join(path.dirname(require.resolve('webpack/package.json')), 'lib/')],\n        config: [__filename],\n        tsconfig: [env.locations.appTsConfig, env.locations.appJsConfig].filter(f =>\n          fs.existsSync(f)\n        ),\n      },\n    },\n    infrastructureLogging: {\n      debug: EXPO_DEBUG,\n      level: EXPO_DEBUG ? 'verbose' : 'none',\n    },\n\n    plugins: [\n      // Delete the build folder\n      isProd &&\n        new CleanWebpackPlugin({\n          dangerouslyAllowCleanPatternsOutsideProject: true,\n          cleanOnceBeforeBuildPatterns: [locations.production.folder],\n          dry: false,\n          verbose: false,\n        }),\n      // Copy the template files over\n      isProd && new CopyWebpackPlugin({ patterns: filesToCopy }),\n\n      // Generate the `index.html`\n      new ExpoHtmlWebpackPlugin(env, templateIndex),\n\n      ExpoInterpolateHtmlPlugin.fromEnv(env, ExpoHtmlWebpackPlugin),\n\n      env.pwa &&\n        new ExpoPwaManifestWebpackPlugin(\n          {\n            template: templateManifest,\n            path: 'manifest.json',\n            publicPath,\n            inject: shouldInjectManifestTag,\n          },\n          config\n        ),\n\n      new FaviconWebpackPlugin(\n        {\n          projectRoot: env.projectRoot,\n          publicPath,\n          links,\n        },\n        ensureSourceAbsolute(getFaviconIconConfig(config))\n      ),\n      generatePWAImageAssets &&\n        new ApplePwaWebpackPlugin(\n          {\n            projectRoot: env.projectRoot,\n            publicPath,\n            links,\n            meta,\n          },\n          {\n            name: env.config.web?.shortName,\n            isFullScreen: env.config.web?.meta?.apple?.touchFullscreen,\n            isWebAppCapable: !!env.config.web?.meta?.apple?.mobileWebAppCapable,\n            barStyle: env.config.web?.meta?.apple?.barStyle,\n          },\n          ensureSourceAbsolute(getSafariIconConfig(env.config)),\n          ensureSourceAbsolute(getSafariStartupImageConfig(env.config))\n        ),\n      generatePWAImageAssets &&\n        new ChromeIconsWebpackPlugin(\n          {\n            projectRoot: env.projectRoot,\n            publicPath,\n          },\n          ensureSourceAbsolute(getChromeIconConfig(config))\n        ),\n\n      // This gives some necessary context to module not found errors, such as\n      // the requesting resource.\n      new ModuleNotFoundPlugin(locations.root),\n\n      new ExpoDefinePlugin({\n        mode,\n        publicUrl,\n        config,\n        // @ts-ignore\n        platform: env.platform,\n      }),\n\n      isProd &&\n        new MiniCssExtractPlugin({\n          // Options similar to the same options in webpackOptions.output\n          // both options are optional\n          filename: 'static/css/[name].[contenthash:8].css',\n          chunkFilename: 'static/css/[name].[contenthash:8].chunk.css',\n        }),\n\n      // Generate an asset manifest file with the following content:\n      // - \"files\" key: Mapping of all asset filenames to their corresponding\n      //   output file so that tools can pick it up without having to parse\n      //   `index.html`\n      // - \"entrypoints\" key: Array of files which are included in `index.html`,\n      //   can be used to reconstruct the HTML if necessary\n\n      new WebpackManifestPlugin({\n        fileName: 'asset-manifest.json',\n        publicPath,\n        filter: ({ path }) => {\n          if (\n            path.match(/(apple-touch-startup-image|apple-touch-icon|chrome-icon|precache-manifest)/)\n          ) {\n            return false;\n          }\n          // Remove compressed versions and service workers\n          return !(path.endsWith('.gz') || path.endsWith('worker.js'));\n        },\n        generate: (seed: Record<string, any>, files, entrypoints) => {\n          const manifestFiles = files.reduce<Record<string, string>>((manifest, file) => {\n            if (file.name) {\n              manifest[file.name] = file.path;\n            }\n            return manifest;\n          }, seed);\n          const entrypointFiles = entrypoints.main.filter(fileName => !fileName.endsWith('.map'));\n\n          return {\n            files: manifestFiles,\n            entrypoints: entrypointFiles,\n          };\n        },\n      }),\n\n      // TODO: Drop\n      new ExpectedErrorsPlugin(),\n\n      // Skip using a progress bar in CI\n      env.logger &&\n        new ExpoProgressBarPlugin({\n          logger: env.logger,\n          nonInteractive: isCI,\n          bundleDetails: {\n            bundleType: 'bundle',\n            platform: env.platform,\n            entryFile: locations.appMain,\n            dev: isDev ?? false,\n            minify: isProd ?? false,\n          },\n        }),\n    ].filter(truthy),\n\n    module: {\n      strictExportPresence: false,\n      // @ts-ignore\n      rules: [\n        // Handle node_modules packages that contain sourcemaps\n        shouldUseSourceMap && {\n          enforce: 'pre',\n          exclude: /@babel(?:\\/|\\\\{1,2})runtime/,\n          test: /\\.(js|mjs|jsx|ts|tsx|css)$/,\n          use: [\n            {\n              loader: require.resolve('source-map-loader'),\n              options: {\n                filterSourceMappingUrl(url: string, resourcePath: string) {\n                  // https://github.com/alewin/useWorker/issues/138\n                  if (resourcePath.match(/@koale\\/useworker/)) {\n                    return 'remove';\n                  }\n                  return true;\n                },\n              },\n            },\n          ],\n\n          resolve: {\n            fullySpecified: false,\n          },\n        },\n        {\n          oneOf: allLoaders,\n        },\n      ].filter(truthy),\n    },\n    resolve: {\n      // modules: ['node_modules'],\n      mainFields: ['browser', 'module', 'main'],\n      aliasFields: ['browser', 'module', 'main'],\n      extensions: getPlatformsExtensions(env.platform),\n      symlinks: false,\n    },\n    // Turn off performance processing because we utilize\n    // our own (CRA) hints via the FileSizeReporter\n\n    // TODO: Bacon: Remove this higher value\n    performance: isCI ? false : { maxAssetSize: 600000, maxEntrypointSize: 600000 },\n  };\n\n  if (isProd) {\n    webpackConfig = withOptimizations(webpackConfig);\n  } else {\n    webpackConfig = withDevServer(webpackConfig, env, {\n      allowedHost: argv.allowedHost,\n      proxy: argv.proxy,\n    });\n  }\n\n  webpackConfig = withAlias(webpackConfig, getAliases(env.projectRoot));\n\n  return webpackConfig;\n}\n"]}