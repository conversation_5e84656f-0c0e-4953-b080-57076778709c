{"version": 3, "file": "ignoredFiles.js", "sourceRoot": "", "sources": ["../../src/utils/ignoredFiles.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;GASG;AACH,gDAAwB;AAExB,6DAA0D;AAE1D,SAAgB,YAAY,CAAC,MAAc;IACzC,OAAO,IAAI,MAAM,CACf,OAAO,IAAA,uCAAkB,EACvB,cAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CACpD,mBAAmB,EACpB,GAAG,CACJ,CAAC;AACJ,CAAC;AAPD,oCAOC", "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/a422bf2/packages/react-dev-utils/ignoredFiles.js\n * But with Node LTS support.\n */\nimport path from 'path';\n\nimport { escapeStringRegexp } from './escapeStringRegexp';\n\nexport function ignoredFiles(appSrc: string) {\n  return new RegExp(\n    `^(?!${escapeStringRegexp(\n      path.normalize(appSrc + '/').replace(/[\\\\]+/g, '/')\n    )}).+/node_modules/`,\n    'g'\n  );\n}\n"]}