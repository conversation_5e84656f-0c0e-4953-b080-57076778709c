{"version": 3, "file": "redirectServedPathMiddleware.js", "sourceRoot": "", "sources": ["../../src/utils/redirectServedPathMiddleware.ts"], "names": [], "mappings": ";;;;;;AAWA,gDAAwB;AAExB,SAAgB,kCAAkC,CAAC,UAAkB;IACnE,mEAAmE;IACnE,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACrC,OAAO,SAAS,4BAA4B,CAC1C,GAAoB,EACpB,GAAqB,EACrB,IAA0B;QAE1B,IAAI,UAAU,KAAK,EAAE,IAAI,GAAG,CAAC,GAAG,KAAK,UAAU,IAAI,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YACjF,IAAI,EAAE,CAAC;SACR;aAAM;YACL,MAAM,OAAO,GAAG,cAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9E,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SACvB;IACH,CAAC,CAAC;AACJ,CAAC;AAfD,gFAeC", "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/a422bf2/packages/react-dev-utils/redirectServedPathMiddleware.js\n * But with Node LTS support.\n */\nimport type express from 'express';\nimport path from 'path';\n\nexport function createRedirectServedPathMiddleware(servedPath: string) {\n  // remove end slash so user can land on `/test` instead of `/test/`\n  servedPath = servedPath.slice(0, -1);\n  return function redirectServedPathMiddleware(\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) {\n    if (servedPath === '' || req.url === servedPath || req.url.startsWith(servedPath)) {\n      next();\n    } else {\n      const newPath = path.posix.join(servedPath, req.path !== '/' ? req.path : '');\n      res.redirect(newPath);\n    }\n  };\n}\n"]}