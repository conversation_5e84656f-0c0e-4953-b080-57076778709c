{"version": 3, "file": "evalSourceMapMiddleware.js", "sourceRoot": "", "sources": ["../../src/utils/evalSourceMapMiddleware.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;AAMH,SAAS,eAAe,CAAC,MAAW;IAClC,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACpF,OAAO,8CAA8C,MAAM,EAAE,CAAC;AAChE,CAAC;AAED,SAAS,aAAa,CAAC,MAA8B,EAAE,EAAU;IAC/D,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,CAC/D,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CAC5C,CAAC;IAEtB,aAAa;IACb,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,6BAA6B,CAAC,MAAwB;IACpE,OAAO,SAAS,+BAA+B,CAC7C,GAAoB,EACpB,GAAqB,EACrB,IAA0B;;QAE1B,IAAI,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE;YAChD,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC;YACpC,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAChC,OAAO,IAAI,EAAE,CAAC;aACf;YACD,MAAM,EAAE,GAAG,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,6BAA6B,CAAC,0CAAG,CAAC,CAAC,CAAC;YAC/D,sBAAsB;YACtB,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACzB,OAAO,IAAI,EAAE,CAAC;aACf;YAED,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YACzC,MAAM,YAAY,GAAG,wBAAwB,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YACvE,MAAM,SAAS,GAAG,qCAAqC,MAAM,CAAC,EAAE,EAAE,CAAC;YACnE,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,YAAY,KAAK,SAAS,EAAE,CAAC,CAAC;SAC9D;aAAM;YACL,OAAO,IAAI,EAAE,CAAC;SACf;IACH,CAAC,CAAC;AACJ,CAAC;AAzBD,sEAyBC", "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/a422bf2/packages/react-dev-utils/evalSourceMapMiddleware.js\n * But with Node LTS support.\n */\n\nimport type express from 'express';\nimport type webpack from 'webpack';\nimport type WebpackDevServer from 'webpack-dev-server';\n\nfunction base64SourceMap(source: any) {\n  const base64 = Buffer.from(JSON.stringify(source.map()), 'utf8').toString('base64');\n  return `data:application/json;charset=utf-8;base64,${base64}`;\n}\n\nfunction getSourceById(server: WebpackDevServer & any, id: string) {\n  const module = Array.from(server._stats.compilation.modules).find(\n    m => server._stats.compilation.chunkGraph.getModuleId(m) === id\n  ) as webpack.Module[];\n\n  // @ts-ignore\n  return module.originalSource();\n}\n\n/*\n * Middleware responsible for retrieving a generated source\n * Receives a webpack internal url: \"webpack-internal:///<module-id>\"\n * Returns a generated source: \"<source-text><sourceMappingURL><sourceURL>\"\n *\n * Based on EvalSourceMapDevToolModuleTemplatePlugin.js\n */\nexport function createEvalSourceMapMiddleware(server: WebpackDevServer) {\n  return function handleWebpackInternalMiddleware(\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) {\n    if (req.url.startsWith('/__get-internal-source')) {\n      const fileName = req.query.fileName;\n      if (typeof fileName !== 'string') {\n        return next();\n      }\n      const id = fileName?.match(/webpack-internal:\\/\\/\\/(.+)/)?.[1];\n      // @ts-ignore: untyped\n      if (!id || !server._stats) {\n        return next();\n      }\n\n      const source = getSourceById(server, id);\n      const sourceMapURL = `//# sourceMappingURL=${base64SourceMap(source)}`;\n      const sourceURL = `//# sourceURL=webpack-internal:///${module.id}`;\n      res.end(`${source.source()}\\n${sourceMapURL}\\n${sourceURL}`);\n    } else {\n      return next();\n    }\n  };\n}\n"]}