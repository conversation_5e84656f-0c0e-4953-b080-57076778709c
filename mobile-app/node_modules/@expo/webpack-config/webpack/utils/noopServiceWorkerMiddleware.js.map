{"version": 3, "file": "noopServiceWorkerMiddleware.js", "sourceRoot": "", "sources": ["../../src/utils/noopServiceWorkerMiddleware.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;;AAGH,gDAAwB;AAExB,SAAgB,iCAAiC,CAAC,UAAkB;IAClE,OAAO,SAAS,2BAA2B,CACzC,GAAoB,EACpB,GAAqB,EACrB,IAA0B;QAE1B,IAAI,GAAG,CAAC,GAAG,KAAK,cAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,EAAE;YAChE,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YACjD,GAAG,CAAC,IAAI,CACN;;;;;;;;;;;;;;;EAeN,CACK,CAAC;SACH;aAAM;YACL,IAAI,EAAE,CAAC;SACR;IACH,CAAC,CAAC;AACJ,CAAC;AA9BD,8EA8BC", "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/a422bf2/packages/react-dev-utils/noopServiceWorkerMiddleware.js\n * But with Node LTS support.\n */\n\nimport type express from 'express';\nimport path from 'path';\n\nexport function createNoopServiceWorkerMiddleware(servedPath: string) {\n  return function noopServiceWorkerMiddleware(\n    req: express.Request,\n    res: express.Response,\n    next: express.NextFunction\n  ) {\n    if (req.url === path.posix.join(servedPath, 'service-worker.js')) {\n      res.setHeader('Content-Type', 'text/javascript');\n      res.send(\n        `// This service worker file is effectively a 'no-op' that will reset any\n // previous service worker registered for the same host:port combination.\n // In the production build, this file is replaced with an actual service worker\n // file that will precache your site's local assets.\n // See https://github.com/facebook/create-react-app/issues/2272#issuecomment-302832432\n self.addEventListener('install', () => self.skipWaiting());\n self.addEventListener('activate', () => {\n   self.clients.matchAll({ type: 'window' }).then(windowClients => {\n     for (let windowClient of windowClients) {\n       // Force open pages to refresh, so that they have a chance to load the\n       // fresh navigation response from the local dev server.\n       windowClient.navigate(windowClient.url);\n     }\n   });\n });\n `\n      );\n    } else {\n      next();\n    }\n  };\n}\n"]}