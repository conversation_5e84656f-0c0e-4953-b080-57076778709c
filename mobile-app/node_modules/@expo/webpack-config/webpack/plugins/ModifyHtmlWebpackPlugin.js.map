{"version": 3, "file": "ModifyHtmlWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ModifyHtmlWebpackPlugin.ts"], "names": [], "mappings": ";;AAAA,qCAAqF;AAErF,SAAS,gBAAgB,CAAC,QAAkB,EAAE,IAAY;;IACxD,OAAO,MAAA,MAAA,QAAQ,CAAC,OAAO,0CAAE,OAAO,0CAC5B,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,WAAW,EACrC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;AACnE,CAAC;AAkBD,MAAqB,uBAAuB;IAC1C,YAAoB,gBAAiD,EAAE;QAAnD,kBAAa,GAAb,aAAa,CAAsC;IAAG,CAAC;IAE3E,KAAK,CAAC,WAAW,CACf,QAAkB,EAClB,WAAwB,EACxB,IAAoB;QAEpB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,QAAkB;QACtB,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,WAAwB,EAAE,EAAE;YACjF,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CACxC;gBACE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC3B,0EAA0E;gBAC1E,KAAK,EAAE,qBAAW,CAAC,+BAA+B;aACnD,EACD,KAAK,IAAI,EAAE;gBACT,gEAAgE;gBAChE,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,mBAAmB,CAAQ,CAAC;gBACjF,IAAI,iBAAiB,EAAE;oBACrB,IAAI,OAAO,iBAAiB,CAAC,QAAQ,KAAK,WAAW,EAAE;wBACrD,WAAW,CAAC,MAAM,CAAC,IAAI,CACrB,IAAI,sBAAY,CACd,iIAAiI,CAClI,CACF,CAAC;wBACF,OAAO;qBACR;oBAED,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,cAAc,CAAC,QAAQ,CAC7D,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,KAAK,EACH,IAAoB,EACpB,YAAiE,EACjE,EAAE;wBACF,sDAAsD;wBACtD,kEAAkE;wBAClE,MAAM,kBAAkB,GACtB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,UAAU;4BAC7C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BACxC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;wBAEhD,IAAI,kBAAkB,KAAK,KAAK,EAAE;4BAChC,OAAO,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;yBACjC;wBAED,IAAI;4BACF,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;yBAC5D;wBAAC,OAAO,KAAU,EAAE;4BACnB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAChC;wBAED,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3B,CAAC,CACF,CAAC;iBACH;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA/DD,0CA+DC", "sourcesContent": ["import { Compilation, Compiler, WebpackError, WebpackPluginInstance } from 'webpack';\n\nfunction maybeFetchPlugin(compiler: Compiler, name: string): WebpackPluginInstance | undefined {\n  return compiler.options?.plugins\n    ?.map(({ constructor }) => constructor)\n    .find(constructor => constructor && constructor.name === name);\n}\n\nexport type HTMLPluginData = {\n  assetTags: any;\n  outputName: string;\n  plugin: any;\n};\n\nexport type HTMLLinkNode = {\n  rel?: string;\n  name?: string;\n  content?: string;\n  media?: string;\n  href?: string;\n  sizes?: string;\n  node: any;\n};\n\nexport default class ModifyHtmlWebpackPlugin {\n  constructor(private modifyOptions: { inject?: boolean | Function } = {}) {}\n\n  async modifyAsync(\n    compiler: Compiler,\n    compilation: Compilation,\n    data: HTMLPluginData\n  ): Promise<HTMLPluginData> {\n    return data;\n  }\n\n  apply(compiler: Compiler) {\n    compiler.hooks.compilation.tap(this.constructor.name, (compilation: Compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: this.constructor.name,\n          // https://github.com/webpack/webpack/blob/master/lib/Compilation.js#L3280\n          stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,\n        },\n        async () => {\n          // Hook into the html-webpack-plugin processing and add the html\n          const HtmlWebpackPlugin = maybeFetchPlugin(compiler, 'HtmlWebpackPlugin') as any;\n          if (HtmlWebpackPlugin) {\n            if (typeof HtmlWebpackPlugin.getHooks === 'undefined') {\n              compilation.errors.push(\n                new WebpackError(\n                  'ModifyHtmlWebpackPlugin - This ModifyHtmlWebpackPlugin version is not compatible with your current HtmlWebpackPlugin version.\\n'\n                )\n              );\n              return;\n            }\n\n            HtmlWebpackPlugin.getHooks(compilation).alterAssetTags.tapAsync(\n              this.constructor.name,\n              async (\n                data: HTMLPluginData,\n                htmlCallback: (error: Error | null, data: HTMLPluginData) => void\n              ) => {\n                // Skip if a custom injectFunction returns false or if\n                // the htmlWebpackPlugin optuons includes a `favicons: false` flag\n                const isInjectionAllowed =\n                  typeof this.modifyOptions.inject === 'function'\n                    ? this.modifyOptions.inject(data.plugin)\n                    : data.plugin.options.pwaManifest !== false;\n\n                if (isInjectionAllowed === false) {\n                  return htmlCallback(null, data);\n                }\n\n                try {\n                  data = await this.modifyAsync(compiler, compilation, data);\n                } catch (error: any) {\n                  compilation.errors.push(error);\n                }\n\n                htmlCallback(null, data);\n              }\n            );\n          }\n        }\n      );\n    });\n  }\n}\n"]}