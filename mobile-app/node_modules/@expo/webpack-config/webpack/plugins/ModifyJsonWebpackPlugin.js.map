{"version": 3, "file": "ModifyJsonWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ModifyJsonWebpackPlugin.ts"], "names": [], "mappings": ";;AAAA,qCAAqF;AAUrF,SAAS,gBAAgB,CAAC,QAAkB,EAAE,IAAY;;IACxD,OAAO,MAAA,MAAA,QAAQ,CAAC,OAAO,0CAAE,OAAO,0CAC5B,GAAG,CAAC,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC,WAAW,EACrC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACzE,CAAC;AAED,MAAqB,uBAAuB;IAC1C,KAAK,CAAC,WAAW,CACf,QAAkB,EAClB,WAAwB,EACxB,IAAuB;QAEvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,QAAkB;QACtB,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,WAAwB,EAAE,EAAE;YACjF,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CACxC;gBACE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAC3B,0EAA0E;gBAC1E,KAAK,EAAE,qBAAW,CAAC,+BAA+B;aACnD,EACD,KAAK,IAAI,EAAE;gBACT,gEAAgE;gBAChE,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,QAAQ,EAAE,0BAA0B,CAAQ,CAAC;gBACxF,IAAI,iBAAiB,EAAE;oBACrB,IAAI,OAAO,iBAAiB,CAAC,QAAQ,KAAK,WAAW,EAAE;wBACrD,WAAW,CAAC,MAAM,CAAC,IAAI,CACrB,IAAI,sBAAY,CACd,iIAAiI,CAClI,CACF,CAAC;wBACF,OAAO;qBACR;oBAED,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,QAAQ,CACzD,IAAI,CAAC,WAAW,CAAC,IAAI,EACrB,KAAK,EACH,IAAuB,EACvB,QAAgE,EAChE,EAAE;wBACF,IAAI;4BACF,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;yBAC5D;wBAAC,OAAO,KAAU,EAAE;4BACnB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAChC;wBAED,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBACvB,CAAC,CACF,CAAC;iBACH;YACH,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAlDD,0CAkDC", "sourcesContent": ["import { Compilation, Compiler, WebpackError, WebpackPluginInstance } from 'webpack';\n\nimport { BeforeEmitOptions } from './JsonWebpackPlugin';\n\nexport type Options = {\n  path: string;\n  json: any;\n  pretty?: boolean;\n};\n\nfunction maybeFetchPlugin(compiler: Compiler, name: string): WebpackPluginInstance | undefined {\n  return compiler.options?.plugins\n    ?.map(({ constructor }) => constructor)\n    .find(constructor => constructor && constructor.name.endsWith(name));\n}\n\nexport default class ModifyJsonWebpackPlugin {\n  async modifyAsync(\n    compiler: Compiler,\n    compilation: Compilation,\n    data: BeforeEmitOptions\n  ): Promise<BeforeEmitOptions> {\n    return data;\n  }\n\n  apply(compiler: Compiler) {\n    compiler.hooks.compilation.tap(this.constructor.name, (compilation: Compilation) => {\n      compilation.hooks.processAssets.tapPromise(\n        {\n          name: this.constructor.name,\n          // https://github.com/webpack/webpack/blob/master/lib/Compilation.js#L3280\n          stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,\n        },\n        async () => {\n          // Hook into the html-webpack-plugin processing and add the html\n          const JsonWebpackPlugin = maybeFetchPlugin(compiler, 'PwaManifestWebpackPlugin') as any;\n          if (JsonWebpackPlugin) {\n            if (typeof JsonWebpackPlugin.getHooks === 'undefined') {\n              compilation.errors.push(\n                new WebpackError(\n                  'ModifyJsonWebpackPlugin - This ModifyJsonWebpackPlugin version is not compatible with your current JsonWebpackPlugin version.\\n'\n                )\n              );\n              return;\n            }\n\n            JsonWebpackPlugin.getHooks(compilation).beforeEmit.tapAsync(\n              this.constructor.name,\n              async (\n                data: BeforeEmitOptions,\n                callback: (error: Error | null, data: BeforeEmitOptions) => void\n              ) => {\n                try {\n                  data = await this.modifyAsync(compiler, compilation, data);\n                } catch (error: any) {\n                  compilation.errors.push(error);\n                }\n\n                callback(null, data);\n              }\n            );\n          }\n        }\n      );\n    });\n  }\n}\n"]}