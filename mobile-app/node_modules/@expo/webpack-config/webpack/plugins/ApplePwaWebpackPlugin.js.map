{"version": 3, "file": "ApplePwaWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ApplePwaWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAC1B,uCAAoG;AACpG,qCAAyD;AAEzD,wFAAkG;AASlG,MAAqB,qBAAsB,SAAQ,iCAAuB;IACxE,YACU,UAA4E,EAC5E,IAAkB,EAClB,IAAwB,EACxB,YAAgC;QAExC,KAAK,EAAE,CAAC;QALA,eAAU,GAAV,UAAU,CAAkE;QAC5E,SAAI,GAAJ,IAAI,CAAc;QAClB,SAAI,GAAJ,IAAI,CAAoB;QACxB,iBAAY,GAAZ,YAAY,CAAoB;IAG1C,CAAC;IAED,KAAK,CAAC,WAAW,CACf,QAAkB,EAClB,WAAwB,EACxB,IAAoB;QAEpB,OAAO;;QAEP,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAW,EAAE;YACnD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC;QAEF,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YAC7B,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,EAAE;gBACjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC,CAAC;aACpE;YACD,IAAI,CAAC,kBAAkB,CAAC,8BAA8B,CAAC,EAAE;gBACvD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC,CAAC;aAC1E;SACF;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,EAAE;YAC3E,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC,CAAC;SACpE;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,EAAE;YACvE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,4BAA4B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACjF;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,kBAAkB,CAAC,uCAAuC,CAAC,EAAE;YACtF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CACtB,OAAO,CAAC,uCAAuC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CACrE,CAAC;SACH;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;QAEpE,SAAS,SAAS,CAAC,IAAY,EAAE,OAAe;YAC9C,MAAM,CAAC,GAAG,CAAC,eAAK,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,SAAS,UAAU,CAAC,IAAY,EAAE,OAAe;YAC/C,MAAM,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,UAAU,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACtE,CAAC;QACD,WAAW;QACX,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,MAAM,KAAK,GAAa,IAAI,CAAC,UAAU,CAAC,KAAK;iBAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,kBAAkB,CAAC;iBACzC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAM,CAAC,CAAC;YAEtB,MAAM,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;YAC1B,MAAM,aAAa,GAAa,EAAE,CAAC;YAEnC,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE;gBAC9B,MAAM,KAAK,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;gBAChC,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBACzB,SAAS,CACP,cAAc,EACd,oDAAoD,KAAK,SAAS,CACnE,CAAC;iBACH;qBAAM;oBACL,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBAC1B;aACF;YAED,MAAM,UAAU,GAAG,MAAM,IAAA,iCAAsB,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE;gBAC1E,KAAK,EAAE,aAAa;aACrB,CAAC,CAAC;YAEH,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE;gBAC9B,MAAM,IAAI,GAAG,MAAA,KAAK,CAAC,GAAG,0CAAE,UAAU,CAAC,KAAK,CAAC;gBACzC,IAAI,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBAChC,SAAS,CACP,cAAc,EACd,oDAAoD,IAAI,SAAS,CAClE,CAAC;iBACH;qBAAM;oBACL,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,iBAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACrC;aACF;SACF;aAAM;YACL,UAAU,CAAC,cAAc,EAAE,sDAAsD,CAAC,CAAC;SACpF;QAED,iBAAiB;QAEjB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,MAAM,GAAG,MAAM,IAAA,8BAAmB,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAE7E,MAAM,KAAK,GAAa,IAAI,CAAC,UAAU,CAAC,KAAK;iBAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,2BAA2B,CAAC;iBAClD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAM,CAAC,CAAC;YAEtB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,MAAM,KAAK,GAAG,MAAA,KAAK,CAAC,GAAG,0CAAE,UAAU,CAAC,KAAK,CAAC;gBAC1C,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBAClC,SAAS,CACP,uBAAuB,EACvB,6DAA6D,KAAK,UAAU,CAC7E,CAAC;iBACH;qBAAM;oBACL,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,iBAAO,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACrC;aACF;SACF;aAAM;YACL,UAAU,CAAC,uBAAuB,EAAE,sDAAsD,CAAC,CAAC;SAC7F;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AArHD,wCAqHC;AAED,SAAS,OAAO,CAAC,IAAY,EAAE,OAAe;IAC5C,OAAO;QACL,OAAO,EAAE,MAAM;QACf,OAAO,EAAE,IAAI;QACb,UAAU,EAAE;YACV,IAAI;YACJ,OAAO;SACR;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import chalk from 'chalk';\nimport { generateAppleIconAsync, generateSplashAsync, IconOptions, ProjectOptions } from 'expo-pwa';\nimport { Compilation, Compiler, sources } from 'webpack';\n\nimport ModifyHtmlWebpackPlugin, { HTMLLinkNode, HTMLPluginData } from './ModifyHtmlWebpackPlugin';\n\nexport type ApplePwaMeta = {\n  name?: string;\n  barStyle?: string;\n  isWebAppCapable?: boolean;\n  isFullScreen?: boolean;\n};\n\nexport default class ApplePwaWebpackPlugin extends ModifyHtmlWebpackPlugin {\n  constructor(\n    private pwaOptions: ProjectOptions & { links: HTMLLinkNode[]; meta: HTMLLinkNode[] },\n    private meta: ApplePwaMeta,\n    private icon: IconOptions | null,\n    private startupImage: IconOptions | null\n  ) {\n    super();\n  }\n\n  async modifyAsync(\n    compiler: Compiler,\n    compilation: Compilation,\n    data: HTMLPluginData\n  ): Promise<HTMLPluginData> {\n    // Meta\n\n    const hasMetaTagWithName = (name: string): boolean => {\n      return this.pwaOptions.meta.some(v => v.name === name);\n    };\n\n    if (this.meta.isWebAppCapable) {\n      if (!hasMetaTagWithName('mobile-web-app-capable')) {\n        data.assetTags.meta.push(metaTag('mobile-web-app-capable', 'yes'));\n      }\n      if (!hasMetaTagWithName('apple-mobile-web-app-capable')) {\n        data.assetTags.meta.push(metaTag('apple-mobile-web-app-capable', 'yes'));\n      }\n    }\n    if (this.meta.isFullScreen && !hasMetaTagWithName('apple-touch-fullscreen')) {\n      data.assetTags.meta.push(metaTag('apple-touch-fullscreen', 'yes'));\n    }\n    if (this.meta.name && !hasMetaTagWithName('apple-mobile-web-app-title')) {\n      data.assetTags.meta.push(metaTag('apple-mobile-web-app-title', this.meta.name));\n    }\n    if (this.meta.barStyle && !hasMetaTagWithName('apple-mobile-web-app-status-bar-style')) {\n      data.assetTags.meta.push(\n        metaTag('apple-mobile-web-app-status-bar-style', this.meta.barStyle)\n      );\n    }\n\n    const logger = compiler.getInfrastructureLogger('apple-pwa-plugin');\n\n    function logNotice(type: string, message: string) {\n      logger.log(chalk.magenta(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n\n    function logWarning(type: string, message: string) {\n      logger.warn(chalk.yellow(`\\u203A ${type}: ${chalk.gray(message)}`));\n    }\n    // App Icon\n    if (this.icon) {\n      const links: string[] = this.pwaOptions.links\n        .filter(v => v.rel === 'apple-touch-icon')\n        .map(v => v.sizes!);\n\n      const targetSizes = [180];\n      const requiredSizes: number[] = [];\n\n      for (const size of targetSizes) {\n        const sizes = `${size}x${size}`;\n        if (links.includes(sizes)) {\n          logNotice(\n            'Safari Icons',\n            `Using custom <link rel=\"apple-touch-icon\" sizes=\"${sizes}\" .../>`\n          );\n        } else {\n          requiredSizes.push(size);\n        }\n      }\n\n      const iconAssets = await generateAppleIconAsync(this.pwaOptions, this.icon, {\n        sizes: requiredSizes,\n      });\n\n      for (const asset of iconAssets) {\n        const size = asset.tag?.attributes.sizes;\n        if (size && links.includes(size)) {\n          logNotice(\n            'Safari Icons',\n            `Using custom <link rel=\"apple-touch-icon\" sizes=\"${size}\" .../>`\n          );\n        } else {\n          compilation.emitAsset(asset.asset.path, new sources.RawSource(asset.asset.source));\n          data.assetTags.meta.push(asset.tag);\n        }\n      }\n    } else {\n      logWarning('Safari Icons', `No template image found, skipping auto generation...`);\n    }\n\n    // Splash screens\n\n    if (this.startupImage) {\n      const assets = await generateSplashAsync(this.pwaOptions, this.startupImage);\n\n      const links: string[] = this.pwaOptions.links\n        .filter(v => v.rel === 'apple-touch-startup-image')\n        .map(v => v.media!);\n\n      for (const asset of assets) {\n        const media = asset.tag?.attributes.media;\n        if (media && links.includes(media)) {\n          logNotice(\n            'Safari Splash Screens',\n            `Using custom <link rel=\"apple-touch-startup-image\" media=\"${media}\" ... />`\n          );\n        } else {\n          compilation.emitAsset(asset.asset.path, new sources.RawSource(asset.asset.source));\n          data.assetTags.meta.push(asset.tag);\n        }\n      }\n    } else {\n      logWarning('Safari Splash Screens', `No template image found, skipping auto generation...`);\n    }\n    return data;\n  }\n}\n\nfunction metaTag(name: string, content: string): any {\n  return {\n    tagName: 'meta',\n    voidTag: true,\n    attributes: {\n      name,\n      content,\n    },\n  };\n}\n"]}