{"version": 3, "file": "createOriginalStackFrame.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/createOriginalStackFrame.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;;;;AAEH,kDAAqD;AACrD,gDAAwB;AACxB,aAAa;AACb,2CAAuE;AAQvE,SAAS,aAAa,CAAC,MAAc;IACnC,uDAAuD;IACvD,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;QACpC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC7B;IAED,iDAAiD;IACjD,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;QACxC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC7B;IAED,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;QACnC,OAAO,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC7B;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,4CAA4C;AAC5C,KAAK,UAAU,oCAAoC,CACjD,aAAkB,EAClB,QAAiD;;IAEjD,MAAM,QAAQ,GAAG,MAAM,IAAI,8BAAiB,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC;IAClE,IAAI;QACF,MAAM,cAAc,GAA2B,QAAQ,CAAC,mBAAmB,CAAC;YAC1E,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,MAAA,QAAQ,CAAC,MAAM,mCAAI,CAAC;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAC;SACb;QAED,MAAM,aAAa,GACjB,MAAA,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,EAAE,yBAAyB,CAAC,IAAI,CAAC,mCAAI,IAAI,CAAC;QAE3F,OAAO;YACL,cAAc;YACd,aAAa;SACd,CAAC;KACH;YAAS;QACR,8BAA8B;QAC9B,MAAA,QAAQ,CAAC,OAAO,wDAAI,CAAC;KACtB;AACH,CAAC;AAEM,KAAK,UAAU,wBAAwB,CAAC,EAC7C,IAAI,EACJ,MAAM,EACN,MAAM,EACN,UAAU,EACV,aAAa,EACb,KAAK,EACL,gBAAgB,GASjB;;IACC,MAAM,MAAM,GAAG,MAAM,oCAAoC,CAAC,MAAM,EAAE;QAChE,IAAI;QACJ,MAAM;KACP,CAAC,CAAC;IAEH,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC;KACb;IAED,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,MAAM,CAAC;IAEjD,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;QAC1B,OAAO,IAAI,CAAC;KACb;IAED,MAAM,QAAQ,GAAG,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,UAAU,IAAI,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;IAEjG,MAAM,aAAa,GAAe;QAChC,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,cAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM;QACpF,UAAU,EAAE,cAAc,CAAC,IAAI;QAC/B,MAAM,EAAE,cAAc,CAAC,MAAM;QAC7B,UAAU,EAAE,KAAK,CAAC,UAAU;QAC5B,SAAS,EAAE,EAAE;KACd,CAAC;IAEF,MAAM,iBAAiB,GACrB,CAAC,gBAAgB,IAAI,CAAC,CAAC,MAAA,MAAA,aAAa,CAAC,IAAI,0CAAE,QAAQ,CAAC,cAAc,CAAC,mCAAI,IAAI,CAAC,CAAC;QAC7E,aAAa;QACb,cAAc,CAAC,IAAI;QACjB,CAAC,CAAE,IAAA,6BAAgB,EACf,aAAa,EACb;YACE,KAAK,EAAE;gBACL,IAAI,EAAE,cAAc,CAAC,IAAI;gBACzB,MAAM,EAAE,MAAA,cAAc,CAAC,MAAM,mCAAI,CAAC;aACnC;SACF,EACD,EAAE,UAAU,EAAE,IAAI,EAAE,CACV;QACd,CAAC,CAAC,IAAI,CAAC;IAEX,OAAO;QACL,kBAAkB,EAAE,aAAa;QACjC,iBAAiB;KAClB,CAAC;AACJ,CAAC;AA9DD,4DA8DC", "sourcesContent": ["/**\n * Copyright © 2021 650 Industries.\n * Copyright © 2021 Vercel, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/vercel/next.js/blob/1552b8341e5b512a2827485a4a9689cd429c520e/packages/react-dev-overlay/src/middleware.ts#L63-L178\n */\n\nimport { codeFrameColumns } from '@babel/code-frame';\nimport path from 'path';\n// @ts-ignore\nimport { NullableMappedPosition, SourceMapConsumer } from 'source-map';\nimport { StackFrame } from 'stacktrace-parser';\n\nexport type OriginalStackFrameResponse = {\n  originalStackFrame: StackFrame;\n  originalCodeFrame: string | null;\n};\n\nfunction getSourcePath(source: string) {\n  // Webpack prefixes certain source paths with this path\n  if (source.startsWith('webpack:///')) {\n    return source.substring(11);\n  }\n\n  // Make sure library name is filtered out as well\n  if (source.startsWith('webpack://_N_E/')) {\n    return source.substring(15);\n  }\n\n  if (source.startsWith('webpack://')) {\n    return source.substring(10);\n  }\n\n  return source;\n}\n\n// TODO: Use dev-server symbolicator instead\nasync function findOriginalSourcePositionAndContent(\n  webpackSource: any,\n  position: { line: number; column: number | null }\n) {\n  const consumer = await new SourceMapConsumer(webpackSource.map());\n  try {\n    const sourcePosition: NullableMappedPosition = consumer.originalPositionFor({\n      line: position.line,\n      column: position.column ?? 0,\n    });\n\n    if (!sourcePosition.source) {\n      return null;\n    }\n\n    const sourceContent: string | null =\n      consumer.sourceContentFor(sourcePosition.source, /* returnNullOnMissing */ true) ?? null;\n\n    return {\n      sourcePosition,\n      sourceContent,\n    };\n  } finally {\n    // @ts-ignore: unexpected type\n    consumer.destroy?.();\n  }\n}\n\nexport async function createOriginalStackFrame({\n  line,\n  column,\n  source,\n  modulePath,\n  rootDirectory,\n  frame,\n  frameNodeModules,\n}: {\n  line: number;\n  column: number | null;\n  source: any;\n  modulePath?: string;\n  rootDirectory: string;\n  frameNodeModules?: boolean;\n  frame: any;\n}): Promise<OriginalStackFrameResponse | null> {\n  const result = await findOriginalSourcePositionAndContent(source, {\n    line,\n    column,\n  });\n\n  if (result === null) {\n    return null;\n  }\n\n  const { sourcePosition, sourceContent } = result;\n\n  if (!sourcePosition.source) {\n    return null;\n  }\n\n  const filePath = path.resolve(rootDirectory, modulePath || getSourcePath(sourcePosition.source));\n\n  const originalFrame: StackFrame = {\n    file: sourceContent ? path.relative(rootDirectory, filePath) : sourcePosition.source,\n    lineNumber: sourcePosition.line,\n    column: sourcePosition.column,\n    methodName: frame.methodName, // TODO: resolve original method name (?)\n    arguments: [],\n  };\n\n  const originalCodeFrame: string | null =\n    (frameNodeModules || !(originalFrame.file?.includes('node_modules') ?? true)) &&\n    sourceContent &&\n    sourcePosition.line\n      ? (codeFrameColumns(\n          sourceContent,\n          {\n            start: {\n              line: sourcePosition.line,\n              column: sourcePosition.column ?? 0,\n            },\n          },\n          { forceColor: true }\n        ) as string)\n      : null;\n\n  return {\n    originalStackFrame: originalFrame,\n    originalCodeFrame,\n  };\n}\n"]}