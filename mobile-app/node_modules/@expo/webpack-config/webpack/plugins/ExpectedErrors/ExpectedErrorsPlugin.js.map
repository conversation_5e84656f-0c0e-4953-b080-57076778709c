{"version": 3, "file": "ExpectedErrorsPlugin.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/ExpectedErrorsPlugin.ts"], "names": [], "mappings": ";;AAWA,+DAA4D;AAE5D,MAAqB,oBAAoB;IAC/B,gBAAgB,CAAC,WAAgC,EAAE,MAAa;QACtE,OAAO,OAAO,CAAC,GAAG,CAChB,MAAM,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YACvB,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,IAAA,yCAAmB,EAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAC7D,OAAO,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;aAC1C;YAAC,OAAO,CAAM,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACf,OAAO,KAAK,CAAC;aACd;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAA0B;QAC9B,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;YAClE,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;;gBACvE,WAAW;gBACX,IAAI,MAAA,WAAW,CAAC,QAAQ,0CAAE,MAAM,EAAE;oBAChC,WAAW,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;iBACvF;gBAED,SAAS;gBACT,IAAI,MAAA,WAAW,CAAC,MAAM,0CAAE,MAAM,EAAE;oBAC9B,WAAW,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;iBACnF;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA9BD,uCA8BC", "sourcesContent": ["/**\n * Copyright © 2021 650 Industries.\n * Copyright © 2021 Vercel, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/vercel/next.js/blob/1552b8341e5b512a2827485a4a9689cd429c520e/packages/next/build/webpack/plugins/wellknown-errors-plugin/index.ts\n */\nimport webpack from 'webpack';\n\nimport { getModuleBuildError } from './getModuleBuildError';\n\nexport default class ExpectedErrorsPlugin {\n  private parseErrorsAsync(compilation: webpack.Compilation, errors: any[]) {\n    return Promise.all(\n      errors.map(async error => {\n        try {\n          const parsed = await getModuleBuildError(compilation, error);\n          return parsed === false ? error : parsed;\n        } catch (e: any) {\n          console.log(e);\n          return error;\n        }\n      })\n    );\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(this.constructor.name, compilation => {\n      compilation.hooks.afterSeal.tapPromise(this.constructor.name, async () => {\n        // Warnings\n        if (compilation.warnings?.length) {\n          compilation.warnings = await this.parseErrorsAsync(compilation, compilation.warnings);\n        }\n\n        // Errors\n        if (compilation.errors?.length) {\n          compilation.errors = await this.parseErrorsAsync(compilation, compilation.errors);\n        }\n      });\n    });\n  }\n}\n"]}