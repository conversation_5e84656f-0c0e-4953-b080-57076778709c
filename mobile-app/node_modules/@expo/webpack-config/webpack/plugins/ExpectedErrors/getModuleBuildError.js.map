{"version": 3, "file": "getModuleBuildError.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/getModuleBuildError.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;GAQG;AACH,2CAA6B;AAI7B,uDAAkD;AAClD,6DAAoF;AAEpF,SAAS,WAAW,CAAC,WAAgC,EAAE,CAAM;;IAC3D,IAAI,QAAgB,CAAC;IACrB,MAAM,GAAG,GAAkB,MAAA,MAAA,MAAA,WAAW,CAAC,QAAQ,0CAAE,OAAO,mCAAI,WAAW,CAAC,OAAO,CAAC,OAAO,mCAAI,IAAI,CAAC;IAChG,IAAI,GAAG,KAAK,IAAI,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ,EAAE;QAClD,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC1E,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;KACnE;SAAM;QACL,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;QACtD,IAAI,OAAO,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,kBAAkB,CAAA,KAAK,UAAU,EAAE;YAC/C,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;SACnD;aAAM;YACL,QAAQ,GAAG,MAAA,CAAC,CAAC,OAAO,mCAAI,CAAC,CAAC,WAAW,CAAC;SACvC;KACF;IAED,OAAO,QAAQ,IAAI,WAAW,CAAC;AACjC,CAAC;AAEM,KAAK,UAAU,mBAAmB,CACvC,WAAgC,EAChC,KAAU;IAEV,IACE,CAAC,CACC,OAAO,KAAK,KAAK,QAAQ;QACzB,CAAC,kBAAkB,EAAE,qBAAqB,EAAE,yBAAyB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3F,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;QACrB,KAAK,CAAC,KAAK,YAAY,KAAK,CAC7B,EACD;QACA,OAAO,KAAK,CAAC;KACd;IAED,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE9D,IAAI,KAAK,CAAC,IAAI,KAAK,yBAAyB,EAAE;QAC5C,OAAO,MAAM,IAAA,+CAA0B,EAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;KAC7E;IAED,MAAM,aAAa,GAAG,MAAM,IAAA,qCAAgB,EAAC,WAAW,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;IACjF,IAAI,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,KAAK,GAAG,IAAA,+BAAa,EAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;IACzD,IAAI,KAAK,KAAK,KAAK,EAAE;QACnB,OAAO,KAAK,CAAC;KACd;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAhCD,kDAgCC", "sourcesContent": ["/**\n * Copyright © 2021 650 Industries.\n * Copyright © 2021 Vercel, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/vercel/next.js/blob/1552b8341e5b512a2827485a4a9689cd429c520e/packages/next/build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts\n */\nimport * as path from 'path';\nimport webpack from 'webpack';\n\nimport { WebpackFileError } from './WebpackFileError';\nimport { getBabelError } from './parseBabelError';\nimport { getModuleDependencyWarning, getNotFoundError } from './parseNotFoundError';\n\nfunction getFileData(compilation: webpack.Compilation, m: any): string {\n  let resolved: string;\n  const ctx: string | null = compilation.compiler?.context ?? compilation.options.context ?? null;\n  if (ctx !== null && typeof m.resource === 'string') {\n    const res = path.relative(ctx, m.resource).replace(/\\\\/g, path.posix.sep);\n    resolved = res.startsWith('.') ? res : `.${path.posix.sep}${res}`;\n  } else {\n    const requestShortener = compilation.requestShortener;\n    if (typeof m?.readableIdentifier === 'function') {\n      resolved = m.readableIdentifier(requestShortener);\n    } else {\n      resolved = m.request ?? m.userRequest;\n    }\n  }\n\n  return resolved || '<unknown>';\n}\n\nexport async function getModuleBuildError(\n  compilation: webpack.Compilation,\n  input: any\n): Promise<WebpackFileError | false> {\n  if (\n    !(\n      typeof input === 'object' &&\n      ['ModuleBuildError', 'ModuleNotFoundError', 'ModuleDependencyWarning'].includes(input.name) &&\n      Boolean(input.module) &&\n      input.error instanceof Error\n    )\n  ) {\n    return false;\n  }\n\n  const sourceFilename = getFileData(compilation, input.module);\n\n  if (input.name === 'ModuleDependencyWarning') {\n    return await getModuleDependencyWarning(compilation, input, sourceFilename);\n  }\n\n  const notFoundError = await getNotFoundError(compilation, input, sourceFilename);\n  if (notFoundError !== false) {\n    return notFoundError;\n  }\n\n  const babel = getBabelError(sourceFilename, input.error);\n  if (babel !== false) {\n    return babel;\n  }\n\n  return false;\n}\n"]}