{"version": 3, "file": "parseNotFoundError.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/parseNotFoundError.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,kDAA0B;AAC1B,gDAAwB;AAGxB,yDAAsD;AACtD,yEAAsE;AAEtE,wCAAwC;AACxC,kBAAkB;AAClB,MAAM,UAAU,GAAG,KAAK,CAAC;AAEzB,SAAS,cAAc,CAAC,KAAU,EAAE,WAAgB;IAClD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;IACjC,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;IAC3B,OAAO,OAAO,EAAE;QACd,IAAI,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC;YAAE,MAAM,CAAC,mDAAmD;QAC3F,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM;YAAE,MAAM;QACnB,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;QAC9C,OAAO,GAAG,MAAM,CAAC;KAClB;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAC,WAAgB,EAAE,KAAU,EAAE,QAAgB;IACnF,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,EAAE;QACxC,OAAO,KAAK,CAAC;KACd;IACD,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;QAChE,GAAG,EAAE,KAAK,CAAC,GAAG;YACZ,CAAC,CAAC,KAAK,CAAC,GAAG;YACX,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxE,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE;KAC9C,CAAC,CAAC;IACH,wFAAwF;IACxF,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,KAAK,CAAC;KACd;IAED,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO;SACrC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,sBAAsB,EAAE,kBAAkB,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE3E,OAAO,IAAI,mCAAgB,CACzB;QACE,QAAQ,EAAE,QAAQ;QAClB,IAAI,EAAE,KAAK,CAAC,UAAU;QACtB,GAAG,EAAE,KAAK,CAAC,MAAM;KAClB,EACD;QACE,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,KAAK,YAAY,EAAE;QACxD,KAAK,CAAC,KAAK;QACX,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC;QAClC,YAAY;KACb;SACE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;AACJ,CAAC;AAlCD,4CAkCC;AAED,SAAS,cAAc,CAAC,WAAgC,EAAE,KAAU;IAClE,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,EAAE,CAAC;KACX;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC,CAAC;IAEhD,IAAI,eAAe,GAAG,wCAAwC,CAAC;IAC/D,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAEvD,KAAK,MAAM,EAAE,MAAM,EAAE,IAAI,WAAW,EAAE;QACpC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YACpB,SAAS;SACV;QACD,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7D,eAAe,IAAI,KAAK,QAAQ,IAAI,CAAC;KACtC;IAED,OAAO,eAAe,GAAG,IAAI,CAAC;AAChC,CAAC;AAED,iEAAiE;AACjE,sEAAsE;AAC/D,KAAK,UAAU,0BAA0B,CAC9C,WAAgC,EAChC,KAAU,EACV,QAAgB;IAEhB,IAAI,KAAK,CAAC,IAAI,KAAK,yBAAyB,EAAE;QAC5C,OAAO,KAAK,CAAC;KACd;IAED,MAAM,KAAK,GAAG,MAAM,gBAAgB,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;QAChE,GAAG,EAAE,KAAK,CAAC,GAAG;QACd,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE;KAC9C,CAAC,CAAC;IAEH,wFAAwF;IACxF,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,mCAAgB,CACzB;QACE,QAAQ,EAAE,QAAQ;QAClB,IAAI,EAAE,KAAK,CAAC,UAAU;QACtB,GAAG,EAAE,KAAK,CAAC,MAAM;KAClB,EACD;QACE,KAAK,CAAC,KAAK,CAAC,OAAO;QACnB,KAAK,CAAC,KAAK;QACX,cAAc,CAAC,WAAW,EAAE,KAAK,CAAC;QAClC,YAAY;KACb;SACE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,IAAI,CAAC,CACd,CAAC;AACJ,CAAC;AAlCD,gEAkCC;AAED,SAAS,cAAc,CAAC,WAAgC;;IACtD,4BAA4B;IAC5B,OAAO,MAAA,WAAW,CAAC,OAAO,CAAC,OAAO,mCAAI,WAAW,CAAC,OAAO,CAAC;AAC5D,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC7B,WAAmB,EACnB,EAAE,GAAG,EAAE,cAAc,EAAqC;;IAE1D,IAAI;QACF,MAAM,MAAM,GAAG,MAAM,IAAA,mDAAwB,EAAC;YAC5C,IAAI,EAAE,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,0CAAE,IAAI;YACtB,MAAM,EAAE,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,KAAK,0CAAE,MAAM;YAC1B,MAAM,EAAE,cAAc;YACtB,aAAa,EAAE,WAAW;YAC1B,gBAAgB,EAAE,IAAI;YACtB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;QAEH,wFAAwF;QACxF,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,CAAC;SACb;QACD,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,kBAAkB,CAAC,UAAU;YAChD,MAAM,EAAE,MAAM,CAAC,kBAAkB,CAAC,MAAM;YACxC,KAAK,EAAE,MAAA,MAAM,CAAC,iBAAiB,mCAAI,EAAE;SACtC,CAAC;KACH;IAAC,OAAO,GAAQ,EAAE;QACjB,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QAChD,8CAA8C;QAC9C,OAAO,IAAI,CAAC;KACb;AACH,CAAC", "sourcesContent": ["/**\n * Copyright JS Foundation and other contributors\n * Copyright (c) 2021 Vercel, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * 'Software'), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n *\n * Based on https://github.com/webpack/webpack/blob/fcdd04a833943394bbb0a9eeb54a962a24cc7e41/lib/stats/DefaultStatsFactoryPlugin.js#L422-L431\n * Based on https://github.com/vercel/next.js/pull/27840\n */\nimport chalk from 'chalk';\nimport path from 'path';\nimport webpack from 'webpack';\n\nimport { WebpackFileError } from './WebpackFileError';\nimport { createOriginalStackFrame } from './createOriginalStackFrame';\n\n// import { isWebpack5 } from 'webpack';\n// TODO: Webpack 5\nconst isWebpack5 = false;\n\nfunction getModuleTrace(input: any, compilation: any) {\n  const visitedModules = new Set();\n  const moduleTrace = [];\n  let current = input.module;\n  while (current) {\n    if (visitedModules.has(current)) break; // circular (technically impossible, but who knows)\n    visitedModules.add(current);\n    const origin = compilation.moduleGraph.getIssuer(current);\n    if (!origin) break;\n    moduleTrace.push({ origin, module: current });\n    current = origin;\n  }\n\n  return moduleTrace;\n}\n\nexport async function getNotFoundError(compilation: any, input: any, fileName: string) {\n  if (input.name !== 'ModuleNotFoundError') {\n    return false;\n  }\n  const stack = await createStackTrace(getProjectRoot(compilation), {\n    loc: input.loc\n      ? input.loc\n      : (input.dependencies || []).map((d: any) => d.loc).filter(Boolean)[0],\n    originalSource: input.module.originalSource(),\n  });\n  // If we could not result the original location we still need to show the existing error\n  if (!stack) {\n    return input;\n  }\n\n  const errorMessage = input.error.message\n    .replace(/ in '.*?'/, '')\n    .replace(/Can't resolve '(.*)'/, `Can't resolve '${chalk.green('$1')}'`);\n\n  return new WebpackFileError(\n    {\n      filePath: fileName,\n      line: stack.lineNumber,\n      col: stack.column,\n    },\n    [\n      chalk.red.bold('Module not found') + `: ${errorMessage}`,\n      stack.frame,\n      getImportTrace(compilation, input),\n      // TODO: FYI\n    ]\n      .filter(Boolean)\n      .join('\\n')\n  );\n}\n\nfunction getImportTrace(compilation: webpack.Compilation, input: any) {\n  if (!isWebpack5) {\n    return '';\n  }\n\n  const projectRoot = getProjectRoot(compilation);\n\n  let importTraceLine = '\\nImport trace for requested module:\\n';\n  const moduleTrace = getModuleTrace(input, compilation);\n\n  for (const { origin } of moduleTrace) {\n    if (!origin.resource) {\n      continue;\n    }\n    const filePath = path.relative(projectRoot, origin.resource);\n    importTraceLine += `./${filePath}\\n`;\n  }\n\n  return importTraceLine + '\\n';\n}\n\n// This can occur in React Native when using require incorrectly:\n// i.e. `(require: any).Systrace = Systrace;` which is valid in Metro.\nexport async function getModuleDependencyWarning(\n  compilation: webpack.Compilation,\n  input: any,\n  fileName: string\n) {\n  if (input.name !== 'ModuleDependencyWarning') {\n    return false;\n  }\n\n  const stack = await createStackTrace(getProjectRoot(compilation), {\n    loc: input.loc,\n    originalSource: input.module.originalSource(),\n  });\n\n  // If we could not result the original location we still need to show the existing error\n  if (!stack) {\n    return input;\n  }\n\n  return new WebpackFileError(\n    {\n      filePath: fileName,\n      line: stack.lineNumber,\n      col: stack.column,\n    },\n    [\n      input.error.message,\n      stack.frame,\n      getImportTrace(compilation, input),\n      // TODO: FYI\n    ]\n      .filter(Boolean)\n      .join('\\n')\n  );\n}\n\nfunction getProjectRoot(compilation: webpack.Compilation): string {\n  // @ts-ignore: Webpack v5/v4\n  return compilation.options.context ?? compilation.context;\n}\n\nasync function createStackTrace(\n  projectRoot: string,\n  { loc, originalSource }: { loc: any; originalSource: any }\n) {\n  try {\n    const result = await createOriginalStackFrame({\n      line: loc?.start?.line,\n      column: loc?.start?.column,\n      source: originalSource,\n      rootDirectory: projectRoot,\n      frameNodeModules: true,\n      frame: {},\n    });\n\n    // If we could not result the original location we still need to show the existing error\n    if (!result) {\n      return null;\n    }\n    return {\n      lineNumber: result.originalStackFrame.lineNumber,\n      column: result.originalStackFrame.column,\n      frame: result.originalCodeFrame ?? '',\n    };\n  } catch (err: any) {\n    console.log('Failed to parse source map:', err);\n    // Don't fail on failure to resolve sourcemaps\n    return null;\n  }\n}\n"]}