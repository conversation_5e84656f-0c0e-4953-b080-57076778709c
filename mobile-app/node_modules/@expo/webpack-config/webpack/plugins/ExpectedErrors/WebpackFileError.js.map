{"version": 3, "file": "WebpackFileError.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/WebpackFileError.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,MAAa,gBAAiB,SAAQ,KAAK;IAIzC,YACE,IAIC,EACD,OAAe;QAEf,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;CACF;AAfD,4CAeC;AAED,SAAS,WAAW,CAAC,MAIpB;IACC,MAAM,QAAQ,GAAG,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnD,OAAO,QAAQ,GAAG,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC1F,CAAC", "sourcesContent": ["import chalk from 'chalk';\n\nexport class WebpackFileError extends Error {\n  // Webpack special cases a public file prop.\n  file: string;\n\n  constructor(\n    file: {\n      filePath: string | null;\n      line?: number | null;\n      col?: number | null;\n    },\n    message: string\n  ) {\n    super(message);\n    this.file = formatPaths(file);\n  }\n}\n\nfunction formatPaths(config: {\n  filePath: string | null;\n  line?: number | null;\n  col?: number | null;\n}) {\n  const filePath = chalk.reset.cyan(config.filePath);\n  return filePath + chalk.gray(`:${[config.line, config.col].filter(Boolean).join(':')}`);\n}\n"]}