{"version": 3, "file": "parseBabelError.js", "sourceRoot": "", "sources": ["../../../src/plugins/ExpectedErrors/parseBabelError.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;;GAUG;AACH,kDAA0B;AAE1B,yDAAsD;AAEtD,SAAgB,aAAa,CAC3B,QAAgB,EAChB,GAGC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,GAAG,CAAC,GAAG,EAAE;QACX,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3C,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO;YACzB,iEAAiE;aAChE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtB,yCAAyC;aACxC,OAAO,CAAC,IAAI,MAAM,CAAC,mBAAmB,UAAU,IAAI,MAAM,kBAAkB,CAAC,EAAE,EAAE,CAAC,CAAC;QAEtF,OAAO,IAAI,mCAAgB,CACzB;YACE,QAAQ,EAAE,QAAQ;YAClB,IAAI,EAAE,UAAU;YAChB,GAAG,EAAE,MAAM;SACZ,EACD,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE,CAAC,CACtD,CAAC;KACH;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAhCD,sCAgCC", "sourcesContent": ["/**\n * Copyright © 2021 650 Industries.\n * Copyright © 2021 Vercel, Inc.\n * Copyright 2014-present <PERSON> and other contributors\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/vercel/next.js/blob/1552b8341e5b512a2827485a4a9689cd429c520e/packages/next/build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts\n * Based on https://github.com/babel/babel/blob/34693d6024da3f026534dd8d569f97ac0109602e/packages/babel-core/src/parser/index.js\n */\nimport chalk from 'chalk';\n\nimport { WebpackFileError } from './WebpackFileError';\n\nexport function getBabelError(\n  fileName: string,\n  err: Error & {\n    code?: 'BABEL_PARSE_ERROR';\n    loc?: { line: number; column: number };\n  }\n): WebpackFileError | false {\n  if (err.code !== 'BABEL_PARSE_ERROR') {\n    return false;\n  }\n\n  if (err.loc) {\n    const lineNumber = Math.max(1, err.loc.line);\n    const column = Math.max(1, err.loc.column);\n\n    const message = err.message\n      // Remove file information, which instead is provided by webpack.\n      .replace(/^.+?: /, '')\n      // Remove column information from message\n      .replace(new RegExp(`[^\\\\S\\\\r\\\\n]*\\\\(${lineNumber}:${column}\\\\)[^\\\\S\\\\r\\\\n]*`), '');\n\n    return new WebpackFileError(\n      {\n        filePath: fileName,\n        line: lineNumber,\n        col: column,\n      },\n      chalk.red.bold('Syntax error').concat(`: ${message}`)\n    );\n  }\n\n  return false;\n}\n"]}