{"version": 3, "file": "ExpoPwaManifestWebpackPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoPwaManifestWebpackPlugin.ts"], "names": [], "mappings": ";;;;;AAAA,kDAA0B;AAE1B,uCAAgD;AAChD,4CAAoB;AAEpB,0FAA0F;AAS1F,MAAqB,4BAA6B,SAAQ,kCAAwB;IAChF,YAAY,UAAkC,EAAE,MAAkB;QAChE,IAAI,SAAc,CAAC;QACnB,IAAI;YACF,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACtC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;aACpF;SACF;QAAC,OAAO,EAAE,OAAO,EAAE,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,qDAAqD,OAAO,EAAE,CAAC,CAAC,CAAC;SAC3F;QAED,IAAI,CAAC,SAAS;YAAE,SAAS,GAAG,IAAA,+BAAoB,EAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAE7D,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IAC/B,CAAC;CACF;AAfD,+CAeC", "sourcesContent": ["import chalk from 'chalk';\nimport type { ExpoConfig } from 'expo/config';\nimport { generateManifestJson } from 'expo-pwa';\nimport fs from 'fs';\n\nimport PwaManifestWebpackPlugin, { PwaManifestOptions } from './PwaManifestWebpackPlugin';\n\nexport type ExpoPwaManifestOptions = PwaManifestOptions & {\n  /**\n   * The path to a template manifest.json.\n   */\n  template: string;\n};\n\nexport default class ExpoPwaManifestWebpackPlugin extends PwaManifestWebpackPlugin {\n  constructor(pwaOptions: ExpoPwaManifestOptions, config: ExpoConfig) {\n    let inputJson: any;\n    try {\n      if (fs.existsSync(pwaOptions.template)) {\n        inputJson = JSON.parse(fs.readFileSync(pwaOptions.template, { encoding: 'utf8' }));\n      }\n    } catch ({ message }) {\n      console.log(chalk.yellow(`\\u203A PWA manifest: failed to use template file: ${message}`));\n    }\n\n    if (!inputJson) inputJson = generateManifestJson({}, config);\n\n    super(pwaOptions, inputJson);\n  }\n}\n"]}