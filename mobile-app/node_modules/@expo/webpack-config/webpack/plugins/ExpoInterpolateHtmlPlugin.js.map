{"version": 3, "file": "ExpoInterpolateHtmlPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoInterpolateHtmlPlugin.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAOH,gCAAmD;AAEnD,oEAAiE;AAEjE,MAAqB,qBAAqB;IAoBxC,YACS,iBAAoC,EACpC,YAAoC;QADpC,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,iBAAY,GAAZ,YAAY,CAAwB;IAC1C,CAAC;IAEJ,KAAK,CAAC,QAAkB;QACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,uBAAuB,CAAC,yBAAyB,CAAC,CAAC;QAE3E,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;YAClE,IAAI,CAAC,iBAAiB;gBACpB,aAAa;iBACZ,QAAQ,CAAC,WAAW,CAAC;iBACrB,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,IAAS,EAAE,EAAE;gBAC/D,mEAAmE;gBACnE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;oBACrC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,WAAW,KAAK,EAAE,CAAC,CAAC;oBACjD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAC3B,IAAI,MAAM,CAAC,GAAG,GAAG,IAAA,uCAAkB,EAAC,GAAG,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,EACpD,KAAK,CACN,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;;AA5CH,wCA6CC;AA5CQ,6BAAO,GAAG,CACf,GAAuE,EACvE,iBAA2C,EACpB,EAAE;;IACzB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,EAAE,UAAU,EAAE,GAAG,IAAA,oBAAc,EAAC,GAAG,CAAC,CAAC;IAE3C,aAAa;IACb,OAAO,IAAI,qBAAqB,CAAC,iBAAiB,EAAE;QAClD,cAAc,EAAE,UAAU;QAC1B,2EAA2E;QAC3E,SAAS,EAAE,MAAA,MAAM,CAAC,GAAG,0CAAE,IAAI;QAC3B,aAAa,EAAE,MAAA,MAAM,CAAC,GAAG,0CAAE,IAAI;QAC/B,oDAAoD;QACpD,SAAS,EAAE,2hBAA2hB;QACtiB,OAAO,EAAE,MAAM;KAChB,CAAC,CAAC;AACL,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @borrows https://github.com/facebook/create-react-app/blob/f0a837c1f07ebd963ddbba2c2937d04fc1b79d40/packages/react-dev-utils/InterpolateHtmlPlugin.js\n */\n\n// Extracted to ensure the `html-webpack-plugin` was always the same.\n\nimport HtmlWebpackPlugin from 'html-webpack-plugin';\nimport { Compiler } from 'webpack';\n\nimport { getConfig, getPublicPaths } from '../env';\nimport { Environment } from '../types';\nimport { escapeStringRegexp } from '../utils/escapeStringRegexp';\n\nexport default class InterpolateHtmlPlugin {\n  static fromEnv = (\n    env: Pick<Environment, 'mode' | 'config' | 'locations' | 'projectRoot'>,\n    htmlWebpackPlugin: typeof HtmlWebpackPlugin\n  ): InterpolateHtmlPlugin => {\n    const config = env.config || getConfig(env);\n    const { publicPath } = getPublicPaths(env);\n\n    // @ts-ignore\n    return new InterpolateHtmlPlugin(htmlWebpackPlugin, {\n      WEB_PUBLIC_URL: publicPath,\n      // @ts-ignore Type 'string | undefined' is not assignable to type 'string'.\n      WEB_TITLE: config.web?.name,\n      LANG_ISO_CODE: config.web?.lang,\n      // These are for legacy ejected web/index.html files\n      NO_SCRIPT: `<form action=\"\" style=\"background-color:#fff;position:fixed;top:0;left:0;right:0;bottom:0;z-index:9999;\"><div style=\"font-size:18px;font-family:Helvetica,sans-serif;line-height:24px;margin:10%;width:80%;\"> <p>Oh no! It looks like JavaScript is not enabled in your browser.</p> <p style=\"margin:20px 0;\"> <button type=\"submit\" style=\"background-color: #4630EB; border-radius: 100px; border: none; box-shadow: none; color: #fff; cursor: pointer; font-weight: bold; line-height: 20px; padding: 6px 16px;\">Reload</button> </p> </div> </form>`,\n      ROOT_ID: 'root',\n    });\n  };\n\n  constructor(\n    public htmlWebpackPlugin: HtmlWebpackPlugin,\n    public replacements: Record<string, string>\n  ) {}\n\n  apply(compiler: Compiler) {\n    const logger = compiler.getInfrastructureLogger('interpolate-html-plugin');\n\n    compiler.hooks.compilation.tap(this.constructor.name, compilation => {\n      this.htmlWebpackPlugin\n        // @ts-ignore\n        .getHooks(compilation)\n        .afterTemplateExecution.tap(this.constructor.name, (data: any) => {\n          // Run HTML through a series of user-specified string replacements.\n          Object.keys(this.replacements).forEach(key => {\n            const value = this.replacements[key];\n            logger.debug(`Replace: \"${key}\" with: ${value}`);\n            data.html = data.html.replace(\n              new RegExp('%' + escapeStringRegexp(key) + '%', 'g'),\n              value\n            );\n          });\n        });\n    });\n  }\n}\n"]}