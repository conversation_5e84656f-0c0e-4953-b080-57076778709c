{"version": 3, "file": "ExpoDefinePlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoDefinePlugin.ts"], "names": [], "mappings": ";;;;;;AACA,mCAAiC;AACjC,oDAA4B;AAC5B,sDAA8B;AAE9B,gCAA4D;AAC5D,8CAAuD;AAGvD,MAAM,0BAA0B,GAAG;IACjC,oEAAoE;IACpE,gBAAgB;IAChB,eAAe;IACf,qBAAqB;IACrB,0BAA0B;IAC1B,KAAK;IACL,SAAS;IACT,+BAA+B;IAC/B,SAAS;IACT,OAAO;IACP,WAAW;IACX,+BAA+B;IAC/B,qBAAqB;CACtB,CAAC;AAEF,SAAS,0BAA0B,CAAC,WAAuB;;IACzD,MAAM,cAAc,GAAwB;QAC1C,GAAG,WAAW;QACd,sDAAsD;QACtD,IAAI,EAAE,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI;QACjD,0DAA0D;QAC1D,GAAG,EAAE;YACH,4DAA4D;YAC5D,uDAAuD;YACvD,gCAAgC;YAChC,MAAM,EAAE,MAAA,WAAW,CAAC,GAAG,0CAAE,MAAM;SAChC;KACF,CAAC;IAEF,KAAK,MAAM,KAAK,IAAI,0BAA0B,EAAE;QAC9C,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;KAC9B;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AASD;;;;;;;;GAQG;AACH,SAAgB,uBAAuB,CACrC,IAAU,EACV,UAAkB,EAClB,iBAA6B,EAC7B,QAAgB;IAEhB,MAAM,WAAW,GAAG,IAAA,aAAO,EAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,WAAW,KAAK,YAAY,CAAC;IAE7C,2DAA2D;IAC3D,gDAAgD;IAChD,MAAM,qBAAqB,GAAG,aAAa,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC;IACzE,MAAM,aAAa,GAAG,qBAAqB,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC;IACxF,MAAM,YAAY,GAAG,0BAA0B,CAAC;IAEhD,MAAM,gBAAgB,GAAG,IAAA,gBAAO,EAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;IAEnF,MAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IAEtD,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;SACxC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACjE,MAAM,CACL,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACX,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1D,OAAO,GAAG,CAAC;IACb,CAAC,EACD;QACE;;;WAGG;QACH,CAAC,GAAG,MAAM,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QAElD;;;;;WAKG;QACH,CAAC,GAAG,MAAM,YAAY,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;QAEnD;;;WAGG;QACH,CAAC,GAAG,MAAM,cAAc,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QAE5D,CAAC,GAAG,MAAM,YAAY,CAAC,EAAE,qBAAU;QACnC,CAAC,GAAG,MAAM,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAC/C,6DAA6D;QAC7D,6DAA6D;QAC7D,CAAC,GAAG,MAAM,iBAAiB,CAAC,EAAE,mBAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAQ,CAAC,CAAC,CAAC,CAAC,SAAS;KACpD,CAC5B,CAAC;IAEJ,IAAI,gBAAgB,EAAE;QACpB,OAAO;YACL,GAAG,UAAU;YACb,OAAO;SACR,CAAC;KACH;IAED,OAAO;QACL,aAAa,EAAE,UAAU;QACzB,OAAO;KACR,CAAC;AACJ,CAAC;AAlED,0DAkEC;AAED;;;;GAIG;AACH,MAAqB,YAAa,SAAQ,iBAAO,CAAC,YAAY;IAgB5D,YAAY,EACV,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,GAMT;QACC,MAAM,iBAAiB,GAAG,0BAA0B,CAAC,MAAM,CAAC,CAAC;QAE7D,MAAM,oBAAoB,GAAG,uBAAuB,CAClD,IAAI,EACJ,SAAS,EACT,iBAAwB,EACxB,QAAQ,CACT,CAAC;QAEF,KAAK,CAAC,oBAAoB,CAAC,CAAC;IAC9B,CAAC;;AArCH,+BAsCC;AArCQ,oCAAuB,GAAG,uBAAuB,CAAC;AAClD,oBAAO,GAAG,CACf,GAAoF,EACtE,EAAE;IAChB,MAAM,IAAI,GAAG,IAAA,aAAO,EAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,oBAAc,EAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAC5C,OAAO,IAAI,YAAY,CAAC;QACtB,IAAI;QACJ,SAAS;QACT,MAAM;QACN,QAAQ,EAAE,GAAG,CAAC,QAAQ;KACvB,CAAC,CAAC;AACL,CAAC,CAAC;AA0BJ,SAAS,aAAa,CAAC,GAAmC,EAAE,UAAkB;IAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;QACnB,OAAO,KAAK,CAAC;KACd;IAED,IAAI,GAAG,CAAC,UAAU,KAAK,aAAa,EAAE;QACpC,OAAO,KAAK,CAAC;KACd;IAED,IAAI;QACF,OAAO,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;KAC/C;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\nimport { boolish } from 'getenv';\nimport semver from 'semver';\nimport webpack from 'webpack';\n\nimport { getConfig, getMode, getPublicPaths } from '../env';\nimport { EXPO_DEBUG, sockPath } from '../env/defaults';\nimport { Environment, ExpoPlatform, Mode } from '../types';\n\nconst RESTRICTED_MANIFEST_FIELDS = [\n  // Omit app.json properties that get removed during the native build\n  'facebookScheme',\n  'facebookAppId',\n  'facebookDisplayName',\n  // Remove iOS and Android.\n  'ios',\n  'android',\n  // Hide internal / build values\n  'plugins',\n  'hooks',\n  '_internal',\n  // Remove metro-specific values\n  'assetBundlePatterns',\n];\n\nfunction createEnvironmentConstants(appManifest: ExpoConfig) {\n  const publicManifest: Record<string, any> = {\n    ...appManifest,\n    // @ts-ignore: displayName doesn't exist on ExpoConfig\n    name: appManifest.displayName || appManifest.name,\n    // Use the PWA `manifest.json` as the native web manifest.\n    web: {\n      // Pass through config properties that are not stored in the\n      // PWA `manifest.json`, but still need to be accessible\n      // through `Constants.manifest`.\n      config: appManifest.web?.config,\n    },\n  };\n\n  for (const field of RESTRICTED_MANIFEST_FIELDS) {\n    delete publicManifest[field];\n  }\n  return publicManifest;\n}\n\n/**\n * @internal\n */\nexport interface ClientEnv {\n  [key: string]: any;\n}\n\n/**\n * Create the global environment variables to surface in the project. Also creates the `__DEV__` boolean to provide better parity with Metro bundler.\n *\n * @param mode defines the Metro bundler `global.__DEV__` value.\n * @param publicPath passed as `process.env.PUBLIC_URL` to the app.\n * @param nativeAppManifest public values to be used in `expo-constants`.\n * @param platform native platform.\n * @internal\n */\nexport function createClientEnvironment(\n  mode: Mode,\n  publicPath: string,\n  nativeAppManifest: ExpoConfig,\n  platform: string\n): ClientEnv {\n  const environment = getMode({ mode });\n  const __DEV__ = environment !== 'production';\n\n  // Adding the env variables to the Expo manifest is unsafe.\n  // This feature is deprecated in SDK 41 forward.\n  const isEnvBindingSupported = lteSdkVersion(nativeAppManifest, '40.0.0');\n  const ENV_VAR_REGEX = isEnvBindingSupported ? /^(EXPO_|REACT_NATIVE_|CI$)/i : /^(CI$)/i;\n  const SECRET_REGEX = /(PASSWORD|SECRET|TOKEN)/i;\n\n  const shouldDefineKeys = boolish('EXPO_WEBPACK_DEFINE_ENVIRONMENT_AS_KEYS', false);\n\n  const prefix = shouldDefineKeys ? 'process.env.' : '';\n\n  const processEnv = Object.keys(process.env)\n    .filter(key => ENV_VAR_REGEX.test(key) && !SECRET_REGEX.test(key))\n    .reduce(\n      (env, key) => {\n        env[`${prefix}${key}`] = JSON.stringify(process.env[key]);\n        return env;\n      },\n      {\n        /**\n         * Useful for determining whether we’re running in production mode.\n         * Most importantly, it switches React into the correct mode.\n         */\n        [`${prefix}NODE_ENV`]: JSON.stringify(environment),\n\n        /**\n         * Useful for resolving the correct path to static assets in `public`.\n         * For example, <img src={process.env.PUBLIC_URL + '/img/logo.png'} />.\n         * This should only be used as an escape hatch. Normally you would put\n         * images into the root folder and `import` them in code to get their paths.\n         */\n        [`${prefix}PUBLIC_URL`]: JSON.stringify(publicPath),\n\n        /**\n         * Surfaces the `app.json` (config) as an environment variable which is then parsed by\n         * `expo-constants` https://docs.expo.dev/versions/latest/sdk/constants/\n         */\n        [`${prefix}APP_MANIFEST`]: JSON.stringify(nativeAppManifest),\n\n        [`${prefix}EXPO_DEBUG`]: EXPO_DEBUG,\n        [`${prefix}PLATFORM`]: JSON.stringify(platform),\n        // [`${prefix}WDS_SOCKET_HOST`]: process.env.WDS_SOCKET_HOST,\n        // [`${prefix}WDS_SOCKET_PORT`]: process.env.WDS_SOCKET_PORT,\n        [`${prefix}WDS_SOCKET_PATH`]: sockPath ? JSON.stringify(sockPath) : undefined,\n      } as Record<string, string>\n    );\n\n  if (shouldDefineKeys) {\n    return {\n      ...processEnv,\n      __DEV__,\n    };\n  }\n\n  return {\n    'process.env': processEnv,\n    __DEV__,\n  };\n}\n\n/**\n * Required for `expo-constants` https://docs.expo.dev/versions/latest/sdk/constants/.\n * This surfaces the `app.json` (config) as an environment variable which is then parsed by `expo-constants`.\n * @category plugins\n */\nexport default class DefinePlugin extends webpack.DefinePlugin {\n  static createClientEnvironment = createClientEnvironment;\n  static fromEnv = (\n    env: Pick<Environment, 'projectRoot' | 'mode' | 'config' | 'locations' | 'platform'>\n  ): DefinePlugin => {\n    const mode = getMode(env);\n    const { publicUrl } = getPublicPaths(env);\n    const config = env.config || getConfig(env);\n    return new DefinePlugin({\n      mode,\n      publicUrl,\n      config,\n      platform: env.platform,\n    });\n  };\n\n  constructor({\n    mode,\n    publicUrl,\n    config,\n    platform,\n  }: {\n    mode: Mode;\n    publicUrl: string;\n    config: ExpoConfig;\n    platform: ExpoPlatform;\n  }) {\n    const publicAppManifest = createEnvironmentConstants(config);\n\n    const environmentVariables = createClientEnvironment(\n      mode,\n      publicUrl,\n      publicAppManifest as any,\n      platform\n    );\n\n    super(environmentVariables);\n  }\n}\n\nfunction lteSdkVersion(exp: Pick<ExpoConfig, 'sdkVersion'>, sdkVersion: string): boolean {\n  if (!exp.sdkVersion) {\n    return false;\n  }\n\n  if (exp.sdkVersion === 'UNVERSIONED') {\n    return false;\n  }\n\n  try {\n    return semver.lte(exp.sdkVersion, sdkVersion);\n  } catch {\n    return false;\n  }\n}\n"]}