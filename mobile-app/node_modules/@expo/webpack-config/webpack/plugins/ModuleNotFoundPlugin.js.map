{"version": 3, "file": "ModuleNotFoundPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ModuleNotFoundPlugin.ts"], "names": [], "mappings": ";;;;;;AAAA;;;;;;;;;GASG;AACH,kDAA0B;AAC1B,qCAA6C;AAC7C,gDAAwB;AAWxB,SAAS,qBAAqB,CAAC,KAAU;IACvC,OAAO,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,qBAAqB,CAAC;AAC/C,CAAC;AAED,MAAa,oBAAoB;IAC/B,YAAoB,OAAe,EAAU,YAAqB;QAA9C,YAAO,GAAP,OAAO,CAAQ;QAAU,iBAAY,GAAZ,YAAY,CAAS;QAChE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAEO,cAAc;QACpB,IAAI;YACF,OAAO,IAAA,cAAU,EAAC,WAAW,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC;SAC/D;QAAC,MAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAEO,eAAe,CAAC,KAAa;QACnC,IAAI,IAAI,GAAG,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9C,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,GAAG,KAAK,CAAC;SACd;aAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAChC,IAAI,GAAG,GAAG,GAAG,cAAI,CAAC,GAAG,GAAG,IAAI,CAAC;SAC9B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,aAAa,CAAC,GAAwB;;QAC5C,MAAM,EAAE,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC;QAE/C,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,MAAM,eAAe,GACnB,GAAG,CAAC,OAAO,IAAI,iDAAiD,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACrF,IAAI,eAAe,EAAE;gBACnB,MAAM,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,GAAG,eAAe,CAAC;gBACtD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;gBAC5F,MAAM,aAAa,GAAG,cAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACnD,GAAG,CAAC,OAAO,GAAG,sBAAsB,aAAa,qDAAqD,UAAU,IAAI,CAAC;aACtH;YACD,OAAO,GAAG,CAAC;SACZ;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACnD,wCAAwC;QACxC,IAAI,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEnC,MAAM,OAAO,GAAG,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,OAAO,EAAE;YACX,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7D,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEzD,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;YAClC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,QAAQ,EAAE;gBACZ,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBACrC,OAAO,GAAG;oBACR,wBAAwB,MAAM,yCAAyC;oBACvE,EAAE;oBACF,2CAA2C;wBACzC,CAAC,MAAM,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,eAAK,CAAC,IAAI,CAAC,eAAe,MAAM,EAAE,CAAC,CAAC;wBACjF,GAAG;iBACN,CAAC;aACH;iBAAM,IAAI,MAAM,EAAE;gBACjB,OAAO,GAAG,CAAC,qBAAqB,MAAM,SAAS,OAAO,IAAI,CAAC,CAAC;aAC7D;iBAAM;gBACL,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACzB;SACF;aAAM;YACL,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;SACzB;QACD,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAEnE,MAAM,wBAAwB,GAAG,MAAA,GAAG,CAAC,KAAK,0CAAE,qBAAqB,CAAC;QAClE,IAAI,wBAAwB,EAAE;YAC5B,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;SAC7D;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,KAAK,CAAC,QAAkB;QACtB,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,CAAC;QAC/B,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;YAC5B,QAAQ,CAAC,GAAG;gBACV,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,kBAAkB,IAAI,GAAG,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE;oBACpE,OAAO,GAAG,CAAC;iBACZ;gBACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,EAAE;oBAC5B,EAAE,EAAE,CAAC,WAAwB,EAAE,QAAkD,EAAE,EAAE;wBACnF,GAAG,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,GAAiB,EAAE,GAAG,IAAe,EAAE,EAAE;4BAC5D,IAAI,qBAAqB,CAAC,GAAG,CAAC,EAAE;gCAC9B,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;6BAC1B;4BACD,QAAQ,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;wBACzB,CAAC,CAAC,CAAC;oBACL,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAjGD,oDAiGC", "sourcesContent": ["/**\n * Copyright (c) 2022 Expo, Inc.\n * Copyright (c) 2015-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/facebook/create-react-app/blob/f0a837c/packages/react-dev-utils/ModuleNotFoundPlugin.js\n * But with Node LTS support and removed support for CaseSensitivePathsPlugin which we don't implement due to performance concerns.\n */\nimport chalk from 'chalk';\nimport { sync as findUpSync } from 'find-up';\nimport path from 'path';\nimport { Compilation, Compiler, WebpackError } from 'webpack';\n\ntype ModuleNotFoundError = WebpackError & {\n  details?: string;\n  error?: any;\n  resource: string;\n  origin: { resource: string };\n  message: string;\n};\n\nfunction isModuleNotFoundError(error: any): error is ModuleNotFoundError {\n  return error?.name === 'ModuleNotFoundError';\n}\n\nexport class ModuleNotFoundPlugin {\n  constructor(private appPath: string, private yarnLockFile?: string) {\n    this.useYarnCommand = this.useYarnCommand.bind(this);\n    this.getRelativePath = this.getRelativePath.bind(this);\n    this.prettierError = this.prettierError.bind(this);\n  }\n\n  private useYarnCommand() {\n    try {\n      return findUpSync('yarn.lock', { cwd: this.appPath }) != null;\n    } catch {\n      return false;\n    }\n  }\n\n  private getRelativePath(_file: string) {\n    let file = path.relative(this.appPath, _file);\n    if (file.startsWith('..')) {\n      file = _file;\n    } else if (!file.startsWith('.')) {\n      file = '.' + path.sep + file;\n    }\n    return file;\n  }\n\n  private prettierError(err: ModuleNotFoundError) {\n    const { details: _details = '', origin } = err;\n\n    if (origin == null) {\n      const caseSensitivity =\n        err.message && /\\[CaseSensitivePathsPlugin\\] `(.*?)` .* `(.*?)`/.exec(err.message);\n      if (caseSensitivity) {\n        const [, incorrectPath, actualName] = caseSensitivity;\n        const actualFile = this.getRelativePath(path.join(path.dirname(incorrectPath), actualName));\n        const incorrectName = path.basename(incorrectPath);\n        err.message = `Cannot find file: '${incorrectName}' does not match the corresponding name on disk: '${actualFile}'.`;\n      }\n      return err;\n    }\n\n    const file = this.getRelativePath(origin.resource);\n    // TODO: This looks like a type error...\n    let details = _details.split('\\n');\n\n    const request = /resolve '(.*?)' in '(.*?)'/.exec(details);\n    if (request) {\n      const isModule = details[1] && details[1].includes('module');\n      const isFile = details[1] && details[1].includes('file');\n\n      let [, target, context] = request;\n      context = this.getRelativePath(context);\n      if (isModule) {\n        const isYarn = this.useYarnCommand();\n        details = [\n          `Cannot find module: '${target}'. Make sure this package is installed.`,\n          '',\n          'You can install this package by running: ' +\n            (isYarn ? chalk.bold(`yarn add ${target}`) : chalk.bold(`npm install ${target}`)) +\n            '.',\n        ];\n      } else if (isFile) {\n        details = [`Cannot find file '${target}' in '${context}'.`];\n      } else {\n        details = [err.message];\n      }\n    } else {\n      details = [err.message];\n    }\n    err.message = [file, ...details].join('\\n').replace('Error: ', '');\n\n    const isModuleScopePluginError = err.error?.__module_scope_plugin;\n    if (isModuleScopePluginError) {\n      err.message = err.message.replace('Module not found: ', '');\n    }\n    return err;\n  }\n\n  apply(compiler: Compiler) {\n    const { prettierError } = this;\n    compiler.hooks.make.intercept({\n      register(tap) {\n        if (!(tap.name === 'MultiEntryPlugin' || tap.name === 'EntryPlugin')) {\n          return tap;\n        }\n        return Object.assign({}, tap, {\n          fn: (compilation: Compilation, callback: (error: any, ...args: unknown[]) => void) => {\n            tap.fn(compilation, (err: WebpackError, ...args: unknown[]) => {\n              if (isModuleNotFoundError(err)) {\n                err = prettierError(err);\n              }\n              callback(err, ...args);\n            });\n          },\n        });\n      },\n    });\n  }\n}\n"]}