{"version": 3, "file": "ExpoProgressBarPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/ExpoProgressBarPlugin.ts"], "names": [], "mappings": ";;;;;AACA,sDAA8B;AAE9B,MAAqB,UAAW,SAAQ,iBAAO,CAAC,cAAc;IAc5D,YACS,KAUN;QAED,KAAK,CAAC,UAAU,CAAC,EAAE;YACjB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YACzB,IAAI,UAAU,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;aAC9D;iBAAM,IAAI,UAAU,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,SAAS,CAAC,sBAAsB,EAAE;oBACrC,UAAU;oBACV,OAAO;oBACP,aAAa,EAAE,KAAK,CAAC,aAAa;iBACnC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,SAAS,CAAC,6BAA6B,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;aACxE;QACH,CAAC,CAAC,CAAC;QAzBI,UAAK,GAAL,KAAK,CAUX;QAxBH,cAAS,GAAG,CAAC,IAAY,EAAE,KAAU,EAAE,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CACpB,EAAE,GAAG,EAAE,OAAO,EAAE,EAChB,IAAI,CAAC,SAAS,CAAC;gBACb,GAAG,EAAE,OAAO;gBACZ,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE;gBACd,UAAU,EAAE,KAAK;gBACjB,IAAI,EAAE,IAAI;gBACV,GAAG,KAAK;aACT,CAAC,CACH,CAAC;QACJ,CAAC,CAAC;QA+BF,6BAA6B;QAC7B,uBAAkB,GAAG,GAAG,CAAC;QAMzB,YAAO,GAAW,EAAE,CAAC;IATrB,CAAC;IAKD,aAAa;QACX,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAClD,CAAC;IAID,KAAK,CAAC,QAA0B;QAC9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEpC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACtB,qEAAqE;QACrE,yEAAyE;QACzE,wEAAwE;QACxE,4EAA4E;QAC5E,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,EAAE;YACzC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,GAAG,KAAK,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;;YAC5C,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC;gBAC7B,GAAG,EAAE,KAAK;gBACV,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE;gBACrB,MAAM,WAAW,GAAG,SAAS,CAAC,MAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAChD,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,cAAc;qBACrB,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE;oBACpC,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;iBACxC,CAAC,CAAC;gBAEH,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;oBAC/B,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE;wBAC/B,KAAK;qBACN,CAAC,CAAC;iBACJ;gBAED,OAAO;aACR;YAED,yCAAyC;YACzC,IAAI,MAAA,SAAS,CAAC,QAAQ,0CAAE,MAAM,EAAE;gBAC9B,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,QAAQ,EAAE;oBACxC,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE;wBACjC,OAAO;qBACR,CAAC,CAAC;iBACJ;aACF;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvGD,6BAuGC", "sourcesContent": ["import Log from '@expo/bunyan';\nimport webpack from 'webpack';\n\nexport default class WebpackBar extends webpack.ProgressPlugin {\n  sendEvent = (name: string, props: any) => {\n    this.props.logger.info(\n      { tag: 'metro' },\n      JSON.stringify({\n        tag: 'metro',\n        id: Date.now(),\n        shouldHide: false,\n        type: name,\n        ...props,\n      })\n    );\n  };\n\n  constructor(\n    public props: {\n      logger: Log;\n      nonInteractive?: boolean;\n      bundleDetails: {\n        bundleType: 'bundle' | 'delta' | 'meta' | 'map' | 'ram' | 'cli' | 'hmr' | 'todo' | 'graph';\n        minify?: boolean;\n        dev?: boolean;\n        entryFile?: string | null;\n        platform?: string;\n      };\n    }\n  ) {\n    super(percentage => {\n      const { buildID } = this;\n      if (percentage === 1) {\n        this.sendEvent('bundle_build_done', { percentage, buildID });\n      } else if (percentage === 0) {\n        this.sendEvent('bundle_build_started', {\n          percentage,\n          buildID,\n          bundleDetails: props.bundleDetails,\n        });\n      } else {\n        this.sendEvent('bundle_transform_progressed', { percentage, buildID });\n      }\n    });\n  }\n\n  // Add some offset from Metro\n  _nextBundleBuildID = 999;\n\n  getNewBuildID(): string {\n    return (this._nextBundleBuildID++).toString(36);\n  }\n\n  buildID: string = '';\n\n  apply(compiler: webpack.Compiler) {\n    this.buildID = this.getNewBuildID();\n\n    super.apply(compiler);\n    // \"invalid\" event fires when you have changed a file, and Webpack is\n    // recompiling a bundle. WebpackDevServer takes care to pause serving the\n    // bundle, so if you refresh, it'll wait instead of serving the old one.\n    // \"invalid\" is short for \"bundle invalidated\", it doesn't imply any errors.\n    compiler.hooks.invalid.tap('invalid', () => {\n      this.props.bundleDetails.bundleType = 'hmr';\n    });\n\n    compiler.hooks.done.tap('done', async stats => {\n      const statsData = stats.toJson({\n        all: false,\n        warnings: true,\n        errors: true,\n      });\n\n      if (stats.hasErrors()) {\n        const metroErrors = statsData.errors!.map(error => {\n          return {\n            message: error,\n            name: 'WebpackError',\n          };\n        });\n\n        this.sendEvent('bundle_build_failed', {\n          buildID: this.buildID,\n          bundleOptions: this.props.bundleDetails,\n        });\n\n        for (const error of metroErrors) {\n          this.sendEvent('bundling_error', {\n            error,\n          });\n        }\n\n        return;\n      }\n\n      // Show warnings if no errors were found.\n      if (statsData.warnings?.length) {\n        for (const warning of statsData.warnings) {\n          this.sendEvent('bundling_warning', {\n            warning,\n          });\n        }\n      }\n    });\n  }\n}\n"]}