{"version": 3, "file": "withAlias.js", "sourceRoot": "", "sources": ["../../src/addons/withAlias.ts"], "names": [], "mappings": ";;AAEA;;;;;;GAMG;AACH,SAAwB,SAAS,CAC/B,aAA4B,EAC5B,QAAmC,EAAE;IAErC,iBAAiB;IACjB,IAAI,CAAC,aAAa,CAAC,OAAO;QAAE,aAAa,CAAC,OAAO,GAAG,EAAE,CAAC;IACvD,aAAa,CAAC,OAAO,CAAC,KAAK,GAAG;QAC5B,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;QACtC,GAAG,KAAK;KACT,CAAC;IAEF,OAAO,aAAa,CAAC;AACvB,CAAC;AAZD,4BAYC", "sourcesContent": ["import { Configuration } from 'webpack';\n\n/**\n * Apply aliases to a Webpack config\n *\n * @param webpackConfig Existing Webpack config to modify.\n * @param alias Extra aliases to inject\n * @category addons\n */\nexport default function with<PERSON>lias(\n  webpackConfig: Configuration,\n  alias: { [key: string]: string } = {}\n): Configuration {\n  // Mix in aliases\n  if (!webpackConfig.resolve) webpackConfig.resolve = {};\n  webpackConfig.resolve.alias = {\n    ...(webpackConfig.resolve.alias || {}),\n    ...alias,\n  };\n\n  return webpackConfig;\n}\n"]}