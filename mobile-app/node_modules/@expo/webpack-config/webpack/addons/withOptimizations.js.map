{"version": 3, "file": "withOptimizations.js", "sourceRoot": "", "sources": ["../../src/addons/withOptimizations.ts"], "names": [], "mappings": ";;;;;;AAAA,gGAA8D;AAC9D,mCAAiC;AACjC,oDAA2B;AAC3B,kFAAiD;AAGjD;;;GAGG;AACH,SAAgB,WAAW;IACzB,OAAO,IAAA,gBAAO,EAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC1C,CAAC;AAFD,kCAEC;AAED;;;;;GAKG;AACH,SAAwB,iBAAiB,CAAC,aAA4B;IACpE,IAAI,aAAa,CAAC,IAAI,KAAK,YAAY,EAAE;QACvC,aAAa,CAAC,YAAY,GAAG;YAC3B,GAAG,aAAa,CAAC,YAAY;YAC7B,oEAAoE;YACpE,6EAA6E;YAC7E,WAAW,EAAE,KAAK;SACnB,CAAC;QACF,OAAO,aAAa,CAAC;KACtB;IAED,MAAM,YAAY,GAAG,WAAW,EAAE,CAAC;IAEnC,aAAa,CAAC,YAAY,GAAG;QAC3B,oEAAoE;QACpE,6EAA6E;QAC7E,WAAW,EAAE,KAAK;QAClB,GAAG,CAAC,aAAa,CAAC,YAAY,IAAI,EAAE,CAAC;QAErC,2FAA2F;QAC3F,WAAW,EAAE;YACX,MAAM,EAAE,KAAK;SACd;QACD,OAAO,EAAE,KAAK;QACd,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;YACT,uCAAuC;YACvC,aAAa;YACb,IAAI,+BAAY,CAAC;gBACf,aAAa,EAAE;oBACb,KAAK,EAAE;wBACL,iEAAiE;wBACjE,+DAA+D;wBAC/D,oEAAoE;wBACpE,2DAA2D;wBAC3D,yDAAyD;wBACzD,IAAI,EAAE,IAAI;qBACX;oBACD,QAAQ,EAAE;wBACR,IAAI,EAAE,CAAC;wBACP,QAAQ,EAAE,KAAK;wBACf,0EAA0E;wBAC1E,2DAA2D;wBAC3D,iCAAiC;wBACjC,kDAAkD;wBAClD,WAAW,EAAE,KAAK;wBAClB,gEAAgE;wBAChE,2DAA2D;wBAC3D,gCAAgC;wBAChC,iDAAiD;wBACjD,MAAM,EAAE,CAAC;qBACV;oBACD,MAAM,EAAE,YAAY;wBAClB,CAAC,CAAC,KAAK;wBACP,CAAC,CAAC;4BACE,QAAQ,EAAE,IAAI;yBACf;oBACL,eAAe,EAAE,YAAY;oBAC7B,WAAW,EAAE,YAAY;oBACzB,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC;wBACP,QAAQ,EAAE,YAAY;wBACtB,2EAA2E;wBAC3E,2DAA2D;wBAC3D,UAAU,EAAE,IAAI;qBACjB;iBACF;gBACD,gEAAgE;gBAChE,0DAA0D;gBAC1D,4EAA4E;gBAC5E,qEAAqE;gBACrE,QAAQ,EAAE,CAAC,gBAAK;aACjB,CAAC;YACF,uCAAuC;YACvC,IAAI,sCAAkB,EAAE;SACzB;QACD,uHAAuH;QACvH,YAAY,EAAE,KAAK;KACpB,CAAC;IAEF,OAAO,aAAa,CAAC;AACvB,CAAC;AAjFD,oCAiFC", "sourcesContent": ["import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';\nimport { boolish } from 'getenv';\nimport isWsl from 'is-wsl';\nimport TerserPlugin from 'terser-webpack-plugin';\nimport { Configuration } from 'webpack';\n\n/**\n * Returns `true` if the Expo web environment variable enabled.\n * @internal\n */\nexport function isDebugMode(): boolean {\n  return boolish('EXPO_WEB_DEBUG', false);\n}\n\n/**\n * Add the minifier and other optimizations for production builds.\n *\n * @param webpackConfig Existing Webpack config to modify.\n * @category addons\n */\nexport default function withOptimizations(webpackConfig: Configuration): Configuration {\n  if (webpackConfig.mode !== 'production') {\n    webpackConfig.optimization = {\n      ...webpackConfig.optimization,\n      // Required for React Native packages that use import/export syntax:\n      // https://webpack.js.org/configuration/optimization/#optimizationusedexports\n      usedExports: false,\n    };\n    return webpackConfig;\n  }\n\n  const _isDebugMode = isDebugMode();\n\n  webpackConfig.optimization = {\n    // Required for React Native packages that use import/export syntax:\n    // https://webpack.js.org/configuration/optimization/#optimizationusedexports\n    usedExports: false,\n    ...(webpackConfig.optimization || {}),\n\n    // https://github.com/facebook/create-react-app/discussions/11278#discussioncomment-1808511\n    splitChunks: {\n      chunks: 'all',\n    },\n    nodeEnv: false,\n    minimize: true,\n    minimizer: [\n      // This is only used in production mode\n      // @ts-ignore\n      new TerserPlugin({\n        terserOptions: {\n          parse: {\n            // We want terser to parse ecma 8 code. However, we don't want it\n            // to apply any minification steps that turns valid ecma 5 code\n            // into invalid ecma 5 code. This is why the 'compress' and 'output'\n            // sections only apply transformations that are ecma 5 safe\n            // https://github.com/facebook/create-react-app/pull/4234\n            ecma: 2018,\n          },\n          compress: {\n            ecma: 5,\n            warnings: false,\n            // Disabled because of an issue with Uglify breaking seemingly valid code:\n            // https://github.com/facebook/create-react-app/issues/2376\n            // Pending further investigation:\n            // https://github.com/mishoo/UglifyJS2/issues/2011\n            comparisons: false,\n            // Disabled because of an issue with Terser breaking valid code:\n            // https://github.com/facebook/create-react-app/issues/5250\n            // Pending futher investigation:\n            // https://github.com/terser-js/terser/issues/120\n            inline: 2,\n          },\n          mangle: _isDebugMode\n            ? false\n            : {\n                safari10: true,\n              },\n          keep_classnames: _isDebugMode,\n          keep_fnames: _isDebugMode,\n          output: {\n            ecma: 5,\n            comments: _isDebugMode,\n            // Turned on because emoji and regex is not minified properly using default\n            // https://github.com/facebook/create-react-app/issues/2488\n            ascii_only: true,\n          },\n        },\n        // Use multi-process parallel running to improve the build speed\n        // Default number of concurrent runs: os.cpus().length - 1\n        // Disabled on WSL (Windows Subsystem for Linux) due to an issue with Terser\n        // https://github.com/webpack-contrib/terser-webpack-plugin/issues/21\n        parallel: !isWsl,\n      }),\n      // This is only used in production mode\n      new CssMinimizerPlugin(),\n    ],\n    // Skip the emitting phase whenever there are errors while compiling. This ensures that no erroring assets are emitted.\n    emitOnErrors: false,\n  };\n\n  return webpackConfig;\n}\n"]}