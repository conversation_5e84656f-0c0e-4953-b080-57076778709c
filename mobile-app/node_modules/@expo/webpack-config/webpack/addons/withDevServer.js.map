{"version": 3, "file": "withDevServer.js", "sourceRoot": "", "sources": ["../../src/addons/withDevServer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAA4B;AAC5B,kDAA0B;AAC1B,oDAA4B;AAC5B,wDAA0B;AAC1B,2CAA6B;AAQ7B,gCAAkD;AAClD,8CAAqE;AAErE,8EAAiF;AACjF,wDAAqD;AACrD,sFAAyF;AACzF,wFAA2F;AAE3F,+DAA+D;AAC/D,+BAA+B;AAC/B,SAAS,mBAAmB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAO;IAC/D,IAAI,SAAS,CAAC;IACd,IAAI;QACF,yDAAyD;QACzD,SAAS,GAAG,gBAAM,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;KAC7D;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;IAED,IAAI;QACF,yDAAyD;QACzD,gBAAM,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;KACvC;IAAC,MAAM;QACN,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,mDAAmD;AACnD,SAAS,WAAW,CAAC,IAAY,EAAE,IAAY;IAC7C,IAAI,CAAC,kBAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACxB,MAAM,IAAI,KAAK,CACb,iBAAiB,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,eAAK,CAAC,MAAM,CAC1E,IAAI,CACL,mBAAmB,CACrB,CAAC;KACH;IACD,OAAO,kBAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAED,uBAAuB;AACvB,qEAAqE;AACrE,SAAS,cAAc,CAAC,WAAmB,EAAE,OAAgB;IAC3D,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;IAEnD,IAAI,OAAO,IAAI,YAAY,IAAI,YAAY,EAAE;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;QACxD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC;YAC1C,GAAG,EAAE,WAAW,CAAC,OAAO,EAAE,cAAc,CAAC;SAC1C,CAAC;QAEF,IAAI,mBAAmB,CAAC,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE;YACxD,OAAO,MAAM,CAAC;SACf;aAAM;YACL,OAAO,CAAC,GAAG,CACT,eAAK,CAAC,MAAM,CACV,2LAA2L,CAC5L,CACF,CAAC;YACF,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AASD;;;;;;;GAOG;AACH,SAAwB,aAAa,CACnC,aAA4B,EAC5B,GAAiB,EACjB,UAA4B,EAAE;IAE9B,aAAa,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACxD,OAAO,aAAa,CAAC;AACvB,CAAC;AAPD,gCAOC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAC7B,GAAiB,EACjB,EAAE,WAAW,EAAE,KAAK,KAAuB,EAAE;IAE7C,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,GAAG,GAAG,CAAC;IAC9B,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAA,cAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAClE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,GAAG,IAAA,oBAAc,EAAC,GAAG,CAAC,CAAC;IAE5D,MAAM,eAAe,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,KAAK,MAAM,CAAC;IAExF,wEAAwE;IACxE,oHAAoH;IACpH,OAAO;QACL,wEAAwE;QACxE,2EAA2E;QAC3E,2DAA2D;QAC3D,wFAAwF;QACxF,2EAA2E;QAC3E,2EAA2E;QAC3E,2DAA2D;QAC3D,2DAA2D;QAC3D,qEAAqE;QACrE,4EAA4E;QAC5E,2EAA2E;QAC3E,wEAAwE;QACxE,2EAA2E;QAC3E,wEAAwE;QACxE,wEAAwE;QACxE,qEAAqE;QACrE,2EAA2E;QAC3E,0EAA0E;QAC1E,YAAY,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,OAAO,CAAc;QACrF,8CAA8C;QAC9C,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE;YACN,2EAA2E;YAC3E,4EAA4E;YAC5E,4EAA4E;YAC5E,0EAA0E;YAC1E,wEAAwE;YACxE,2EAA2E;YAC3E,2EAA2E;YAC3E,yEAAyE;YACzE,oDAAoD;YACpD,uEAAuE;YACvE,wEAAwE;YACxE,wEAAwE;YACxE,4EAA4E;YAC5E,yEAAyE;YACzE,SAAS,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM;YACpC,UAAU,EAAE,CAAC,eAAe,CAAC;YAC7B,sEAAsE;YACtE,KAAK,EAAE;gBACL,wDAAwD;gBACxD,0DAA0D;gBAC1D,8DAA8D;gBAC9D,2DAA2D;gBAC3D,OAAO,EAAE,IAAA,2BAAY,EAAC,SAAS,CAAC,IAAI,CAAC;aACtC;SACF;QAED,MAAM,EAAE;YACN,YAAY,EAAE;gBACZ,kFAAkF;gBAClF,4EAA4E;gBAC5E,2BAA2B;gBAC3B,QAAQ,EAAE,mBAAQ;gBAClB,QAAQ,EAAE,mBAAQ;gBAClB,IAAI,EAAE,mBAAQ;aACf;YACD,iBAAiB;YACjB,OAAO,EAAE,KAAK;YACd,6DAA6D;YAC7D,mBAAmB;SACpB;QAED,eAAe,EAAE;YACf,IAAI,EAAE,IAAI;YACV,OAAO,EAAE;gBACP,IAAI,EAAE,mBAAQ;gBACd,IAAI,EAAE,mBAAQ;gBACd,IAAI,EAAE,mBAAQ;aACf;SACF;QACD,aAAa,EAAE;YACb,KAAK,EAAE,IAAI;YACX,gFAAgF;YAChF,+EAA+E;YAC/E,iBAAiB;YACjB,oEAAoE;YACpE,UAAU,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzC;QAED,KAAK,EAAE,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;QAC7C,IAAI,EAAJ,eAAI;QACJ,kBAAkB,EAAE;YAClB,yDAAyD;YACzD,+DAA+D;YAC/D,cAAc,EAAE,IAAI;YACpB,KAAK,EAAE,eAAe;SACvB;QAED,yEAAyE;QACzE,KAAK;QACL,gBAAgB,CAAC,WAAW,EAAE,SAAS;YACrC,IAAA,gBAAM,EAAC,SAAS,EAAE,mCAAmC,CAAC,CAAC;YAEvD,WAAW,CAAC,OAAO;YACjB,iCAAiC;YACjC,6EAA6E;YAC7E,wEAAwE;YACxE,IAAA,uDAA6B,EAAC,SAAS,CAAC;YACxC,8EAA8E;YAC9E,IAAA,iEAAkC,EAAC,eAAe,CAAC;YACnD,wEAAwE;YACxE,yEAAyE;YACzE,qEAAqE;YACrE,kCAAkC;YAClC,kFAAkF;YAClF,IAAA,+DAAiC,EAAC,eAAe,CAAC,CACnD,CAAC;YAEF,OAAO,WAAW,CAAC;QACrB,CAAC;KACF,CAAC;AACJ,CAAC;AA7HD,0CA6HC", "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\nimport crypto from 'crypto';\nimport fs from 'fs-extra';\nimport * as path from 'path';\nimport { Configuration } from 'webpack';\nimport {\n  ProxyConfigArray,\n  ProxyConfigMap,\n  Configuration as WebpackDevServerConfiguration,\n} from 'webpack-dev-server';\n\nimport { getPaths, getPublicPaths } from '../env';\nimport { host, sockHost, sockPath, sockPort } from '../env/defaults';\nimport { Environment } from '../types';\nimport { createEvalSourceMapMiddleware } from '../utils/evalSourceMapMiddleware';\nimport { ignoredFiles } from '../utils/ignoredFiles';\nimport { createNoopServiceWorkerMiddleware } from '../utils/noopServiceWorkerMiddleware';\nimport { createRedirectServedPathMiddleware } from '../utils/redirectServedPathMiddleware';\n\n// Ensure the certificate and key provided are valid and if not\n// throw an easy to debug error\nfunction validateKeyAndCerts({ cert, key, keyFile, crtFile }: any): boolean {\n  let encrypted;\n  try {\n    // publicEncrypt will throw an error with an invalid cert\n    encrypted = crypto.publicEncrypt(cert, Buffer.from('test'));\n  } catch {\n    return false;\n  }\n\n  try {\n    // privateDecrypt will throw an error with an invalid key\n    crypto.privateDecrypt(key, encrypted);\n  } catch {\n    return false;\n  }\n  return true;\n}\n\n// Read file and throw an error if it doesn't exist\nfunction readEnvFile(file: string, type: string): any {\n  if (!fs.existsSync(file)) {\n    throw new Error(\n      `You specified ${chalk.cyan(type)} in your env, but the file \"${chalk.yellow(\n        file\n      )}\" can't be found.`\n    );\n  }\n  return fs.readFileSync(file);\n}\n\n// Get the https config\n// Return cert files if provided in env, otherwise just true or false\nfunction getHttpsConfig(projectRoot: string, isHttps: boolean): any {\n  const { SSL_CRT_FILE, SSL_KEY_FILE } = process.env;\n\n  if (isHttps && SSL_CRT_FILE && SSL_KEY_FILE) {\n    const crtFile = path.resolve(projectRoot, SSL_CRT_FILE);\n    const keyFile = path.resolve(projectRoot, SSL_KEY_FILE);\n    const config = {\n      cert: readEnvFile(crtFile, 'SSL_CRT_FILE'),\n      key: readEnvFile(keyFile, 'SSL_KEY_FILE'),\n    };\n\n    if (validateKeyAndCerts({ ...config, keyFile, crtFile })) {\n      return config;\n    } else {\n      console.log(\n        chalk.yellow(\n          `\\u203A Failed to self-sign SSL certificates for HTTPS. Falling back to insecure https. You can re run with \\`--no-https\\` to disable HTTPS, or delete the \\`.expo\\` folder and try again.`\n        )\n      );\n      return true;\n    }\n  }\n  return isHttps;\n}\n\ntype SelectiveEnv = Pick<Environment, 'mode' | 'locations' | 'projectRoot' | 'https' | 'platform'>;\n\ntype DevServerOptions = {\n  allowedHost?: string;\n  proxy?: ProxyConfigMap | ProxyConfigArray;\n};\n\n/**\n * Add a valid dev server to the provided Webpack config.\n *\n * @param webpackConfig Existing Webpack config to modify.\n * @param env locations, projectRoot, and https options.\n * @param options Configure how the dev server is setup.\n * @category addons\n */\nexport default function withDevServer(\n  webpackConfig: Configuration,\n  env: SelectiveEnv,\n  options: DevServerOptions = {}\n): Configuration {\n  webpackConfig.devServer = createDevServer(env, options);\n  return webpackConfig;\n}\n\n/**\n * Create a valid Webpack dev server config.\n *\n * @param env locations, projectRoot, and https options.\n * @param options Configure how the dev server is setup.\n * @internal\n */\nexport function createDevServer(\n  env: SelectiveEnv,\n  { allowedHost, proxy }: DevServerOptions = {}\n): WebpackDevServerConfiguration {\n  const { https = false } = env;\n  const locations = env.locations || getPaths(env.projectRoot, env);\n  const { publicPath: publicUrlOrPath } = getPublicPaths(env);\n\n  const disableFirewall = !proxy || process.env.DANGEROUSLY_DISABLE_HOST_CHECK === 'true';\n\n  // Attempt to keep this as close to create-react-native app as possible.\n  // https://github.com/facebook/create-react-app/blob/master/packages/react-scripts/config/webpackDevServer.config.js\n  return {\n    // WebpackDevServer 2.4.3 introduced a security fix that prevents remote\n    // websites from potentially accessing local content through DNS rebinding:\n    // https://github.com/webpack/webpack-dev-server/issues/887\n    // https://medium.com/webpack/webpack-dev-server-middleware-security-issues-1489d950874a\n    // However, it made several existing use cases such as development in cloud\n    // environment or subdomains in development significantly more complicated:\n    // https://github.com/facebook/create-react-app/issues/2271\n    // https://github.com/facebook/create-react-app/issues/2233\n    // While we're investigating better solutions, for now we will take a\n    // compromise. Since our WDS configuration only serves files in the `public`\n    // folder we won't consider accessing them a vulnerability. However, if you\n    // use the `proxy` feature, it gets more dangerous because it can expose\n    // remote code execution vulnerabilities in backends like Django and Rails.\n    // So we will disable the host check normally, but enable it if you have\n    // specified the `proxy` setting. Finally, we let you override it if you\n    // really know what you're doing with a special environment variable.\n    // Note: [\"localhost\", \".localhost\"] will support subdomains - but we might\n    // want to allow setting the allowedHosts manually for more complex setups\n    allowedHosts: disableFirewall ? ['all'] : ([allowedHost].filter(Boolean) as string[]),\n    // Enable gzip compression of generated files.\n    compress: true,\n    static: {\n      // By default WebpackDevServer serves physical files from current directory\n      // in addition to all the virtual build products that it serves from memory.\n      // This is confusing because those files won’t automatically be available in\n      // production build folder unless we copy them. However, copying the whole\n      // project directory is dangerous because we may expose sensitive files.\n      // Instead, we establish a convention that only files in `public` directory\n      // get served. Our build script will copy `public` into the `build` folder.\n      // In `index.html`, you can get URL of `public` folder with %PUBLIC_URL%:\n      // <link rel=\"icon\" href=\"%PUBLIC_URL%/favicon.ico\">\n      // In JavaScript code, you can access it with `process.env.PUBLIC_URL`.\n      // Note that we only recommend to use `public` folder as an escape hatch\n      // for files like `favicon.ico`, `manifest.json`, and libraries that are\n      // for some reason broken when imported through webpack. If you just want to\n      // use an image, put it in `src` and `import` it from JavaScript instead.\n      directory: locations.template.folder,\n      publicPath: [publicUrlOrPath],\n      // By default files from `contentBase` will not trigger a page reload.\n      watch: {\n        // Reportedly, this avoids CPU overload on some systems.\n        // https://github.com/facebook/create-react-app/issues/293\n        // src/node_modules is not ignored to support absolute imports\n        // https://github.com/facebook/create-react-app/issues/1065\n        ignored: ignoredFiles(locations.root),\n      },\n    },\n\n    client: {\n      webSocketURL: {\n        // Enable custom sockjs pathname for websocket connection to hot reloading server.\n        // Enable custom sockjs hostname, pathname and port for websocket connection\n        // to hot reloading server.\n        hostname: sockHost,\n        pathname: sockPath,\n        port: sockPort,\n      },\n      // overlay: true,\n      overlay: false,\n      // TODO: This is nonstandard, prevents logging in the browser\n      // logging: 'none',\n    },\n\n    webSocketServer: {\n      type: 'ws',\n      options: {\n        host: sockHost,\n        port: sockPort,\n        path: sockPath,\n      },\n    },\n    devMiddleware: {\n      index: true,\n      // It is important to tell WebpackDevServer to use the same \"publicPath\" path as\n      // we specified in the webpack config. When homepage is '.', default to serving\n      // from the root.\n      // remove last slash so user can land on `/test` instead of `/test/`\n      publicPath: publicUrlOrPath.slice(0, -1),\n    },\n\n    https: getHttpsConfig(env.projectRoot, https),\n    host,\n    historyApiFallback: {\n      // Paths with dots should still use the history fallback.\n      // See https://github.com/facebook/create-react-app/issues/387.\n      disableDotRule: true,\n      index: publicUrlOrPath,\n    },\n\n    // `proxy` is run between `before` and `after` `webpack-dev-server` hooks\n    proxy,\n    setupMiddlewares(middlewares, devServer) {\n      assert(devServer, 'webpack-dev-server is not defined');\n\n      middlewares.unshift(\n        // Keep `evalSourceMapMiddleware`\n        // middlewares before `redirectServedPath` otherwise will not have any effect\n        // This lets us fetch source contents from webpack for the error overlay\n        createEvalSourceMapMiddleware(devServer),\n        // Redirect to `PUBLIC_URL` or `homepage` from `package.json` if url not match\n        createRedirectServedPathMiddleware(publicUrlOrPath),\n        // This service worker file is effectively a 'no-op' that will reset any\n        // previous service worker registered for the same host:port combination.\n        // We do this in development to avoid hitting the production cache if\n        // it used the same host and port.\n        // https://github.com/facebook/create-react-app/issues/2272#issuecomment-302832432\n        createNoopServiceWorkerMiddleware(publicUrlOrPath)\n      );\n\n      return middlewares;\n    },\n  };\n}\n"]}