{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/addons/index.ts"], "names": [], "mappings": ";;;;;;AAAA,yDAAmE;AAA1D,uIAAA,OAAO,OAAqB;AACrC,yCAAmD;AAA1C,uHAAA,OAAO,OAAa;AAC7B,iDAA2D;AAAlD,+HAAA,OAAO,OAAiB", "sourcesContent": ["export { default as withOptimizations } from './withOptimizations';\nexport { default as with<PERSON>lias } from './withAlias';\nexport { default as withDevServer } from './withDevServer';\n"]}