{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../../src/env/validate.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B,4DAAoC;AACpC,mCAAmC;AAGnC;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,GAAqB;IACvD,IAAI,OAAO,GAAG,CAAC,WAAW,KAAK,QAAQ,EAAE;QACvC,MAAM,IAAI,KAAK,CACb,yGAAyG,CAC1G,CAAC;KACH;IACD,0BAA0B,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAEtC,MAAM,UAAU,GAAG,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;IACzD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/C,MAAM,IAAI,KAAK,CACb,iFAAiF,UAAU,CAAC,IAAI,CAC9F,IAAI,CACL,EAAE,CACJ,CAAC;KACH;IAED,gDAAgD;IAChD,IAAI,OAAO,GAAG,CAAC,QAAQ,KAAK,WAAW,EAAE;QACvC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC;KACtB;IACD,wFAAwF;IACxF,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,WAAW,EAAE;QACpC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;KACnB;IAED,oCAAoC;IACpC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;QAClB,GAAG,CAAC,SAAS,GAAG,IAAA,gBAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;KAChD;IAED,kCAAkC;IAClC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;QACf,GAAG,CAAC,MAAM,GAAG,IAAA,mBAAS,EAAC,GAAkB,CAAC,CAAC;KAC5C;IAED,OAAO,GAAkB,CAAC;AAC5B,CAAC;AArCD,kDAqCC;AAED,IAAI,MAAM,GAA+B,EAAE,CAAC;AAE5C,SAAS,oBAAoB,CAC3B,MAA8B,EAC9B,GAAW,EACX,QAAiB;IAEjB,OAAO,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC;AAC/E,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,0BAA0B,CAAC,GAAqB,EAAE,WAAoB,KAAK;IACzF,MAAM,QAAQ,GAA8B;QAC1C,UAAU,EAAE,0CAA0C;QACtD,WAAW,EAAE,2CAA2C;QACxD,QAAQ,EAAE,oDAAoD;KAC/D,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC3C,IAAI,oBAAoB,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE;YAChD,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;YACvB,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,QAAQ,CAAC,KAAK,CAClB,8BAA8B,OAAO,qBAAqB,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CACrF,CACF,CAAC;SACH;KACF;AACH,CAAC;AAjBD,gEAiBC;AAED;;;;GAIG;AACH,SAAgB,cAAc;IAC5B,MAAM,GAAG,EAAE,CAAC;AACd,CAAC;AAFD,wCAEC", "sourcesContent": ["import chalk from 'chalk';\n\nimport getConfig from './getConfig';\nimport { getPaths } from './paths';\nimport { Environment, InputEnvironment } from '../types';\n\n/**\n * Validate the environment options and apply default values.\n *\n * @param env\n * @category env\n */\nexport function validateEnvironment(env: InputEnvironment): Environment {\n  if (typeof env.projectRoot !== 'string') {\n    throw new Error(\n      `@expo/webpack-config requires a valid projectRoot string value which points to the root of your project`\n    );\n  }\n  warnEnvironmentDeprecation(env, true);\n\n  const validModes = ['development', 'production', 'none'];\n  if (!env.mode || !validModes.includes(env.mode)) {\n    throw new Error(\n      `@expo/webpack-config requires a valid \\`mode\\` string which should be one of: ${validModes.join(\n        ', '\n      )}`\n    );\n  }\n\n  // Default to web. Allow any arbitrary platform.\n  if (typeof env.platform === 'undefined') {\n    env.platform = 'web';\n  }\n  // No https by default since it doesn't work well across different browsers and devices.\n  if (typeof env.https === 'undefined') {\n    env.https = false;\n  }\n\n  // Ensure the locations are defined.\n  if (!env.locations) {\n    env.locations = getPaths(env.projectRoot, env);\n  }\n\n  // Ensure the config is evaluated.\n  if (!env.config) {\n    env.config = getConfig(env as Environment);\n  }\n\n  return env as Environment;\n}\n\nlet warned: { [key: string]: boolean } = {};\n\nfunction shouldWarnDeprecated(\n  config: { [key: string]: any },\n  key: string,\n  warnOnce: boolean\n): boolean {\n  return (!warnOnce || !(key in warned)) && typeof config[key] !== 'undefined';\n}\n\n/**\n *\n * @param env\n * @param warnOnce\n * @category env\n * @internal\n */\nexport function warnEnvironmentDeprecation(env: InputEnvironment, warnOnce: boolean = false) {\n  const warnings: { [key: string]: string } = {\n    production: 'Please use `mode: \"production\"` instead.',\n    development: 'Please use `mode: \"development\"` instead.',\n    polyfill: 'Please include polyfills manually in your project.',\n  };\n\n  for (const warning of Object.keys(warnings)) {\n    if (shouldWarnDeprecated(env, warning, warnOnce)) {\n      warned[warning] = true;\n      console.warn(\n        chalk.bgYellow.black(\n          `The environment property \\`${warning}\\` is deprecated. ${warnings[warning]}`.trim()\n        )\n      );\n    }\n  }\n}\n\n/**\n * Used for testing\n * @category env\n * @internal\n */\nexport function _resetWarnings() {\n  warned = {};\n}\n"]}