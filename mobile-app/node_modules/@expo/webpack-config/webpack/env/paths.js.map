{"version": 3, "file": "paths.js", "sourceRoot": "", "sources": ["../../src/env/paths.ts"], "names": [], "mappings": ";;;;;;AAAA,qBAAqB;AACrB,wCAAsF;AACtF,wFAAyD;AACzD,4CAAoB;AACpB,gDAAwB;AACxB,gEAAuC;AACvC,8CAAsB;AAEtB,6CAAiD;AACjD,wDAAgC;AAGhC,wIAAwI;AACxI,SAAS,WAAW,CAAC,SAAiB,EAAE,UAAmB;IACzD,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IACzC,IAAI,QAAQ,IAAI,CAAC,UAAU,EAAE;QAC3B,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;KAClD;SAAM,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE;QAClC,OAAO,GAAG,SAAS,GAAG,CAAC;KACxB;SAAM;QACL,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,SAAS,sBAAsB;IAC7B,OAAO,YAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,uHAAuH;AACvH,SAAS,8BAA8B,CAAC,WAAmB;IACzD,IAAI;QACF,OAAO,IAAA,kCAAiB,EAAC,WAAW,CAAC,CAAC;KACvC;IAAC,OAAO,KAAU,EAAE;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,WAAmB,EAAE,GAAG,cAAwB;IACtF,6CAA6C;IAC7C,IAAI,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,MAAM,MAAK,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;QACxE,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;KAC1B;IAED,OAAO,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,cAAc,CAAC,CAAC;AACtD,CAAC;AAED,SAAS,cAAc,CAAC,WAAmB;IACzC,MAAM,aAAa,GAAG,8BAA8B,CAAC,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,wBAAwB;IACzG,IAAI,aAAa,EAAE;QACjB,OAAO,cAAI,CAAC,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;KACpD;IAED,OAAO,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,qBAAqB,CAAC,QAAgB;IAC7C,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,SAAS,EAAE;QAChD,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC7B;IACD,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,UAAU,CACjB,WAAmB,EACnB,iBAA8B,EAC9B,MAA0C,EAAE;;IAE5C,MAAM,gBAAgB,GAAG,WAAW,IAAI,sBAAsB,EAAE,CAAC;IAEjE,SAAS,QAAQ,CAAC,GAAG,cAAwB;QAC3C,OAAO,8BAA8B,CAAC,gBAAgB,EAAE,GAAG,cAAc,CAAC,CAAC;IAC7E,CAAC;IAED,MAAM,eAAe,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;IACjD,MAAM,WAAW,GAAG,cAAc,CAAC,gBAAgB,CAAC,CAAC;IACrD,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAA,yBAAgB,EAAC,iBAAiB,CAAC,CAAC,CAAC;IAErE,SAAS,YAAY,CAAC,WAAmB,EAAE;;QACzC,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAA,GAAG,CAAC,QAAQ,mCAAI,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE;YAC/B,OAAO,YAAY,CAAC;SACrB;QACD,OAAO,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,mBAAmB,EAAE,QAAQ,CAAC,CAAC;IAC7D,CAAC;IAED,SAAS,iBAAiB,CAAC,GAAG,KAAe;QAC3C,OAAO,cAAI,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,SAAS,gBAAgB,CAAC,GAAG,KAAe;QAC1C,OAAO,cAAI,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,OAAO,GAAkB,IAAI,CAAC;IAClC,IAAI;QACF,OAAO,GAAG,aAAa,CACrB,gBAAgB,EAChB,CAAC,SAAS,EAAE,aAAa,CAAC,EAC1B,qBAAqB,CAAC,MAAA,GAAG,CAAC,QAAQ,mCAAI,KAAK,CAAC,CAC7C,CAAC;KACH;IAAC,MAAM;QACN,mBAAmB;KACpB;IAED,OAAO;QACL,QAAQ;QACR,aAAa,EAAE,gBAAgB;QAC/B,WAAW,EAAE,eAAe;QAC5B,IAAI,EAAE,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QACpC,OAAO;QACP,OAAO,EAAE,WAAW;QACpB,UAAU,EAAE,aAAa,CAAC,gBAAgB,CAAC;QAC3C,eAAe,EAAE,QAAQ,CAAC,qBAAqB,CAAC;QAChD,WAAW,EAAE,QAAQ,CAAC,eAAe,CAAC;QACtC,WAAW,EAAE,QAAQ,CAAC,eAAe,CAAC;QACtC,QAAQ,EAAE;YACR,GAAG,EAAE,YAAY;YACjB,MAAM,EAAE,YAAY,EAAE;YACtB,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC;YACrC,QAAQ,EAAE,YAAY,CAAC,eAAe,CAAC;YACvC,SAAS,EAAE,YAAY,CAAC,YAAY,CAAC;YACrC,OAAO,EAAE,YAAY,CAAC,aAAa,CAAC;SACrC;QACD,UAAU,EAAE;YACV,GAAG,EAAE,iBAAiB;YACtB,MAAM,EAAE,iBAAiB,EAAE;YAC3B,SAAS,EAAE,iBAAiB,CAAC,YAAY,CAAC;YAC1C,QAAQ,EAAE,iBAAiB,CAAC,eAAe,CAAC;YAC5C,SAAS,EAAE,iBAAiB,CAAC,YAAY,CAAC;YAC1C,OAAO,EAAE,iBAAiB,CAAC,aAAa,CAAC;SAC1C;KACF,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,QAAQ,CACtB,WAAmB,EACnB,MAA0C,EAAE;IAE5C,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE;QACrC,yBAAyB,EAAE,IAAI;KAChC,CAAC,CAAC;IACH,OAAO,UAAU,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC;AARD,4BAQC;AAED;;;;;GAKG;AACI,KAAK,UAAU,aAAa,CACjC,WAAmB,EACnB,MAA0C,EAAE;IAE5C,IAAI,GAAG,CAAC;IACR,IAAI;QACF,GAAG,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE,EAAE,yBAAyB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;KACvE;IAAC,MAAM,GAAE;IACV,OAAO,UAAU,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC3C,CAAC;AATD,sCASC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,WAAmB;IAC/C,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE;QACrC,yBAAyB,EAAE,IAAI;KAChC,CAAC,CAAC;IACH,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IAEhD,4EAA4E;IAC5E,4CAA4C;IAC5C,6EAA6E;IAC7E,6EAA6E;IAC7E,+EAA+E;IAC/E,sEAAsE;IACtE,MAAM,SAAS,GAAG,YAAY,IAAI,GAAG,CAAC,QAAQ,CAAC;IAC/C,MAAM,SAAS,GAAG,YAAY,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,aAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,OAAO,WAAW,CAAC,SAAU,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC;AAfD,sCAeC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,GAA8C;IAgB9C,MAAM,UAAU,GAAG,IAAA,iBAAO,EAAC,GAAG,CAAC,CAAC;IAChC,IAAI,UAAU,KAAK,YAAY,EAAE;QAC/B,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClD,OAAO;YACL,UAAU;YACV,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACnC,CAAC;KACH;IAED,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;AAC5C,CAAC;AA3BD,wCA2BC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,WAAmB;IACnD,MAAM,EAAE,GAAG,EAAE,GAAG,IAAA,kBAAS,EAAC,WAAW,EAAE;QACrC,yBAAyB,EAAE,IAAI;KAChC,CAAC,CAAC;IACH,OAAO,8BAA8B,CAAC,WAAW,EAAE,IAAA,yBAAgB,EAAC,GAAG,CAAC,CAAC,CAAC;AAC5E,CAAC;AALD,8CAKC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,WAAmB,EAAE,GAAG,cAAwB;IAC1E,MAAM,gBAAgB,GAAG,WAAW,IAAI,sBAAsB,EAAE,CAAC;IACjE,OAAO,8BAA8B,CAAC,gBAAgB,EAAE,GAAG,cAAc,CAAC,CAAC;AAC7E,CAAC;AAHD,kCAGC;AAED,wIAAwI;AAExI,SAAS,aAAa,CACpB,WAAmB,EACnB,UAAoB,EACpB,SAAmB;IAEnB,MAAM,UAAU,GAAG,IAAA,8BAAiB,EAAC,SAAS,CAAC,CAAC;IAChD,OAAO,2BAA2B,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAC1E,CAAC;AAED,qDAAqD;AACrD,SAAS,2BAA2B,CAClC,WAAmB,EACnB,UAAoB,EACpB,UAAoB;IAEpB,MAAM,GAAG,GAAG,IAAA,uBAAc,EAAC,WAAW,CAAC,CAAC;IAExC,IAAI,GAAG,EAAE;QACP,uHAAuH;QACvH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;QACrB,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACpC,oOAAoO;YACpO,IAAI,KAAK,GAAG,qBAAqB,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YACjE,IAAI,CAAC,KAAK,EAAE;gBACV,sDAAsD;gBACtD,KAAK,GAAG,+BAA+B,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;gBACvE,IAAI,CAAC,KAAK;oBACR,MAAM,IAAI,KAAK,CACb,+GAA+G,CAChH,CAAC;aACL;YACD,OAAO,KAAK,CAAC;SACd;KACF;IAED,gGAAgG;IAChG,uHAAuH;IACvH,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE;QACjC,MAAM,KAAK,GAAG,+BAA+B,CAAC,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QACjF,IAAI,KAAK;YAAE,OAAO,KAAK,CAAC;KACzB;IAED,IAAI;QACF,4FAA4F;QAC5F,4GAA4G;QAC5G,+DAA+D;QAE/D,8MAA8M;QAC9M,OAAO,IAAA,sBAAW,EAAC,WAAW,EAAE,eAAe,CAAC,CAAC;KAClD;IAAC,MAAM;QACN,MAAM,IAAI,KAAK,CACb,wKAAwK,CACzK,CAAC;KACH;AACH,CAAC;AAED,8DAA8D;AAC9D,SAAS,+BAA+B,CACtC,aAAqB,EACrB,QAAgB,EAChB,UAAoB;IAEpB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAClC,MAAM,UAAU,GAAG,sBAAW,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,QAAQ,IAAI,SAAS,EAAE,CAAC,CAAC;QACjF,IAAI,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YAChD,OAAO,UAAU,CAAC;SACnB;KACF;IACD,OAAO,sBAAW,CAAC,MAAM,CAAC,aAAa,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC;AAC7D,CAAC;AAED,yFAAyF;AACzF,yCAAyC;AACzC,SAAS,qBAAqB,CAC5B,aAAqB,EACrB,QAAgB,EAChB,UAAoB;IAEpB,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IACtD,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QAC7B,OAAO,UAAU,CAAC;KACnB;IACD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;QAClC,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,QAAQ,IAAI,SAAS,EAAE,CAAC,CAAC;QACxE,IAAI,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;YAC7B,OAAO,UAAU,CAAC;SACnB;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["/* eslint-env node */\nimport { ExpoConfig, getConfig, getPackage<PERSON>son, getWebOutputPath } from 'expo/config';\nimport findWorkspaceRoot from 'find-yarn-workspace-root';\nimport fs from 'fs';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport url from 'url';\n\nimport { getBareExtensions } from './extensions';\nimport getMode from './getMode';\nimport { Environment, FilePaths, InputEnvironment } from '../types';\n\n// https://github.com/facebook/create-react-app/blob/9750738cce89a967cc71f28390daf5d4311b193c/packages/react-scripts/config/paths.js#L22\nfunction ensureSlash(inputPath: string, needsSlash: boolean): string {\n  const hasSlash = inputPath.endsWith('/');\n  if (hasSlash && !needsSlash) {\n    return inputPath.substr(0, inputPath.length - 1);\n  } else if (!hasSlash && needsSlash) {\n    return `${inputPath}/`;\n  } else {\n    return inputPath;\n  }\n}\n\nfunction getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\n/** Wraps `findYarnOrNpmWorkspaceRoot` and guards against having an empty `package.json` file in an upper directory. */\nfunction findYarnOrNpmWorkspaceRootSafe(projectRoot: string): string | null {\n  try {\n    return findWorkspaceRoot(projectRoot);\n  } catch (error: any) {\n    if (error.message.includes('Unexpected end of JSON input')) {\n      return null;\n    }\n    throw error;\n  }\n}\n\nfunction getAbsolutePathWithProjectRoot(projectRoot: string, ...pathComponents: string[]): string {\n  // Simple check if we are dealing with a URL.\n  if (pathComponents?.length === 1 && pathComponents[0].startsWith('http')) {\n    return pathComponents[0];\n  }\n\n  return path.resolve(projectRoot, ...pathComponents);\n}\n\nfunction getModulesPath(projectRoot: string): string {\n  const workspaceRoot = findYarnOrNpmWorkspaceRootSafe(path.resolve(projectRoot)); // Absolute path or null\n  if (workspaceRoot) {\n    return path.resolve(workspaceRoot, 'node_modules');\n  }\n\n  return path.resolve(projectRoot, 'node_modules');\n}\n\nfunction getPlatformExtensions(platform: string): string[] {\n  if (platform === 'ios' || platform === 'android') {\n    return [platform, 'native'];\n  }\n  return [platform];\n}\n\nfunction parsePaths(\n  projectRoot: string,\n  nativeAppManifest?: ExpoConfig,\n  env: Pick<InputEnvironment, 'platform'> = {}\n): FilePaths {\n  const inputProjectRoot = projectRoot || getPossibleProjectRoot();\n\n  function absolute(...pathComponents: string[]): string {\n    return getAbsolutePathWithProjectRoot(inputProjectRoot, ...pathComponents);\n  }\n\n  const packageJsonPath = absolute('package.json');\n  const modulesPath = getModulesPath(inputProjectRoot);\n  const productionPath = absolute(getWebOutputPath(nativeAppManifest));\n\n  function templatePath(filename: string = ''): string {\n    const overridePath = absolute(env.platform ?? 'web', filename);\n    if (fs.existsSync(overridePath)) {\n      return overridePath;\n    }\n    return path.join(__dirname, '../../web-default', filename);\n  }\n\n  function getProductionPath(...props: string[]): string {\n    return path.resolve(productionPath, ...props);\n  }\n\n  function getIncludeModule(...props: string[]): string {\n    return path.resolve(modulesPath, ...props);\n  }\n\n  let appMain: string | null = null;\n  try {\n    appMain = getEntryPoint(\n      inputProjectRoot,\n      ['./index', './src/index'],\n      getPlatformExtensions(env.platform ?? 'web')\n    );\n  } catch {\n    // ignore the error\n  }\n\n  return {\n    absolute,\n    includeModule: getIncludeModule,\n    packageJson: packageJsonPath,\n    root: path.resolve(inputProjectRoot),\n    appMain,\n    modules: modulesPath,\n    servedPath: getServedPath(inputProjectRoot),\n    appWebpackCache: absolute('node_modules/.cache'),\n    appTsConfig: absolute('tsconfig.json'),\n    appJsConfig: absolute('jsconfig.json'),\n    template: {\n      get: templatePath,\n      folder: templatePath(),\n      indexHtml: templatePath('index.html'),\n      manifest: templatePath('manifest.json'),\n      serveJson: templatePath('serve.json'),\n      favicon: templatePath('favicon.ico'),\n    },\n    production: {\n      get: getProductionPath,\n      folder: getProductionPath(),\n      indexHtml: getProductionPath('index.html'),\n      manifest: getProductionPath('manifest.json'),\n      serveJson: getProductionPath('serve.json'),\n      favicon: getProductionPath('favicon.ico'),\n    },\n  };\n}\n\n/**\n * Sync method for getting default paths used throughout the Webpack config.\n * This is useful for Next.js which doesn't support async Webpack configs.\n *\n * @param projectRoot\n * @category env\n */\nexport function getPaths(\n  projectRoot: string,\n  env: Pick<InputEnvironment, 'platform'> = {}\n): FilePaths {\n  const { exp } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n  return parsePaths(projectRoot, exp, env);\n}\n\n/**\n * Async method for getting default paths used throughout the Webpack config.\n *\n * @param projectRoot\n * @category env\n */\nexport async function getPathsAsync(\n  projectRoot: string,\n  env: Pick<InputEnvironment, 'platform'> = {}\n): Promise<FilePaths> {\n  let exp;\n  try {\n    exp = getConfig(projectRoot, { skipSDKVersionRequirement: true }).exp;\n  } catch {}\n  return parsePaths(projectRoot, exp, env);\n}\n\n/**\n * Get paths dictating where the app is served regardless of the current Webpack mode.\n *\n * @param projectRoot\n * @category env\n */\nexport function getServedPath(projectRoot: string): string {\n  const { pkg } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n  const envPublicUrl = process.env.WEB_PUBLIC_URL;\n\n  // We use `WEB_PUBLIC_URL` environment variable or \"homepage\" field to infer\n  // \"public path\" at which the app is served.\n  // Webpack needs to know it to put the right <script> hrefs into HTML even in\n  // single-page apps that may serve index.html for nested URLs like /todos/42.\n  // We can't use a relative path in HTML because we don't want to load something\n  // like /todos/42/static/js/bundle.7289d.js. We have to know the root.\n  const publicUrl = envPublicUrl || pkg.homepage;\n  const servedUrl = envPublicUrl || (publicUrl ? url.parse(publicUrl).pathname : '/');\n  return ensureSlash(servedUrl!, true);\n}\n\n/**\n * Get paths dictating where the app is served. In development mode this returns default values.\n *\n * @param env\n * @category env\n */\nexport function getPublicPaths(\n  env: Pick<Environment, 'mode' | 'projectRoot'>\n): {\n  /**\n   * Webpack uses `publicPath` to determine where the app is being served from.\n   * It requires a trailing slash, or the file assets will get an incorrect path.\n   * In development, we always serve from the root. This makes config easier.\n   */\n  publicPath: string;\n\n  /**\n   * `publicUrl` is just like `publicPath`, but we will provide it to our app\n   * as %WEB_PUBLIC_URL% in `index.html` and `process.env.WEB_PUBLIC_URL` in JavaScript.\n   * Omit trailing slash as %WEB_PUBLIC_URL%/xyz looks better than %WEB_PUBLIC_URL%xyz.\n   */\n  publicUrl: string;\n} {\n  const parsedMode = getMode(env);\n  if (parsedMode === 'production') {\n    const publicPath = getServedPath(env.projectRoot);\n    return {\n      publicPath,\n      publicUrl: publicPath.slice(0, -1),\n    };\n  }\n\n  return { publicUrl: '', publicPath: '/' };\n}\n\n/**\n * Get the output folder path. Defaults to `web-build`.\n *\n * @param projectRoot\n * @category env\n */\nexport function getProductionPath(projectRoot: string): string {\n  const { exp } = getConfig(projectRoot, {\n    skipSDKVersionRequirement: true,\n  });\n  return getAbsolutePathWithProjectRoot(projectRoot, getWebOutputPath(exp));\n}\n\n/**\n * Get an absolute path relative to the project root while accounting for remote paths (`https://`).\n *\n * @param projectRoot\n * @category env\n */\nexport function getAbsolute(projectRoot: string, ...pathComponents: string[]): string {\n  const inputProjectRoot = projectRoot || getPossibleProjectRoot();\n  return getAbsolutePathWithProjectRoot(inputProjectRoot, ...pathComponents);\n}\n\n// Forked from https://github.com/expo/expo/blob/ae642c8a5e02103d1edbf41d1550759001d0f414/packages/%40expo/config/src/paths/paths.ts#L35\n\nfunction getEntryPoint(\n  projectRoot: string,\n  entryFiles: string[],\n  platforms: string[]\n): string | null {\n  const extensions = getBareExtensions(platforms);\n  return getEntryPointWithExtensions(projectRoot, entryFiles, extensions);\n}\n\n// Used to resolve the main entry file for a project.\nfunction getEntryPointWithExtensions(\n  projectRoot: string,\n  entryFiles: string[],\n  extensions: string[]\n): string {\n  const pkg = getPackageJson(projectRoot);\n\n  if (pkg) {\n    // If the config doesn't define a custom entry then we want to look at the `package.json`s `main` field, and try again.\n    const { main } = pkg;\n    if (main && typeof main === 'string') {\n      // Testing the main field against all of the provided extensions - for legacy reasons we can't use node module resolution as the package.json allows you to pass in a file without a relative path and expect it as a relative path.\n      let entry = getFileWithExtensions(projectRoot, main, extensions);\n      if (!entry) {\n        // Allow for paths like: `{ \"main\": \"expo/AppEntry\" }`\n        entry = resolveFromSilentWithExtensions(projectRoot, main, extensions);\n        if (!entry)\n          throw new Error(\n            `Cannot resolve entry file: The \\`main\\` field defined in your \\`package.json\\` points to a non-existent path.`\n          );\n      }\n      return entry;\n    }\n  }\n\n  // Now we will start looking for a default entry point using the provided `entryFiles` argument.\n  // This will add support for create-react-app (src/index.js) and react-native-cli (index.js) which don't define a main.\n  for (const fileName of entryFiles) {\n    const entry = resolveFromSilentWithExtensions(projectRoot, fileName, extensions);\n    if (entry) return entry;\n  }\n\n  try {\n    // If none of the default files exist then we will attempt to use the main Expo entry point.\n    // This requires `expo` to be installed in the project to work as it will use `node_module/expo/AppEntry.js`\n    // Doing this enables us to create a bare minimum Expo project.\n\n    // TODO(Bacon): We may want to do a check against `./App` and `expo` in the `package.json` `dependencies` as we can more accurately ensure that the project is expo-min without needing the modules installed.\n    return resolveFrom(projectRoot, 'expo/AppEntry');\n  } catch {\n    throw new Error(\n      `The project entry file could not be resolved. Please define it in the \\`main\\` field of the \\`package.json\\`, create an \\`index.js\\`, or install the \\`expo\\` package.`\n    );\n  }\n}\n\n// Resolve from but with the ability to resolve like a bundler\nfunction resolveFromSilentWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  for (const extension of extensions) {\n    const modulePath = resolveFrom.silent(fromDirectory, `${moduleId}.${extension}`);\n    if (modulePath && modulePath.endsWith(extension)) {\n      return modulePath;\n    }\n  }\n  return resolveFrom.silent(fromDirectory, moduleId) || null;\n}\n\n// Statically attempt to resolve a module but with the ability to resolve like a bundler.\n// This won't use node module resolution.\nfunction getFileWithExtensions(\n  fromDirectory: string,\n  moduleId: string,\n  extensions: string[]\n): string | null {\n  const modulePath = path.join(fromDirectory, moduleId);\n  if (fs.existsSync(modulePath)) {\n    return modulePath;\n  }\n  for (const extension of extensions) {\n    const modulePath = path.join(fromDirectory, `${moduleId}.${extension}`);\n    if (fs.existsSync(modulePath)) {\n      return modulePath;\n    }\n  }\n  return null;\n}\n"]}