{"version": 3, "file": "extensions.js", "sourceRoot": "", "sources": ["../../src/env/extensions.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAE5B;;;;;GAKG;AAEH,SAAgB,uBAAuB,CAAC,GAAG,SAAmB;IAC5D,2CAA2C;IAC3C,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,CAAC;AAChE,CAAC;AAHD,0DAGC;AAED,SAAgB,6BAA6B,CAAC,GAAG,SAAmB;IAClE,2CAA2C;IAC3C,2CAA2C;IAC3C,OAAO,iBAAiB,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,GAAG,CACrF,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CACrB,CAAC;AACJ,CAAC;AAND,sEAMC;AAQD,SAAgB,aAAa,CAAC,SAAmB,EAAE,UAAoB;IACrE,oGAAoG;IACpG,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,iCAAiC,CAAC,CAAC;IACpE,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,kCAAkC,CAAC,CAAC;IAEtE,MAAM,cAAc,GAAG,EAAE,CAAC;IAE1B,4DAA4D;IAC5D,KAAK,MAAM,QAAQ,IAAI,CAAC,GAAG,SAAS,EAAE,EAAE,CAAC,EAAE;QACzC,yCAAyC;QACzC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,cAAc,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SACtE;KACF;IACD,OAAO,cAAc,CAAC;AACxB,CAAC;AAfD,sCAeC;AAED,SAAgB,4BAA4B,CAAC,EAC3C,IAAI,EACJ,QAAQ,EACR,OAAO,GACS;IAChB,wCAAwC;IACxC,MAAM,WAAW,GAAG,CAAC,IAAY,EAAY,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAE9F,qBAAqB;IACrB,IAAI,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;IAEnC,IAAI,QAAQ,EAAE;QACZ,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC3B;IACD,IAAI,IAAI,EAAE;QACR,UAAU,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC;KACpD;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAnBD,oEAmBC;AAED,SAAgB,iBAAiB,CAC/B,SAAmB,EACnB,kBAAmC,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;IAEhF,MAAM,cAAc,GAAG,aAAa,CAAC,SAAS,EAAE,4BAA4B,CAAC,eAAe,CAAC,CAAC,CAAC;IAC/F,wBAAwB;IACxB,2BAA2B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IACvD,OAAO,cAAc,CAAC;AACxB,CAAC;AARD,8CAQC;AAED,SAAS,2BAA2B,CAAC,SAAmB,EAAE,cAAwB;IAChF,8CAA8C;IAC9C,8EAA8E;IAC9E,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5B,iDAAiD;IACjD,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7B,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC7B;IACD,OAAO,cAAc,CAAC;AACxB,CAAC", "sourcesContent": ["import assert from 'assert';\n\n/**\n * Get the platform specific platform extensions in the format that Webpack expects (with a dot prefix).\n *\n * @param platforms supported platforms in order of priority. ex: ios, android, web, native, electron, etc...\n * @category env\n */\n\nexport function getModuleFileExtensions(...platforms: string[]): string[] {\n  // Webpack requires a `.` before each value\n  return getBareExtensions(platforms).map(value => `.${value}`);\n}\n\nexport function getNativeModuleFileExtensions(...platforms: string[]): string[] {\n  // Webpack requires a `.` before each value\n  // Disable modern when using `react-native`\n  return getBareExtensions(platforms, { isReact: true, isTS: true, isModern: false }).map(\n    value => `.${value}`\n  );\n}\n\nexport type LanguageOptions = {\n  isTS: boolean;\n  isModern: boolean;\n  isReact: boolean;\n};\n\nexport function getExtensions(platforms: string[], extensions: string[]): string[] {\n  // In the past we used spread operators to collect the values so now we enforce type safety on them.\n  assert(Array.isArray(platforms), 'Expected: `platforms: string[]`');\n  assert(Array.isArray(extensions), 'Expected: `extensions: string[]`');\n\n  const fileExtensions = [];\n\n  // Ensure order is correct: [platformA.js, platformB.js, js]\n  for (const platform of [...platforms, '']) {\n    // Support both TypeScript and JavaScript\n    for (const extension of extensions) {\n      fileExtensions.push([platform, extension].filter(Boolean).join('.'));\n    }\n  }\n  return fileExtensions;\n}\n\nexport function getLanguageExtensionsInOrder({\n  isTS,\n  isModern,\n  isReact,\n}: LanguageOptions): string[] {\n  // @ts-ignore: filter removes false type\n  const addLanguage = (lang: string): string[] => [lang, isReact && `${lang}x`].filter(Boolean);\n\n  // Support JavaScript\n  let extensions = addLanguage('js');\n\n  if (isModern) {\n    extensions.unshift('mjs');\n  }\n  if (isTS) {\n    extensions = [...addLanguage('ts'), ...extensions];\n  }\n\n  return extensions;\n}\n\nexport function getBareExtensions(\n  platforms: string[],\n  languageOptions: LanguageOptions = { isTS: true, isModern: true, isReact: true }\n): string[] {\n  const fileExtensions = getExtensions(platforms, getLanguageExtensionsInOrder(languageOptions));\n  // Always add these last\n  _addMiscellaneousExtensions(platforms, fileExtensions);\n  return fileExtensions;\n}\n\nfunction _addMiscellaneousExtensions(platforms: string[], fileExtensions: string[]): string[] {\n  // Always add these with no platform extension\n  // In the future we may want to add platform and workspace extensions to json.\n  fileExtensions.push('json');\n  // Native doesn't currently support web assembly.\n  if (platforms.includes('web')) {\n    fileExtensions.push('wasm');\n  }\n  return fileExtensions;\n}\n"]}