"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAliases = exports.validateEnvironment = exports.getMode = exports.getConfig = void 0;
var getConfig_1 = require("./getConfig");
Object.defineProperty(exports, "getConfig", { enumerable: true, get: function () { return __importDefault(getConfig_1).default; } });
var getMode_1 = require("./getMode");
Object.defineProperty(exports, "getMode", { enumerable: true, get: function () { return __importDefault(getMode_1).default; } });
var validate_1 = require("./validate");
Object.defineProperty(exports, "validateEnvironment", { enumerable: true, get: function () { return validate_1.validateEnvironment; } });
var alias_1 = require("./alias");
Object.defineProperty(exports, "getAliases", { enumerable: true, get: function () { return alias_1.getAliases; } });
__exportStar(require("./paths"), exports);
__exportStar(require("./extensions"), exports);
//# sourceMappingURL=index.js.map