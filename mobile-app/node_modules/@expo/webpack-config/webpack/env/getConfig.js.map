{"version": 3, "file": "getConfig.js", "sourceRoot": "", "sources": ["../../src/env/getConfig.ts"], "names": [], "mappings": ";;AAAA,uCAAsD;AAItD;;;;;GAKG;AACH,SAAS,SAAS,CAAC,GAAgD;IACjE,IAAI,GAAG,CAAC,MAAM,EAAE;QACd,OAAO,GAAG,CAAC,MAAM,CAAC;KACnB;IACD,2CAA2C;IAC3C,OAAO,IAAA,0BAAe,EAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC1C,CAAC;AAED,kBAAe,SAAS,CAAC", "sourcesContent": ["import { getConfigForPWA, PWAConfig } from 'expo-pwa';\n\nimport { Environment } from '../types';\n\n/**\n * Get the Expo project config in a way that's optimized for web.\n *\n * @param env Environment properties used for getting the Expo project config.\n * @category env\n */\nfunction getConfig(env: Pick<Environment, 'projectRoot' | 'config'>): PWAConfig {\n  if (env.config) {\n    return env.config;\n  }\n  // Fill all config values with PWA defaults\n  return getConfigForPWA(env.projectRoot);\n}\n\nexport default getConfig;\n"]}