{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/env/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,yCAAmD;AAA1C,uHAAA,OAAO,OAAa;AAC7B,qCAA+C;AAAtC,mHAAA,OAAO,OAAW;AAC3B,uCAAiD;AAAxC,+GAAA,mBAAmB,OAAA;AAC5B,iCAAqC;AAA5B,mGAAA,UAAU,OAAA;AACnB,0CAAwB;AACxB,+CAA6B", "sourcesContent": ["export { default as getConfig } from './getConfig';\nexport { default as getMode } from './getMode';\nexport { validateEnvironment } from './validate';\nexport { getAliases } from './alias';\nexport * from './paths';\nexport * from './extensions';\n"]}