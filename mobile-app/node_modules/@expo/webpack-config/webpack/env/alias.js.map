{"version": 3, "file": "alias.js", "sourceRoot": "", "sources": ["../../src/env/alias.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAuC;AAEvC,SAAgB,UAAU,CAAC,WAAmB;IAC5C,4GAA4G;IAC5G,MAAM,OAAO,GAA2B;QACtC,wDAAwD;QACxD,eAAe,EAAE,kBAAkB;QACnC,0DAA0D;QAC1D,4DAA4D,EAC1D,uDAAuD;QACzD,4DAA4D,EAC1D,oFAAoF;QACtF,qDAAqD,EACnD,gEAAgE;QAClE,gEAAgE,EAC9D,2EAA2E;QAC7E,yDAAyD,EACvD,8DAA8D;KACjE,CAAC;IAEF,qBAAqB;IACrB,6EAA6E;IAC7E,IAAI,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,uCAAuC,CAAC,EAAE;QAC5E,OAAO,CAAC,iCAAiC,CAAC,GAAG,uCAAuC,CAAC;KACtF;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAzBD,gCAyBC", "sourcesContent": ["import resolveFrom from 'resolve-from';\n\nexport function getAliases(projectRoot: string): Record<string, string> {\n  // Even if the module isn't installed, react-native should be aliased to react-native-web for better errors.\n  const aliases: Record<string, string> = {\n    // Alias direct react-native imports to react-native-web\n    'react-native$': 'react-native-web',\n    // Alias internal react-native modules to react-native-web\n    'react-native/Libraries/Components/View/ViewStylePropTypes$':\n      'react-native-web/dist/exports/View/ViewStylePropTypes',\n    'react-native/Libraries/EventEmitter/RCTDeviceEventEmitter$':\n      'react-native-web/dist/vendor/react-native/NativeEventEmitter/RCTDeviceEventEmitter',\n    'react-native/Libraries/vendor/emitter/EventEmitter$':\n      'react-native-web/dist/vendor/react-native/emitter/EventEmitter',\n    'react-native/Libraries/vendor/emitter/EventSubscriptionVendor$':\n      'react-native-web/dist/vendor/react-native/emitter/EventSubscriptionVendor',\n    'react-native/Libraries/EventEmitter/NativeEventEmitter$':\n      'react-native-web/dist/vendor/react-native/NativeEventEmitter',\n  };\n\n  // TODO: Drop this...\n  // Check if the installed version of react-native-web still supports NetInfo.\n  if (resolveFrom.silent(projectRoot, 'react-native-web/dist/exports/NetInfo')) {\n    aliases['@react-native-community/netinfo'] = 'react-native-web/dist/exports/NetInfo';\n  }\n\n  return aliases;\n}\n"]}