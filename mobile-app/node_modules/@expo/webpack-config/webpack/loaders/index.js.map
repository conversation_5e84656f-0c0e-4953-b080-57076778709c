{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/loaders/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,uDAO4B;AAN1B,mHAAA,eAAe,OAAA;AACf,uHAAA,mBAAmB,OAAA;AACnB,sHAAA,kBAAkB,OAAA;AAClB,mHAAA,eAAe,OAAA;AACf,sHAAA,kBAAkB,OAAA;AAClB,qIAAA,OAAO,OAAoB;AAE7B,yDAAmE;AAA1D,uIAAA,OAAO,OAAqB;AACrC,sDAAoC", "sourcesContent": ["export {\n  imageLoaderRule,\n  avifImageLoaderRule,\n  fallbackLoaderRule,\n  styleLoaderRule,\n  getBabelLoaderRule,\n  default as createAllLoaders,\n} from './createAllLoaders';\nexport { default as createBabelLoader } from './createBabelLoader';\nexport * from './createBabelLoader';\n"]}