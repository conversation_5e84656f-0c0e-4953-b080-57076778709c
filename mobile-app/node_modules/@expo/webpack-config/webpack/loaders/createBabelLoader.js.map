{"version": 3, "file": "createBabelLoader.js", "sourceRoot": "", "sources": ["../../src/loaders/createBabelLoader.ts"], "names": [], "mappings": ";;;;;;AAAA,sCAAgD;AAChD,kDAA0B;AAC1B,4CAAoB;AACpB,mCAAiC;AACjC,gDAAwB;AACxB,gEAAuC;AAGvC,gCAAsD;AAGtD,2FAA2F;AAC3F,MAAM,kBAAkB,GAAG,IAAA,gBAAO,EAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AAE/D,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,cAAI,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAEpE,+CAA+C;AAC/C,MAAM,8BAA8B,GAAG;IACrC,SAAS,CAAC,cAAc,CAAC;IACzB,SAAS,CAAC,kBAAkB,CAAC;IAC7B,SAAS,CAAC,MAAM,CAAC;IACjB,SAAS,CAAC,YAAY,CAAC;IACvB,SAAS,CAAC,QAAQ,CAAC;IACnB,SAAS,CAAC,OAAO,CAAC;IAClB,SAAS,CAAC,WAAW,CAAC;IACtB,SAAS,CAAC,aAAa,CAAC;IACxB,SAAS,CAAC,aAAa,CAAC;IACxB,SAAS,CAAC,mBAAmB,CAAC;CAC/B,CAAC;AAEF,MAAM,iBAAiB,GAAG;IACxB,eAAe;IACf,mBAAmB;IACnB,SAAS;IACT,+CAA+C;IAC/C,WAAW;CACZ,CAAC;AAEF,SAAS,sBAAsB;IAC7B,OAAO,YAAE,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,kBAAkB,GAAa,EAAE,CAAC;AACxC,yDAAyD;AACzD,SAAS,mBAAmB,CAAC,SAAiB;IAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACjD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IACjC,IAAI,CAAC,UAAU;QAAE,OAAO,IAAI,CAAC;IAC7B,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;KACxB;SAAM;QACL,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;QACjC,OAAO,KAAK,IAAI,IAAI,CAAC;KACtB;AACH,CAAC;AAED,SAAS,UAAU,CAAC,WAAmB;IACrC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC7C,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,sBAAsB,GAAG,eAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;KAC3E;AACH,CAAC;AAED,SAAS,UAAU,CAAC,mBAA4B;IAC9C,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;QAC3C,OAAO,cAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;KAC1C;IACD,OAAO,sBAAsB,EAAE,CAAC;AAClC,CAAC;AAED,SAAS,uBAAuB,CAAC,WAAmB,EAAE,UAAkB,GAAG;IACzE,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IACrD,MAAM,QAAQ,GAAG,eAAe,OAAO,GAAG,CAAC;IAE3C,MAAM,OAAO,GAAG,IAAA,wBAAiB,EAAC;QAChC,QAAQ;QACR,GAAG,EAAE,WAAW;QAChB,cAAc,EAAE,QAAQ;KACzB,CAAC,CAAC;IAEH,OAAO,GAAG,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,OAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;AAC1D,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,gCAAgC,CAC9C,GAA8F;;IAE9F,MAAM,IAAI,GAAG,IAAA,aAAO,EAAC,GAAG,CAAC,CAAC;IAC1B,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,IAAA,cAAQ,EAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAClE,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,IAAI,IAAA,eAAS,EAAC,GAAG,CAAC,CAAC;IAE/C,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC;IACrC,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;IAE7B,OAAO,iBAAiB,CAAC;QACvB,WAAW,EAAE,SAAS,CAAC,IAAI;QAC3B,IAAI;QACJ,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,gBAAgB,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI;QAC9C,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,OAAO,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAA,MAAA,GAAG,CAAC,KAAK,0CAAE,oCAAoC,KAAI,EAAE,CAAC,CAAC;QAC/F,GAAG,EAAE,KAAK,CAAC,GAAG;KACf,CAAC,CAAC;AACL,CAAC;AAnBD,4EAmBC;AACD;;;;GAIG;AACH,SAAwB,iBAAiB,CAAC;AACxC;;GAEG;AACH,IAAI,EACJ,WAAW,EAAE,gBAAgB,EAC7B,gBAAgB,EAChB,OAAO,GAAG,EAAE,EACZ,OAAO,EACP,QAAQ,GAAG,KAAK,EAChB,SAAS,EACT,GAAG,OAAO,KASR,EAAE;IACJ,MAAM,kBAAkB,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAC;IACxD,MAAM,OAAO,GAAG,CAAC,GAAG,8BAA8B,EAAE,GAAG,OAAO,CAAC,CAAC;IAChE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC;IACpC,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;IAEjD,MAAM,YAAY,GAAG,IAAI,KAAK,YAAY,CAAC;IAE3C,MAAM,WAAW,GAAG,gBAAgB,IAAI,sBAAsB,EAAE,CAAC;IACjE,IAAI,aAAa,GAAQ;QACvB,qDAAqD;QACrD,OAAO,EAAE,KAAK;QACd,mEAAmE;QACnE,UAAU,EAAE,IAAI;KACjB,CAAC;IAEF,IACE,CAAC,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;QACzD,CAAC,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAClD;QACA,6EAA6E;QAC7E,kCAAkC;QAClC,MAAM,UAAU,GAAG,sBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;QACxE,IAAI,UAAU,EAAE;YACd,aAAa,GAAG;gBACd,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,UAAU,CAAC;aACtB,CAAC;SACH;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,sDAAsD,CAAC,CAAC,CAAC;SACnF;KACF;IAED,aAAa,GAAG;QACd,GAAG,aAAa;QAChB,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAE3B,UAAU,EAAE,aAAa;QACzB,IAAI,EAAE,kBAAkB;QACxB,OAAO,EAAE,YAAY;QACrB,8DAA8D;QAC9D,0DAA0D;QAC1D,8DAA8D;QAC9D,UAAU,EAAE,kBAAkB;QAC9B,cAAc,EAAE,kBAAkB;KACnC,CAAC;IAEF,IAAI,eAAe,GAAuB,gBAAgB,CAAC,eAAe,CAAC;IAC3E,IAAI,CAAC,eAAe,EAAE;QACpB,IAAI;YACF,eAAe,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,CAAC;SAC/D;QAAC,OAAO,KAAU,EAAE;YACnB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,KAAK,CAAC,0CAA0C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAE1F,MAAM,KAAK,CAAC;SACb;KACF;IACD,aAAa,CAAC,eAAe,GAAG,eAAe,CAAC;IAChD,aAAa,CAAC,gBAAgB,GAAG,KAAK,CAAC;IACvC,aAAa,CAAC,cAAc;QAC1B,gBAAgB,CAAC,cAAc;YAC/B,cAAI,CAAC,IAAI,CACP,kBAAkB,EAClB,OAAO,EACP,QAAQ,EACR,OAAO,EACP,IAAI,IAAI,aAAa,EACrB,cAAc,CACf,CAAC;IACJ,aAAa,CAAC,MAAM,GAAG;QACrB,mBAAmB,EAAE,mBAAmB;QACxC,OAAO,EAAE,SAAS;QAClB,QAAQ;QACR,IAAI;KACL,CAAC;IACF,OAAO;QACL,IAAI,EAAE,wBAAwB;QAC9B,wBAAwB;QACxB,qDAAqD;QACrD,GAAG,OAAO;QACV,OAAO,CAAC,SAAiB;YACvB,KAAK,MAAM,cAAc,IAAI,OAAO,EAAE;gBACpC,IAAI,SAAS,CAAC,QAAQ,CAAC,cAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE;oBACtD,IAAI,OAAO,EAAE;wBACX,MAAM,WAAW,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;wBACnD,IAAI,WAAW;4BAAE,UAAU,CAAC,WAAW,CAAC,CAAC;qBAC1C;oBACD,OAAO,IAAI,CAAC;iBACb;aACF;YACD,6DAA6D;YAC7D,IAAI,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;gBAC1C,KAAK,MAAM,QAAQ,IAAI,iBAAiB,EAAE;oBACxC,IAAI,SAAS,CAAC,QAAQ,CAAC,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE;wBAChD,OAAO,KAAK,CAAC;qBACd;iBACF;gBACD,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,GAAG,EAAE;YACH,GAAG,SAAS;YACZ,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;YACvC,OAAO,EAAE,aAAa;SACvB;QACD,OAAO,EAAE;YACP,cAAc,EAAE,KAAK;SACtB;KACF,CAAC;AACJ,CAAC;AAnID,oCAmIC", "sourcesContent": ["import { loadPartialConfig } from '@babel/core';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport { boolish } from 'getenv';\nimport path from 'path';\nimport resolveFrom from 'resolve-from';\nimport { RuleSetRule } from 'webpack';\n\nimport { getConfig, getMode, getPaths } from '../env';\nimport { Environment, Mode } from '../types';\n\n// Source maps are resource heavy and can cause out of memory issue for large source files.\nconst shouldUseSourceMap = boolish('GENERATE_SOURCEMAP', true);\n\nconst getModule = (name: string) => path.join('node_modules', name);\n\n// Only compile files from the react ecosystem.\nconst includeModulesThatContainPaths = [\n  getModule('react-native'),\n  getModule('react-navigation'),\n  getModule('expo'),\n  getModule('unimodules'),\n  getModule('@react'),\n  getModule('@expo'),\n  getModule('@use-expo'),\n  getModule('@unimodules'),\n  getModule('native-base'),\n  getModule('styled-components'),\n];\n\nconst excludedRootPaths = [\n  '/node_modules',\n  '/bower_components',\n  '/.expo/',\n  // Prevent transpiling webpack generated files.\n  '(webpack)',\n];\n\nfunction getPossibleProjectRoot(): string {\n  return fs.realpathSync(process.cwd());\n}\n\nconst parsedPackageNames: string[] = [];\n// TODO: Bacon: Support internal packages. ex: react/fbjs\nfunction packageNameFromPath(inputPath: string): string | null {\n  const modules = inputPath.split('node_modules/');\n  const libAndFile = modules.pop();\n  if (!libAndFile) return null;\n  if (libAndFile.charAt(0) === '@') {\n    const [org, lib] = libAndFile.split('/');\n    return org + '/' + lib;\n  } else {\n    const components = libAndFile.split('/');\n    const first = components.shift();\n    return first || null;\n  }\n}\n\nfunction logPackage(packageName: string) {\n  if (!parsedPackageNames.includes(packageName)) {\n    parsedPackageNames.push(packageName);\n    console.log(chalk.cyan('\\nCompiling module: ' + chalk.bold(packageName)));\n  }\n}\n\nfunction ensureRoot(possibleProjectRoot?: string): string {\n  if (typeof possibleProjectRoot === 'string') {\n    return path.resolve(possibleProjectRoot);\n  }\n  return getPossibleProjectRoot();\n}\n\nfunction generateCacheIdentifier(projectRoot: string, version: string = '1'): string {\n  const filename = path.join(projectRoot, 'foobar.js');\n  const cacheKey = `babel-cache-${version}-`;\n\n  const partial = loadPartialConfig({\n    filename,\n    cwd: projectRoot,\n    sourceFileName: filename,\n  });\n\n  return `${cacheKey}${JSON.stringify(partial!.options)}`;\n}\n\n/**\n * Creates a Rule for loading Application code and packages from the Expo ecosystem.\n * This method attempts to recreate how Metro loads ES modules in the `node_modules` folder.\n *\n * @param env\n * @internal\n */\nexport function createBabelLoaderFromEnvironment(\n  env: Pick<Environment, 'babel' | 'locations' | 'projectRoot' | 'config' | 'mode' | 'platform'>\n): RuleSetRule {\n  const mode = getMode(env);\n  const locations = env.locations || getPaths(env.projectRoot, env);\n  const appConfig = env.config || getConfig(env);\n\n  const { build = {} } = appConfig.web;\n  const { babel = {} } = build;\n\n  return createBabelLoader({\n    projectRoot: locations.root,\n    mode,\n    platform: env.platform,\n    babelProjectRoot: babel.root || locations.root,\n    verbose: babel.verbose,\n    include: [...(babel.include || []), ...(env.babel?.dangerouslyAddModulePathsToTranspile || [])],\n    use: babel.use,\n  });\n}\n/**\n * A complex babel loader which uses the project's `babel.config.js`\n * to resolve all of the Unimodules which are shipped as ES modules (early 2019).\n * @category loaders\n */\nexport default function createBabelLoader({\n  /**\n   * The webpack mode: `\"production\" | \"development\"`\n   */\n  mode,\n  projectRoot: inputProjectRoot,\n  babelProjectRoot,\n  include = [],\n  verbose,\n  platform = 'web',\n  useCustom,\n  ...options\n}: {\n  projectRoot?: string;\n  useCustom?: boolean;\n  mode?: Mode;\n  babelProjectRoot?: string;\n  include?: string[];\n  verbose?: boolean;\n  [key: string]: any;\n} = {}): RuleSetRule {\n  const ensuredProjectRoot = ensureRoot(babelProjectRoot);\n  const modules = [...includeModulesThatContainPaths, ...include];\n  const customUse = options.use || {};\n  const customUseOptions = customUse.options || {};\n\n  const isProduction = mode === 'production';\n\n  const projectRoot = inputProjectRoot || getPossibleProjectRoot();\n  let presetOptions: any = {\n    // Explicitly use babel.config.js instead of .babelrc\n    babelrc: false,\n    // Attempt to use local babel.config.js file for compiling project.\n    configFile: true,\n  };\n\n  if (\n    !fs.existsSync(path.join(projectRoot, 'babel.config.js')) &&\n    !fs.existsSync(path.join(projectRoot, '.babelrc'))\n  ) {\n    // If no babel config exists then fallback on the default `babel-preset-expo`\n    // which is installed with `expo`.\n    const modulePath = resolveFrom.silent(projectRoot, 'babel-preset-expo');\n    if (modulePath) {\n      presetOptions = {\n        babelrc: false,\n        configFile: false,\n        presets: [modulePath],\n      };\n    } else {\n      console.log(chalk.yellow('\\u203A Webpack failed to locate a valid Babel config'));\n    }\n  }\n\n  presetOptions = {\n    ...presetOptions,\n    ...(customUseOptions || {}),\n\n    sourceType: 'unambiguous',\n    root: ensuredProjectRoot,\n    compact: isProduction,\n    // Babel sourcemaps are needed for debugging into node_modules\n    // code.  Without the options below, debuggers like VSCode\n    // show incorrect code and set breakpoints on the wrong lines.\n    sourceMaps: shouldUseSourceMap,\n    inputSourceMap: shouldUseSourceMap,\n  };\n\n  let cacheIdentifier: string | undefined = customUseOptions.cacheIdentifier;\n  if (!cacheIdentifier) {\n    try {\n      cacheIdentifier = generateCacheIdentifier(ensuredProjectRoot);\n    } catch (error: any) {\n      console.log(chalk.black.bgRed(`The project's Babel config is invalid: ${error.message}`));\n\n      throw error;\n    }\n  }\n  presetOptions.cacheIdentifier = cacheIdentifier;\n  presetOptions.cacheCompression = false;\n  presetOptions.cacheDirectory =\n    customUseOptions.cacheDirectory ||\n    path.join(\n      ensuredProjectRoot,\n      '.expo',\n      platform,\n      'cache',\n      mode || 'development',\n      'babel-loader'\n    );\n  presetOptions.caller = {\n    __dangerous_rule_id: 'expo-babel-loader',\n    bundler: 'webpack',\n    platform,\n    mode,\n  };\n  return {\n    test: /\\.(js|mjs|jsx|ts|tsx)$/,\n    // Can only clobber test\n    // Prevent clobbering the `include` and `use` values.\n    ...options,\n    include(inputPath: string): boolean {\n      for (const possibleModule of modules) {\n        if (inputPath.includes(path.normalize(possibleModule))) {\n          if (verbose) {\n            const packageName = packageNameFromPath(inputPath);\n            if (packageName) logPackage(packageName);\n          }\n          return true;\n        }\n      }\n      // Is inside the project and is not one of designated modules\n      if (inputPath.includes(ensuredProjectRoot)) {\n        for (const excluded of excludedRootPaths) {\n          if (inputPath.includes(path.normalize(excluded))) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    },\n    use: {\n      ...customUse,\n      loader: require.resolve('babel-loader'),\n      options: presetOptions,\n    },\n    resolve: {\n      fullySpecified: false,\n    },\n  };\n}\n"]}