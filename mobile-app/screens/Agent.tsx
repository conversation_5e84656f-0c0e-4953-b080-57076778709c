import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import io from 'socket.io-client';
import ReactiveBlob from '../components/ReactiveBlob';
import JoyDivisionVisualizer from '../components/JoyDivisionVisualizer';

const SERVER_URL = 'http://localhost:3000';

type BlobState = 'idle' | 'recording' | 'thinking';

const Agent: React.FC = () => {
  const [blobState, setBlobState] = useState<BlobState>('idle');
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [textInput, setTextInput] = useState('');
  const [socket, setSocket] = useState<any>(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [agentState, setAgentState] = useState<'idle' | 'listening' | 'thinking' | 'speaking'>('idle');

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
    });

    newSocket.on('newMessage', (message: any) => {
      console.log('New message received:', message);
      if (blobState === 'thinking') {
        setBlobState('idle');
        setAgentState('speaking');
        // Return to idle after showing response
        setTimeout(() => setAgentState('idle'), 3000);
      }
    });

    return () => newSocket.close();
  }, []);

  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        Alert.alert('Permission required', 'Please grant microphone permission');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      setBlobState('recording');
      setAgentState('listening');
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);

      // Auto-stop after 5 seconds for demo
      setTimeout(() => {
        if (recording) {
          stopRecording();
        }
      }, 5000);
    } catch (err) {
      console.error('Failed to start recording', err);
      setBlobState('idle');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    setBlobState('thinking');
    setAgentState('thinking');
    setRecording(null);

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (uri) {
        await uploadAudio(uri);
      }
    } catch (error) {
      console.error('Failed to stop recording', error);
      setBlobState('idle');
    }
  };

  const uploadAudio = async (uri: string) => {
    try {
      // First, upload audio to Watson Speech-to-Text service
      const formData = new FormData();
      formData.append('audio', {
        uri,
        type: 'audio/mp4',
        name: 'recording.mp4',
      } as any);

      const sttResponse = await fetch(`${SERVER_URL}/watson/stt`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      let transcription = '';
      
      if (sttResponse.ok) {
        const sttResult = await sttResponse.json();
        transcription = sttResult.transcription || 'Unable to transcribe audio';
        console.log('Transcription result:', sttResult);
      } else {
        // Fallback to mock if Watson fails
        const mockTranscriptions = [
          "I have been experiencing severe headaches for the past three days",
          "My stomach has been hurting and I feel nauseous",
          "I'm having trouble sleeping and feel very anxious",
          "The medication you prescribed isn't helping much",
          "I feel dizzy when I stand up quickly"
        ];
        transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];
        console.log('Using mock transcription due to STT failure');
      }

      // Send transcription to LLM chat
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: transcription,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Voice message processed:', result);

        // Generate speech for AI response using Watson TTS
        const aiResponseText = result.aiResponse.payload.text;
        
        try {
          const ttsResponse = await fetch(`${SERVER_URL}/watson/tts`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              text: aiResponseText,
              voice: 'en-US_AllisonV3Voice',
              patientId: 'default-patient'
            }),
          });

          if (ttsResponse.ok) {
            const ttsResult = await ttsResponse.json();
            console.log('TTS audio generated:', ttsResult.audioUrl);
            
            // Play the audio response (in a real app, you'd use Audio.Sound.createAsync)
            // For demo, show alert with info about audio generation
            setTimeout(() => {
              Alert.alert(
                '🎤 Voice Message Processed',
                `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}\n\n🔊 Audio response generated!`,
                [
                  { text: 'Play Audio', style: 'default', onPress: () => console.log('Would play:', ttsResult.audioUrl) },
                  { text: 'OK', style: 'cancel' }
                ]
              );
            }, 1000);
          } else {
            // Fallback to text-only response
            setTimeout(() => {
              Alert.alert(
                '🎤 Voice Message Processed',
                `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}`,
                [{ text: 'OK', style: 'default' }]
              );
            }, 1000);
          }
        } catch (ttsError) {
          console.error('TTS error:', ttsError);
          // Fallback to text-only response
          setTimeout(() => {
            Alert.alert(
              '🎤 Voice Message Processed',
              `You said: "${transcription}"\n\n🤖 AI Response: ${aiResponseText}`,
              [{ text: 'OK', style: 'default' }]
            );
          }, 1000);
        }
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Audio upload error:', error);
      Alert.alert('Error', 'Failed to process voice message');
    } finally {
      setBlobState('idle');
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setBlobState('thinking');
      await uploadImage(result.assets[0].uri);
    }
  };

  const uploadImage = async (uri: string) => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'image/jpeg',
        name: 'image.jpg',
      } as any);

      const response = await fetch(`${SERVER_URL}/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Image uploaded successfully:', result);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setBlobState('idle');
    }
  };

  const sendTextMessage = async () => {
    if (!textInput.trim()) return;

    setBlobState('thinking');

    try {
      // Use the new LLM chat endpoint
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: textInput.trim(),
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('AI Response received:', result.aiResponse.payload.text);
        setTextInput('');

        // Show AI response in an alert for demo
        setTimeout(() => {
          Alert.alert(
            '🤖 AI Companion Response',
            result.aiResponse.payload.text,
            [{ text: 'OK', style: 'default' }]
          );
        }, 1000);
      } else {
        throw new Error('Send failed');
      }
    } catch (error) {
      console.error('Text message error:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setBlobState('idle');
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Symptom-OS</Text>
        <Text style={styles.subtitle}>How are you feeling today?</Text>
      </View>

      <View style={styles.blobContainer}>
        <JoyDivisionVisualizer
          agentState={agentState}
          audioLevel={audioLevel}
          isRecording={blobState === 'recording'}
          isThinking={blobState === 'thinking'}
          isSpeaking={agentState === 'speaking'}
        />

        <Text style={styles.stateText}>
          {agentState === 'idle' && 'Tap to record your symptoms'}
          {agentState === 'listening' && 'Recording... speak now'}
          {agentState === 'thinking' && 'Processing your input...'}
          {agentState === 'speaking' && 'AI is responding...'}
        </Text>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.micButton, blobState === 'recording' && styles.recordingButton]}
          onPress={blobState === 'recording' ? stopRecording : startRecording}
          disabled={blobState === 'thinking'}
        >
          <Ionicons 
            name={blobState === 'recording' ? 'stop' : 'mic'} 
            size={32} 
            color="white" 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.imageButton}
          onPress={pickImage}
          disabled={blobState !== 'idle'}
        >
          <Ionicons name="camera" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.textInputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Or type your message here..."
          value={textInput}
          onChangeText={setTextInput}
          multiline
        />
        <TouchableOpacity
          style={[styles.sendButton, !textInput.trim() && styles.sendButtonDisabled]}
          onPress={sendTextMessage}
          disabled={!textInput.trim()}
        >
          <Ionicons name="send" size={20} color={textInput.trim() ? '#007AFF' : '#C7C7CC'} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F4F8',
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
    paddingVertical: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  title: {
    fontSize: 32,
    fontWeight: '800',
    color: '#1E3A8A',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 17,
    color: '#6B7280',
    textAlign: 'center',
    fontWeight: '500',
  },
  blobContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  stateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 20,
  },
  micButton: {
    width: 90,
    height: 90,
    borderRadius: 45,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#3B82F6',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  recordingButton: {
    backgroundColor: '#EF4444',
    shadowColor: '#EF4444',
  },
  imageButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 10,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});

export default Agent;
