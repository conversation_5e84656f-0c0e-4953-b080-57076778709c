import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as ImagePicker from 'expo-image-picker';
import io from 'socket.io-client';
import ReactiveBlob from '../components/ReactiveBlob';

const SERVER_URL = 'http://localhost:3000';

type BlobState = 'idle' | 'recording' | 'thinking';

const Agent: React.FC = () => {
  const [blobState, setBlobState] = useState<BlobState>('idle');
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [textInput, setTextInput] = useState('');
  const [socket, setSocket] = useState<any>(null);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
    });

    newSocket.on('newMessage', (message: any) => {
      console.log('New message received:', message);
      if (blobState === 'thinking') {
        setBlobState('idle');
      }
    });

    return () => newSocket.close();
  }, []);

  const startRecording = async () => {
    try {
      const permission = await Audio.requestPermissionsAsync();
      if (permission.status !== 'granted') {
        Alert.alert('Permission required', 'Please grant microphone permission');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      setBlobState('recording');
      const { recording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );
      setRecording(recording);

      // Auto-stop after 5 seconds for demo
      setTimeout(() => {
        if (recording) {
          stopRecording();
        }
      }, 5000);
    } catch (err) {
      console.error('Failed to start recording', err);
      setBlobState('idle');
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    setBlobState('thinking');
    setRecording(null);

    try {
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      
      if (uri) {
        await uploadAudio(uri);
      }
    } catch (error) {
      console.error('Failed to stop recording', error);
      setBlobState('idle');
    }
  };

  const uploadAudio = async (uri: string) => {
    try {
      // For demo, simulate transcription and send to LLM
      const mockTranscriptions = [
        "I have been experiencing severe headaches for the past three days",
        "My stomach has been hurting and I feel nauseous",
        "I'm having trouble sleeping and feel very anxious",
        "The medication you prescribed isn't helping much",
        "I feel dizzy when I stand up quickly"
      ];

      const transcription = mockTranscriptions[Math.floor(Math.random() * mockTranscriptions.length)];

      // Send transcription to LLM chat
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: transcription,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Voice message processed:', result);

        // Show both transcription and AI response
        setTimeout(() => {
          Alert.alert(
            '🎤 Voice Message Processed',
            `You said: "${transcription}"\n\n🤖 AI Response: ${result.aiResponse.payload.text}`,
            [{ text: 'OK', style: 'default' }]
          );
        }, 1000);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Audio upload error:', error);
      Alert.alert('Error', 'Failed to process voice message');
    } finally {
      setBlobState('idle');
    }
  };

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled && result.assets[0]) {
      setBlobState('thinking');
      await uploadImage(result.assets[0].uri);
    }
  };

  const uploadImage = async (uri: string) => {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri,
        type: 'image/jpeg',
        name: 'image.jpg',
      } as any);

      const response = await fetch(`${SERVER_URL}/upload`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Image uploaded successfully:', result);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      Alert.alert('Error', 'Failed to upload image');
    } finally {
      setBlobState('idle');
    }
  };

  const sendTextMessage = async () => {
    if (!textInput.trim()) return;

    setBlobState('thinking');

    try {
      // Use the new LLM chat endpoint
      const response = await fetch(`${SERVER_URL}/chat/patient/default-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: textInput.trim(),
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log('AI Response received:', result.aiResponse.payload.text);
        setTextInput('');

        // Show AI response in an alert for demo
        setTimeout(() => {
          Alert.alert(
            '🤖 AI Companion Response',
            result.aiResponse.payload.text,
            [{ text: 'OK', style: 'default' }]
          );
        }, 1000);
      } else {
        throw new Error('Send failed');
      }
    } catch (error) {
      console.error('Text message error:', error);
      Alert.alert('Error', 'Failed to send message');
    } finally {
      setBlobState('idle');
    }
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Symptom-OS</Text>
        <Text style={styles.subtitle}>How are you feeling today?</Text>
      </View>

      <View style={styles.blobContainer}>
        <ReactiveBlob state={blobState} />
        
        <Text style={styles.stateText}>
          {blobState === 'idle' && 'Tap to record your symptoms'}
          {blobState === 'recording' && 'Recording... speak now'}
          {blobState === 'thinking' && 'Processing your input...'}
        </Text>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.micButton, blobState === 'recording' && styles.recordingButton]}
          onPress={blobState === 'recording' ? stopRecording : startRecording}
          disabled={blobState === 'thinking'}
        >
          <Ionicons 
            name={blobState === 'recording' ? 'stop' : 'mic'} 
            size={32} 
            color="white" 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.imageButton}
          onPress={pickImage}
          disabled={blobState !== 'idle'}
        >
          <Ionicons name="camera" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.textInputContainer}>
        <TextInput
          style={styles.textInput}
          placeholder="Or type your message here..."
          value={textInput}
          onChangeText={setTextInput}
          multiline
        />
        <TouchableOpacity
          style={[styles.sendButton, !textInput.trim() && styles.sendButtonDisabled]}
          onPress={sendTextMessage}
          disabled={!textInput.trim()}
        >
          <Ionicons name="send" size={20} color={textInput.trim() ? '#007AFF' : '#C7C7CC'} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    paddingTop: 60,
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  blobContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  stateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 20,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 20,
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  recordingButton: {
    backgroundColor: '#FF3B30',
  },
  imageButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 10,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});

export default Agent;
