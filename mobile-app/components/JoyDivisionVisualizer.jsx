/**
 * JOY DIVISION STYLE AUDIO VISUALIZER
 * "Love will tear us apart" - but this visualization will bring us together
 * 
 * Reactive concentric frequency lines that respond to voice input
 * and show AI agent states (listening, thinking, speaking)
 */

import React, { useRef, useEffect, useState } from 'react';
import { View, Dimensions } from 'react-native';

const JoyDivisionVisualizer = ({ 
  isRecording = false, 
  isThinking = false, 
  isSpeaking = false,
  audioLevel = 0,
  agentState = 'idle' // 'idle', 'listening', 'thinking', 'speaking'
}) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const [dimensions, setDimensions] = useState(Dimensions.get('window'));

  // Frequency data simulation
  const frequencyData = useRef(new Array(64).fill(0));
  const smoothedData = useRef(new Array(64).fill(0));
  const time = useRef(0);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
    });
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = dimensions;
    
    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    const centerX = width / 2;
    const centerY = height / 2;
    const maxRadius = Math.min(width, height) * 0.4;
    const numRings = 12; // Number of concentric circles
    const numPoints = 128; // Points per circle for smooth curves

    const animate = () => {
      time.current += 0.016; // ~60fps

      // Clear canvas with dark background
      ctx.fillStyle = '#0a0a0a';
      ctx.fillRect(0, 0, width, height);

      // Generate frequency data based on agent state
      generateFrequencyData();

      // Draw concentric frequency rings
      drawFrequencyRings(ctx, centerX, centerY, maxRadius, numRings, numPoints);

      // Draw center pulse
      drawCenterPulse(ctx, centerX, centerY);

      // Draw state indicator
      drawStateIndicator(ctx, centerX, centerY, maxRadius);

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions, isRecording, isThinking, isSpeaking, audioLevel, agentState]);

  const generateFrequencyData = () => {
    const baseAmplitude = getBaseAmplitude();
    const noiseLevel = getNoiseLevel();
    
    for (let i = 0; i < frequencyData.current.length; i++) {
      let value = 0;
      
      // Generate different patterns based on agent state
      switch (agentState) {
        case 'listening':
          // Reactive to audio input with high frequency emphasis
          value = (audioLevel * 0.8 + Math.random() * 0.2) * 
                  (1 + Math.sin(time.current * 3 + i * 0.1) * 0.3) *
                  (i / frequencyData.current.length); // Higher frequencies more active
          break;
          
        case 'thinking':
          // Pulsing waves with interference patterns
          value = baseAmplitude * 
                  (0.5 + 0.5 * Math.sin(time.current * 2 + i * 0.2)) *
                  (0.7 + 0.3 * Math.sin(time.current * 5 + i * 0.05)) *
                  Math.exp(-Math.abs(i - 32) / 20); // Bell curve distribution
          break;
          
        case 'speaking':
          // Speech-like patterns with formants
          const formant1 = Math.exp(-Math.pow(i - 8, 2) / 50);
          const formant2 = Math.exp(-Math.pow(i - 24, 2) / 80);
          const formant3 = Math.exp(-Math.pow(i - 45, 2) / 100);
          value = baseAmplitude * 0.8 * 
                  (formant1 + formant2 * 0.7 + formant3 * 0.5) *
                  (0.8 + 0.2 * Math.sin(time.current * 8 + i * 0.3));
          break;
          
        default: // idle
          // Gentle ambient noise
          value = noiseLevel * 0.1 * 
                  (0.5 + 0.5 * Math.sin(time.current * 0.5 + i * 0.1));
      }
      
      // Add some random noise for organic feel
      value += (Math.random() - 0.5) * noiseLevel * 0.1;
      
      // Smooth the data for fluid animation
      smoothedData.current[i] = smoothedData.current[i] * 0.85 + value * 0.15;
      frequencyData.current[i] = Math.max(0, Math.min(1, smoothedData.current[i]));
    }
  };

  const getBaseAmplitude = () => {
    switch (agentState) {
      case 'listening': return 0.6 + audioLevel * 0.4;
      case 'thinking': return 0.7;
      case 'speaking': return 0.8;
      default: return 0.2;
    }
  };

  const getNoiseLevel = () => {
    switch (agentState) {
      case 'listening': return 0.3;
      case 'thinking': return 0.2;
      case 'speaking': return 0.4;
      default: return 0.1;
    }
  };

  const getEmotionalColor = (ring) => {
    const ringPhase = (ring / 12) * Math.PI * 2;
    const timePhase = time.current * 0.5;

    switch (agentState) {
      case 'idle':
        // Soft, calming blues and purples
        return `rgba(${Math.floor(100 + 80 * Math.sin(timePhase + ringPhase))}, ${Math.floor(150 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 2))}, `;

      case 'listening':
        // Warm greens and teals
        return `rgba(${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(180 + 75 * Math.sin(timePhase + ringPhase))}, ${Math.floor(140 + 70 * Math.sin(timePhase + ringPhase + 0.5))}, `;

      case 'thinking':
        // Gentle oranges and yellows
        return `rgba(${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase))}, ${Math.floor(160 + 70 * Math.sin(timePhase + ringPhase + 0.8))}, ${Math.floor(100 + 50 * Math.sin(timePhase + ringPhase + 1.5))}, `;

      case 'speaking':
        // Soft pinks and lavenders
        return `rgba(${Math.floor(180 + 60 * Math.sin(timePhase + ringPhase))}, ${Math.floor(130 + 70 * Math.sin(timePhase + ringPhase + 1))}, ${Math.floor(200 + 55 * Math.sin(timePhase + ringPhase + 0.5))}, `;

      case 'emergency':
        // Urgent but caring - warm reds with golden highlights
        return `rgba(${Math.floor(220 + 35 * Math.sin(timePhase * 3 + ringPhase))}, ${Math.floor(100 + 80 * Math.sin(timePhase * 2 + ringPhase + 1))}, ${Math.floor(80 + 60 * Math.sin(timePhase + ringPhase + 2))}, `;

      default:
        return 'rgba(255, 255, 255, '; // Fallback white
    }
  };

  const drawFrequencyRings = (ctx, centerX, centerY, maxRadius, numRings, numPoints) => {
    const ringSpacing = maxRadius / numRings;
    
    for (let ring = 0; ring < numRings; ring++) {
      const baseRadius = ringSpacing * (ring + 1);
      const opacity = 1 - (ring / numRings) * 0.7; // Fade outer rings
      
      // Emotional color with transparency
      const colorBase = getEmotionalColor(ring);
      ctx.strokeStyle = `${colorBase}${opacity})`;
      ctx.lineWidth = Math.max(1, 3 - ring * 0.2);
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      ctx.beginPath();
      
      for (let i = 0; i <= numPoints; i++) {
        const angle = (i / numPoints) * Math.PI * 2;
        
        // Get frequency data for this angle
        const freqIndex = Math.floor((i / numPoints) * frequencyData.current.length);
        const amplitude = frequencyData.current[freqIndex] || 0;
        
        // Add ring-specific modulation
        const ringModulation = 1 + Math.sin(time.current * 1.5 + ring * 0.5) * 0.1;
        const radiusVariation = amplitude * 30 * ringModulation;
        
        const radius = baseRadius + radiusVariation;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.closePath();
      ctx.stroke();
    }
  };

  const drawCenterPulse = (ctx, centerX, centerY) => {
    const pulseRadius = 8 + getBaseAmplitude() * 12;
    const pulseOpacity = 0.3 + getBaseAmplitude() * 0.4;
    
    // Outer glow
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, pulseRadius * 2
    );
    gradient.addColorStop(0, `rgba(255, 255, 255, ${pulseOpacity})`);
    gradient.addColorStop(0.5, `rgba(255, 255, 255, ${pulseOpacity * 0.3})`);
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, pulseRadius * 2, 0, Math.PI * 2);
    ctx.fill();
    
    // Center dot
    ctx.fillStyle = `rgba(255, 255, 255, ${0.8 + pulseOpacity * 0.2})`;
    ctx.beginPath();
    ctx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);
    ctx.fill();
  };

  const drawStateIndicator = (ctx, centerX, centerY, maxRadius) => {
    const indicatorY = centerY + maxRadius + 40;
    
    // State text
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '16px monospace';
    ctx.textAlign = 'center';
    
    const stateText = {
      idle: '◦ READY',
      listening: '● LISTENING',
      thinking: '◐ THINKING',
      speaking: '◑ SPEAKING'
    }[agentState] || '◦ READY';
    
    ctx.fillText(stateText, centerX, indicatorY);
    
    // Frequency bars below text
    const barWidth = 2;
    const barSpacing = 3;
    const numBars = 32;
    const barsWidth = numBars * (barWidth + barSpacing) - barSpacing;
    const startX = centerX - barsWidth / 2;
    
    for (let i = 0; i < numBars; i++) {
      const freqIndex = Math.floor((i / numBars) * frequencyData.current.length);
      const amplitude = frequencyData.current[freqIndex] || 0;
      const barHeight = amplitude * 20;
      
      const x = startX + i * (barWidth + barSpacing);
      const y = indicatorY + 20;
      
      ctx.fillStyle = `rgba(255, 255, 255, ${0.3 + amplitude * 0.5})`;
      ctx.fillRect(x, y - barHeight, barWidth, barHeight);
    }
  };

  return (
    <View style={{ flex: 1, backgroundColor: '#0a0a0a' }}>
      <canvas
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: '#0a0a0a'
        }}
      />
    </View>
  );
};

export default JoyDivisionVisualizer;
