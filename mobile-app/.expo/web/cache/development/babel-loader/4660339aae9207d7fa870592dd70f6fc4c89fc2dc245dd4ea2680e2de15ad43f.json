{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport ExponentAV from \"../ExponentAV\";\nvar _enabled = true;\nexport function isAudioEnabled() {\n  return _enabled;\n}\nexport function throwIfAudioIsDisabled() {\n  if (!_enabled) {\n    throw new Error('Cannot complete operation because audio is not enabled.');\n  }\n}\nexport function setIsEnabledAsync(_x) {\n  return _setIsEnabledAsync.apply(this, arguments);\n}\nfunction _setIsEnabledAsync() {\n  _setIsEnabledAsync = _asyncToGenerator(function* (value) {\n    _enabled = value;\n    yield ExponentAV.setAudioIsEnabled(value);\n  });\n  return _setIsEnabledAsync.apply(this, arguments);\n}", "map": {"version": 3, "names": ["ExponentAV", "_enabled", "isAudioEnabled", "throwIfAudioIsDisabled", "Error", "setIsEnabledAsync", "_x", "_setIsEnabledAsync", "apply", "arguments", "_asyncToGenerator", "value", "setAudioIsEnabled"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio/AudioAvailability.ts"], "sourcesContent": ["import ExponentAV from '../ExponentAV';\n\nlet _enabled = true;\n\nexport function isAudioEnabled(): boolean {\n  return _enabled;\n}\n\nexport function throwIfAudioIsDisabled(): void {\n  if (!_enabled) {\n    throw new Error('Cannot complete operation because audio is not enabled.');\n  }\n}\n\n// @needsAudit\n/**\n * Audio is enabled by default, but if you want to write your own Audio API in a bare workflow app, you might want to disable the Audio API.\n * @param value `true` enables Audio, and `false` disables it.\n * @return A `Promise` that will reject if audio playback could not be enabled for the device.\n */\nexport async function setIsEnabledAsync(value: boolean): Promise<void> {\n  _enabled = value;\n  await ExponentAV.setAudioIsEnabled(value);\n  // TODO : We immediately pause all players when disabled, but we do not resume all shouldPlay\n  // players when enabled. Perhaps for completeness we should allow this; the design of the\n  // enabling API is for people to enable / disable this audio library, but I think that it should\n  // intuitively also double as a global pause/resume.\n}\n"], "mappings": ";AAAA,OAAOA,UAAU;AAEjB,IAAIC,QAAQ,GAAG,IAAI;AAEnB,OAAM,SAAUC,cAAcA,CAAA;EAC5B,OAAOD,QAAQ;AACjB;AAEA,OAAM,SAAUE,sBAAsBA,CAAA;EACpC,IAAI,CAACF,QAAQ,EAAE;IACb,MAAM,IAAIG,KAAK,CAAC,yDAAyD,CAAC;;AAE9E;AAQA,gBAAsBC,iBAAiBA,CAAAC,EAAA;EAAA,OAAAC,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAOtC,SAAAF,mBAAA;EAAAA,kBAAA,GAAAG,iBAAA,CAPM,WAAiCC,KAAc;IACpDV,QAAQ,GAAGU,KAAK;IAChB,MAAMX,UAAU,CAACY,iBAAiB,CAACD,KAAK,CAAC;EAK3C,CAAC;EAAA,OAAAJ,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}