{"ast": null, "code": "import { createFA5iconSet } from \"./createIconSetFromFontAwesome5\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free.json\";\nimport metadata from \"./vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free_meta.json\";\nvar fontMap = {\n  Regular: require(\"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf\"),\n  Light: require(\"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf\"),\n  Solid: require(\"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf\"),\n  Brand: require(\"./vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf\")\n};\nexport var FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand'\n};\nvar iconSet = createFA5iconSet(glyphMap, metadata, fontMap, false);\nexport default iconSet;", "map": {"version": 3, "names": ["createFA5iconSet", "glyphMap", "metadata", "fontMap", "Regular", "require", "Light", "Solid", "Brand", "FA5Style", "regular", "light", "solid", "brand", "iconSet"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/@expo/vector-icons/src/FontAwesome5.ts"], "sourcesContent": ["import { createFA5iconSet } from './createIconSetFromFontAwesome5';\n\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free.json';\nimport metadata from './vendor/react-native-vector-icons/glyphmaps/FontAwesome5Free_meta.json';\nconst fontMap = {\n  Regular: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf'),\n  Light: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf'),\n  Solid: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf'),\n  Brand: require('./vendor/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf'),\n};\n\nexport const FA5Style = {\n  regular: 'regular',\n  light: 'light',\n  solid: 'solid',\n  brand: 'brand',\n};\n\nconst iconSet = createFA5iconSet(glyphMap, metadata, fontMap, false);\n\nexport default iconSet;\n"], "mappings": "AAAA,SAASA,gBAAgB;AAEzB,OAAOC,QAAQ;AACf,OAAOC,QAAQ;AACf,IAAMC,OAAO,GAAG;EACdC,OAAO,EAAEC,OAAO,oEAAoE,CAAC;EACrFC,KAAK,EAAED,OAAO,oEAAoE,CAAC;EACnFE,KAAK,EAAEF,OAAO,kEAAkE,CAAC;EACjFG,KAAK,EAAEH,OAAO,mEAAmE;CAClF;AAED,OAAO,IAAMI,QAAQ,GAAG;EACtBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE;CACR;AAED,IAAMC,OAAO,GAAGd,gBAAgB,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE,KAAK,CAAC;AAEpE,eAAeW,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}