{"ast": null, "code": "import React, { useEffect, useRef } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Svg, { Path } from 'react-native-svg';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ReactiveBlob = function ReactiveBlob(_ref) {\n  var state = _ref.state;\n  var animatedValue = useRef(new Animated.Value(0)).current;\n  var scaleValue = useRef(new Animated.Value(1)).current;\n  useEffect(function () {\n    var animation;\n    switch (state) {\n      case 'idle':\n        animation = Animated.loop(Animated.sequence([Animated.timing(animatedValue, {\n          toValue: 1,\n          duration: 3000,\n          useNativeDriver: false\n        }), Animated.timing(animatedValue, {\n          toValue: 0,\n          duration: 3000,\n          useNativeDriver: false\n        })]));\n        Animated.timing(scaleValue, {\n          toValue: 1,\n          duration: 500,\n          useNativeDriver: true\n        }).start();\n        break;\n      case 'recording':\n        animation = Animated.loop(Animated.sequence([Animated.timing(scaleValue, {\n          toValue: 1.2,\n          duration: 600,\n          useNativeDriver: true\n        }), Animated.timing(scaleValue, {\n          toValue: 1,\n          duration: 600,\n          useNativeDriver: true\n        })]));\n        break;\n      case 'thinking':\n        animation = Animated.loop(Animated.timing(animatedValue, {\n          toValue: 1,\n          duration: 800,\n          useNativeDriver: false\n        }));\n        break;\n    }\n    animation.start();\n    return function () {\n      return animation.stop();\n    };\n  }, [state]);\n  var pathData = 'M100,50 C150,20 180,80 150,120 C120,160 80,160 50,120 C20,80 50,20 100,50 Z';\n  var getColor = function getColor() {\n    switch (state) {\n      case 'idle':\n        return '#E3F2FD';\n      case 'recording':\n        return '#FFEBEE';\n      case 'thinking':\n        return '#F3E5F5';\n      default:\n        return '#E3F2FD';\n    }\n  };\n  var getStrokeColor = function getStrokeColor() {\n    switch (state) {\n      case 'idle':\n        return '#2196F3';\n      case 'recording':\n        return '#F44336';\n      case 'thinking':\n        return '#9C27B0';\n      default:\n        return '#2196F3';\n    }\n  };\n  return _jsx(View, {\n    style: styles.container,\n    children: _jsx(Animated.View, {\n      style: [styles.blobContainer, {\n        transform: [{\n          scale: scaleValue\n        }]\n      }],\n      children: _jsx(Svg, {\n        width: \"200\",\n        height: \"200\",\n        viewBox: \"0 0 200 200\",\n        children: _jsx(Path, {\n          d: pathData,\n          fill: getColor(),\n          stroke: getStrokeColor(),\n          strokeWidth: \"3\"\n        })\n      })\n    })\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  blobContainer: {\n    alignItems: 'center',\n    justifyContent: 'center'\n  }\n});\nexport default ReactiveBlob;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "View", "Animated", "StyleSheet", "Svg", "Path", "jsx", "_jsx", "ReactiveBlob", "_ref", "state", "animatedValue", "Value", "current", "scaleValue", "animation", "loop", "sequence", "timing", "toValue", "duration", "useNativeDriver", "start", "stop", "pathData", "getColor", "getStrokeColor", "style", "styles", "container", "children", "blobC<PERSON>r", "transform", "scale", "width", "height", "viewBox", "d", "fill", "stroke", "strokeWidth", "create", "alignItems", "justifyContent"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/components/ReactiveBlob.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { View, Animated, StyleSheet } from 'react-native';\nimport Svg, { Path } from 'react-native-svg';\n\ninterface ReactiveBlobProps {\n  state: 'idle' | 'recording' | 'thinking';\n}\n\nconst ReactiveBlob: React.FC<ReactiveBlobProps> = ({ state }) => {\n  const animatedValue = useRef(new Animated.Value(0)).current;\n  const scaleValue = useRef(new Animated.Value(1)).current;\n\n  useEffect(() => {\n    let animation: Animated.CompositeAnimation;\n\n    switch (state) {\n      case 'idle':\n        animation = Animated.loop(\n          Animated.sequence([\n            Animated.timing(animatedValue, {\n              toValue: 1,\n              duration: 3000,\n              useNativeDriver: false,\n            }),\n            Animated.timing(animatedValue, {\n              toValue: 0,\n              duration: 3000,\n              useNativeDriver: false,\n            }),\n          ])\n        );\n        Animated.timing(scaleValue, {\n          toValue: 1,\n          duration: 500,\n          useNativeDriver: true,\n        }).start();\n        break;\n\n      case 'recording':\n        animation = Animated.loop(\n          Animated.sequence([\n            Animated.timing(scaleValue, {\n              toValue: 1.2,\n              duration: 600,\n              useNativeDriver: true,\n            }),\n            Animated.timing(scaleValue, {\n              toValue: 1,\n              duration: 600,\n              useNativeDriver: true,\n            }),\n          ])\n        );\n        break;\n\n      case 'thinking':\n        animation = Animated.loop(\n          Animated.timing(animatedValue, {\n            toValue: 1,\n            duration: 800,\n            useNativeDriver: false,\n          })\n        );\n        break;\n    }\n\n    animation.start();\n\n    return () => animation.stop();\n  }, [state]);\n\n  // Simple blob path - we'll use scale and opacity for animation\n  const pathData = 'M100,50 C150,20 180,80 150,120 C120,160 80,160 50,120 C20,80 50,20 100,50 Z';\n\n  const getColor = () => {\n    switch (state) {\n      case 'idle':\n        return '#E3F2FD';\n      case 'recording':\n        return '#FFEBEE';\n      case 'thinking':\n        return '#F3E5F5';\n      default:\n        return '#E3F2FD';\n    }\n  };\n\n  const getStrokeColor = () => {\n    switch (state) {\n      case 'idle':\n        return '#2196F3';\n      case 'recording':\n        return '#F44336';\n      case 'thinking':\n        return '#9C27B0';\n      default:\n        return '#2196F3';\n    }\n  };\n\n  return (\n    <View style={styles.container}>\n      <Animated.View style={[styles.blobContainer, { transform: [{ scale: scaleValue }] }]}>\n        <Svg width=\"200\" height=\"200\" viewBox=\"0 0 200 200\">\n          <Path\n            d={pathData}\n            fill={getColor()}\n            stroke={getStrokeColor()}\n            strokeWidth=\"3\"\n          />\n        </Svg>\n      </Animated.View>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  blobContainer: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n});\n\nexport default ReactiveBlob;\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAEjD,OAAOC,GAAG,IAAIC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAM7C,IAAMC,YAAyC,GAAG,SAA5CA,YAAyCA,CAAAC,IAAA,EAAkB;EAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;EACxD,IAAMC,aAAa,GAAGX,MAAM,CAAC,IAAIE,QAAQ,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAC3D,IAAMC,UAAU,GAAGd,MAAM,CAAC,IAAIE,QAAQ,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAExDd,SAAS,CAAC,YAAM;IACd,IAAIgB,SAAsC;IAE1C,QAAQL,KAAK;MACX,KAAK,MAAM;QACTK,SAAS,GAAGb,QAAQ,CAACc,IAAI,CACvBd,QAAQ,CAACe,QAAQ,CAAC,CAChBf,QAAQ,CAACgB,MAAM,CAACP,aAAa,EAAE;UAC7BQ,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC,EACFnB,QAAQ,CAACgB,MAAM,CAACP,aAAa,EAAE;UAC7BQ,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE;QACnB,CAAC,CAAC,CACH,CACH,CAAC;QACDnB,QAAQ,CAACgB,MAAM,CAACJ,UAAU,EAAE;UAC1BK,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG;UACbC,eAAe,EAAE;QACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACV;MAEF,KAAK,WAAW;QACdP,SAAS,GAAGb,QAAQ,CAACc,IAAI,CACvBd,QAAQ,CAACe,QAAQ,CAAC,CAChBf,QAAQ,CAACgB,MAAM,CAACJ,UAAU,EAAE;UAC1BK,OAAO,EAAE,GAAG;UACZC,QAAQ,EAAE,GAAG;UACbC,eAAe,EAAE;QACnB,CAAC,CAAC,EACFnB,QAAQ,CAACgB,MAAM,CAACJ,UAAU,EAAE;UAC1BK,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG;UACbC,eAAe,EAAE;QACnB,CAAC,CAAC,CACH,CACH,CAAC;QACD;MAEF,KAAK,UAAU;QACbN,SAAS,GAAGb,QAAQ,CAACc,IAAI,CACvBd,QAAQ,CAACgB,MAAM,CAACP,aAAa,EAAE;UAC7BQ,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG;UACbC,eAAe,EAAE;QACnB,CAAC,CACH,CAAC;QACD;IACJ;IAEAN,SAAS,CAACO,KAAK,CAAC,CAAC;IAEjB,OAAO;MAAA,OAAMP,SAAS,CAACQ,IAAI,CAAC,CAAC;IAAA;EAC/B,CAAC,EAAE,CAACb,KAAK,CAAC,CAAC;EAGX,IAAMc,QAAQ,GAAG,6EAA6E;EAE9F,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;IACrB,QAAQf,KAAK;MACX,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,IAAMgB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,QAAQhB,KAAK;MACX,KAAK,MAAM;QACT,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,OACEH,IAAA,CAACN,IAAI;IAAC0B,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EAC5BvB,IAAA,CAACL,QAAQ,CAACD,IAAI;MAAC0B,KAAK,EAAE,CAACC,MAAM,CAACG,aAAa,EAAE;QAAEC,SAAS,EAAE,CAAC;UAAEC,KAAK,EAAEnB;QAAW,CAAC;MAAE,CAAC,CAAE;MAAAgB,QAAA,EACnFvB,IAAA,CAACH,GAAG;QAAC8B,KAAK,EAAC,KAAK;QAACC,MAAM,EAAC,KAAK;QAACC,OAAO,EAAC,aAAa;QAAAN,QAAA,EACjDvB,IAAA,CAACF,IAAI;UACHgC,CAAC,EAAEb,QAAS;UACZc,IAAI,EAAEb,QAAQ,CAAC,CAAE;UACjBc,MAAM,EAAEb,cAAc,CAAC,CAAE;UACzBc,WAAW,EAAC;QAAG,CAChB;MAAC,CACC;IAAC,CACO;EAAC,CACZ,CAAC;AAEX,CAAC;AAED,IAAMZ,MAAM,GAAGzB,UAAU,CAACsC,MAAM,CAAC;EAC/BZ,SAAS,EAAE;IACTa,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDZ,aAAa,EAAE;IACbW,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,eAAenC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}