{"ast": null, "code": "'use client';\n\nimport { hydrate as domLegacyHydrate, render as dom<PERSON>egacy<PERSON>ender } from 'react-dom';\nimport { createRoot as domCreateRoot, hydrateRoot as domHydrateRoot } from 'react-dom/client';\nimport unmountComponentAtNode from \"../unmountComponentAtNode\";\nimport { createSheet } from \"../StyleSheet/dom\";\nexport function hydrate(element, root) {\n  createSheet(root);\n  return domHydrateRoot(root, element);\n}\nexport function render(element, root) {\n  createSheet(root);\n  var reactRoot = domCreateRoot(root);\n  reactRoot.render(element);\n  return reactRoot;\n}\nexport function hydrateLegacy(element, root, callback) {\n  createSheet(root);\n  domLegacyHydrate(element, root, callback);\n  return {\n    unmount: function unmount() {\n      return unmountComponentAtNode(root);\n    }\n  };\n}\nexport default function renderLegacy(element, root, callback) {\n  createSheet(root);\n  domLegacyRender(element, root, callback);\n  return {\n    unmount: function unmount() {\n      return unmountComponentAtNode(root);\n    }\n  };\n}", "map": {"version": 3, "names": ["hydrate", "domLegacyHydrate", "render", "domLegacyRender", "createRoot", "domCreateRoot", "hydrateRoot", "domHydrateRoot", "unmountComponentAtNode", "createSheet", "element", "root", "reactRoot", "hydrateLegacy", "callback", "unmount", "renderLegacy"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/render/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport { hydrate as domLegacyHydrate, render as dom<PERSON>egacy<PERSON>ender } from 'react-dom';\nimport { createRoot as domCreateRoot, hydrateRoot as domHydrateRoot } from 'react-dom/client';\nimport unmountComponentAtNode from '../unmountComponentAtNode';\nimport { createSheet } from '../StyleSheet/dom';\nexport function hydrate(element, root) {\n  createSheet(root);\n  return domHydrateRoot(root, element);\n}\nexport function render(element, root) {\n  createSheet(root);\n  var reactRoot = domCreateRoot(root);\n  reactRoot.render(element);\n  return reactRoot;\n}\nexport function hydrateLegacy(element, root, callback) {\n  createSheet(root);\n  domLegacyHydrate(element, root, callback);\n  return {\n    unmount: function unmount() {\n      return unmountComponentAtNode(root);\n    }\n  };\n}\nexport default function renderLegacy(element, root, callback) {\n  createSheet(root);\n  domLegacyRender(element, root, callback);\n  return {\n    unmount: function unmount() {\n      return unmountComponentAtNode(root);\n    }\n  };\n}"], "mappings": "AASA,YAAY;;AAEZ,SAASA,OAAO,IAAIC,gBAAgB,EAAEC,MAAM,IAAIC,eAAe,QAAQ,WAAW;AAClF,SAASC,UAAU,IAAIC,aAAa,EAAEC,WAAW,IAAIC,cAAc,QAAQ,kBAAkB;AAC7F,OAAOC,sBAAsB;AAC7B,SAASC,WAAW;AACpB,OAAO,SAAST,OAAOA,CAACU,OAAO,EAAEC,IAAI,EAAE;EACrCF,WAAW,CAACE,IAAI,CAAC;EACjB,OAAOJ,cAAc,CAACI,IAAI,EAAED,OAAO,CAAC;AACtC;AACA,OAAO,SAASR,MAAMA,CAACQ,OAAO,EAAEC,IAAI,EAAE;EACpCF,WAAW,CAACE,IAAI,CAAC;EACjB,IAAIC,SAAS,GAAGP,aAAa,CAACM,IAAI,CAAC;EACnCC,SAAS,CAACV,MAAM,CAACQ,OAAO,CAAC;EACzB,OAAOE,SAAS;AAClB;AACA,OAAO,SAASC,aAAaA,CAACH,OAAO,EAAEC,IAAI,EAAEG,QAAQ,EAAE;EACrDL,WAAW,CAACE,IAAI,CAAC;EACjBV,gBAAgB,CAACS,OAAO,EAAEC,IAAI,EAAEG,QAAQ,CAAC;EACzC,OAAO;IACLC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B,OAAOP,sBAAsB,CAACG,IAAI,CAAC;IACrC;EACF,CAAC;AACH;AACA,eAAe,SAASK,YAAYA,CAACN,OAAO,EAAEC,IAAI,EAAEG,QAAQ,EAAE;EAC5DL,WAAW,CAACE,IAAI,CAAC;EACjBR,eAAe,CAACO,OAAO,EAAEC,IAAI,EAAEG,QAAQ,CAAC;EACxC,OAAO;IACLC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1B,OAAOP,sBAAsB,CAACG,IAAI,CAAC;IACrC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}