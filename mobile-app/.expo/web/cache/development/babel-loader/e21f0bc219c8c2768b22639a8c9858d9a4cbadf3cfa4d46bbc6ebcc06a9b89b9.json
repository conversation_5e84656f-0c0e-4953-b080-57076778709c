{"ast": null, "code": "var _window$document;\nexport var isDOMAvailable = typeof window !== 'undefined' && !!((_window$document = window.document) != null && _window$document.createElement);\nexport var canUseEventListeners = isDOMAvailable && !!(window.addEventListener || window.attachEvent);\nexport var canUseViewport = isDOMAvailable && !!window.screen;\nexport var isAsyncDebugging = false;", "map": {"version": 3, "names": ["isDOMAvailable", "window", "_window$document", "document", "createElement", "canUseEventListeners", "addEventListener", "attachEvent", "canUseViewport", "screen", "isAsyncDebugging"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-modules-core/src/environment/browser.web.ts"], "sourcesContent": ["declare global {\n  // Add IE-specific interface to Window\n  interface Window {\n    attachEvent(event: string, listener: EventListener): boolean;\n  }\n}\n\n// Used for delegating node actions when browser APIs aren't available\n// like in SSR websites.\nexport const isDOMAvailable = typeof window !== 'undefined' && !!window.document?.createElement;\nexport const canUseEventListeners =\n  isDOMAvailable && !!(window.addEventListener || window.attachEvent);\nexport const canUseViewport = isDOMAvailable && !!window.screen;\nexport const isAsyncDebugging = false;\n"], "mappings": ";AASA,OAAO,IAAMA,cAAc,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,CAAC,GAAAC,gBAAA,GAACD,MAAM,CAACE,QAAQ,aAAfD,gBAAA,CAAiBE,aAAa;AAC/F,OAAO,IAAMC,oBAAoB,GAC/BL,cAAc,IAAI,CAAC,EAAEC,MAAM,CAACK,gBAAgB,IAAIL,MAAM,CAACM,WAAW,CAAC;AACrE,OAAO,IAAMC,cAAc,GAAGR,cAAc,IAAI,CAAC,CAACC,MAAM,CAACQ,MAAM;AAC/D,OAAO,IAAMC,gBAAgB,GAAG,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}