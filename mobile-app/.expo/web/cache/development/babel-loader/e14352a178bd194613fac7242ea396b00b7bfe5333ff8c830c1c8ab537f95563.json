{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport * as React from 'react';\nimport SectionList from \"../../../../exports/SectionList\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nvar SectionListWithEventThrottle = React.forwardRef(function (props, ref) {\n  return React.createElement(SectionList, _extends({\n    scrollEventThrottle: 0.0001\n  }, props, {\n    ref: ref\n  }));\n});\nexport default createAnimatedComponent(SectionListWithEventThrottle);", "map": {"version": 3, "names": ["_extends", "React", "SectionList", "createAnimatedComponent", "SectionListWithEventThrottle", "forwardRef", "props", "ref", "createElement", "scrollEventThrottle"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedSectionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport SectionList from '../../../../exports/SectionList';\nimport createAnimatedComponent from '../createAnimatedComponent';\n/**\n * @see https://github.com/facebook/react-native/commit/b8c8562\n */\nvar SectionListWithEventThrottle = /*#__PURE__*/React.forwardRef((props, ref) => /*#__PURE__*/React.createElement(SectionList, _extends({\n  scrollEventThrottle: 0.0001\n}, props, {\n  ref: ref\n})));\nexport default createAnimatedComponent(SectionListWithEventThrottle);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AAWrD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW;AAClB,OAAOC,uBAAuB;AAI9B,IAAIC,4BAA4B,GAAgBH,KAAK,CAACI,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG;EAAA,OAAkBN,KAAK,CAACO,aAAa,CAACN,WAAW,EAAEF,QAAQ,CAAC;IACtIS,mBAAmB,EAAE;EACvB,CAAC,EAAEH,KAAK,EAAE;IACRC,GAAG,EAAEA;EACP,CAAC,CAAC,CAAC;AAAA,EAAC;AACJ,eAAeJ,uBAAuB,CAACC,4BAA4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}