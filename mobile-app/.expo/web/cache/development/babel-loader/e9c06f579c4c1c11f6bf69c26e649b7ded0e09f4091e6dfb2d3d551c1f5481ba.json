{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { Asset } from 'expo-asset';\nimport { Platform } from 'expo-modules-core';\nimport { PitchCorrectionQuality } from \"./AV.types\";\nexport var _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS = 500;\nexport var _DEFAULT_INITIAL_PLAYBACK_STATUS = {\n  positionMillis: 0,\n  progressUpdateIntervalMillis: _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS,\n  shouldPlay: false,\n  rate: 1.0,\n  shouldCorrectPitch: false,\n  volume: 1.0,\n  audioPan: 0,\n  isMuted: false,\n  isLooping: false\n};\nexport function getNativeSourceFromSource(source) {\n  var uri = null;\n  var overridingExtension = null;\n  var headers;\n  if (typeof source === 'string' && Platform.OS === 'web') {\n    return {\n      uri: source,\n      overridingExtension: overridingExtension,\n      headers: headers\n    };\n  }\n  var asset = _getAssetFromPlaybackSource(source);\n  if (asset != null) {\n    uri = asset.localUri || asset.uri;\n  } else if (source != null && typeof source !== 'number' && 'uri' in source && typeof source.uri === 'string') {\n    uri = source.uri;\n  }\n  if (uri == null) {\n    return null;\n  }\n  if (source != null && typeof source !== 'number' && 'overrideFileExtensionAndroid' in source && typeof source.overrideFileExtensionAndroid === 'string') {\n    overridingExtension = source.overrideFileExtensionAndroid;\n  }\n  if (source != null && typeof source !== 'number' && 'headers' in source && typeof source.headers === 'object') {\n    headers = source.headers;\n  }\n  return {\n    uri: uri,\n    overridingExtension: overridingExtension,\n    headers: headers\n  };\n}\nfunction _getAssetFromPlaybackSource(source) {\n  if (source == null) {\n    return null;\n  }\n  var asset = null;\n  if (typeof source === 'number') {\n    asset = Asset.fromModule(source);\n  } else if (source instanceof Asset) {\n    asset = source;\n  }\n  return asset;\n}\nexport function assertStatusValuesInBounds(status) {\n  if (typeof status.rate === 'number' && (status.rate < 0 || status.rate > 32)) {\n    throw new RangeError('Rate value must be between 0.0 and 32.0');\n  }\n  if (typeof status.volume === 'number' && (status.volume < 0 || status.volume > 1)) {\n    throw new RangeError('Volume value must be between 0.0 and 1.0');\n  }\n  if (typeof status.audioPan === 'number' && (status.audioPan < -1 || status.audioPan > 1)) {\n    throw new RangeError('Pan value must be between -1.0 and 1.0');\n  }\n}\nexport function getNativeSourceAndFullInitialStatusForLoadAsync(_x, _x2, _x3) {\n  return _getNativeSourceAndFullInitialStatusForLoadAsync.apply(this, arguments);\n}\nfunction _getNativeSourceAndFullInitialStatusForLoadAsync() {\n  _getNativeSourceAndFullInitialStatusForLoadAsync = _asyncToGenerator(function* (source, initialStatus, downloadFirst) {\n    var fullInitialStatus = initialStatus == null ? _DEFAULT_INITIAL_PLAYBACK_STATUS : _objectSpread(_objectSpread({}, _DEFAULT_INITIAL_PLAYBACK_STATUS), initialStatus);\n    assertStatusValuesInBounds(fullInitialStatus);\n    if (typeof source === 'string' && Platform.OS === 'web') {\n      return {\n        nativeSource: {\n          uri: source,\n          overridingExtension: null\n        },\n        fullInitialStatus: fullInitialStatus\n      };\n    }\n    var asset = _getAssetFromPlaybackSource(source);\n    if (downloadFirst && asset) {\n      yield asset.downloadAsync();\n    }\n    var nativeSource = getNativeSourceFromSource(source);\n    if (nativeSource === null) {\n      throw new Error(`Cannot load an AV asset from a null playback source`);\n    }\n    if (asset && asset.localUri) {\n      nativeSource.uri = asset.localUri;\n    }\n    return {\n      nativeSource: nativeSource,\n      fullInitialStatus: fullInitialStatus\n    };\n  });\n  return _getNativeSourceAndFullInitialStatusForLoadAsync.apply(this, arguments);\n}\nexport function getUnloadedStatus() {\n  var error = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  return _objectSpread({\n    isLoaded: false\n  }, error ? {\n    error: error\n  } : null);\n}\nexport var PlaybackMixin = {\n  playAsync: function () {\n    var _playAsync = _asyncToGenerator(function* () {\n      return this.setStatusAsync({\n        shouldPlay: true\n      });\n    });\n    function playAsync() {\n      return _playAsync.apply(this, arguments);\n    }\n    return playAsync;\n  }(),\n  playFromPositionAsync: function () {\n    var _playFromPositionAsync = _asyncToGenerator(function* (positionMillis) {\n      var tolerances = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return this.setStatusAsync({\n        positionMillis: positionMillis,\n        shouldPlay: true,\n        seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n        seekMillisToleranceBefore: tolerances.toleranceMillisBefore\n      });\n    });\n    function playFromPositionAsync(_x4) {\n      return _playFromPositionAsync.apply(this, arguments);\n    }\n    return playFromPositionAsync;\n  }(),\n  pauseAsync: function () {\n    var _pauseAsync = _asyncToGenerator(function* () {\n      return this.setStatusAsync({\n        shouldPlay: false\n      });\n    });\n    function pauseAsync() {\n      return _pauseAsync.apply(this, arguments);\n    }\n    return pauseAsync;\n  }(),\n  stopAsync: function () {\n    var _stopAsync = _asyncToGenerator(function* () {\n      return this.setStatusAsync({\n        positionMillis: 0,\n        shouldPlay: false\n      });\n    });\n    function stopAsync() {\n      return _stopAsync.apply(this, arguments);\n    }\n    return stopAsync;\n  }(),\n  setPositionAsync: function () {\n    var _setPositionAsync = _asyncToGenerator(function* (positionMillis) {\n      var tolerances = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      return this.setStatusAsync({\n        positionMillis: positionMillis,\n        seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n        seekMillisToleranceBefore: tolerances.toleranceMillisBefore\n      });\n    });\n    function setPositionAsync(_x5) {\n      return _setPositionAsync.apply(this, arguments);\n    }\n    return setPositionAsync;\n  }(),\n  setRateAsync: function () {\n    var _setRateAsync = _asyncToGenerator(function* (rate) {\n      var shouldCorrectPitch = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      var pitchCorrectionQuality = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : PitchCorrectionQuality.Low;\n      return this.setStatusAsync({\n        rate: rate,\n        shouldCorrectPitch: shouldCorrectPitch,\n        pitchCorrectionQuality: pitchCorrectionQuality\n      });\n    });\n    function setRateAsync(_x6) {\n      return _setRateAsync.apply(this, arguments);\n    }\n    return setRateAsync;\n  }(),\n  setVolumeAsync: function () {\n    var _setVolumeAsync = _asyncToGenerator(function* (volume, audioPan) {\n      return this.setStatusAsync({\n        volume: volume,\n        audioPan: audioPan\n      });\n    });\n    function setVolumeAsync(_x7, _x8) {\n      return _setVolumeAsync.apply(this, arguments);\n    }\n    return setVolumeAsync;\n  }(),\n  setIsMutedAsync: function () {\n    var _setIsMutedAsync = _asyncToGenerator(function* (isMuted) {\n      return this.setStatusAsync({\n        isMuted: isMuted\n      });\n    });\n    function setIsMutedAsync(_x9) {\n      return _setIsMutedAsync.apply(this, arguments);\n    }\n    return setIsMutedAsync;\n  }(),\n  setIsLoopingAsync: function () {\n    var _setIsLoopingAsync = _asyncToGenerator(function* (isLooping) {\n      return this.setStatusAsync({\n        isLooping: isLooping\n      });\n    });\n    function setIsLoopingAsync(_x0) {\n      return _setIsLoopingAsync.apply(this, arguments);\n    }\n    return setIsLoopingAsync;\n  }(),\n  setProgressUpdateIntervalAsync: function () {\n    var _setProgressUpdateIntervalAsync = _asyncToGenerator(function* (progressUpdateIntervalMillis) {\n      return this.setStatusAsync({\n        progressUpdateIntervalMillis: progressUpdateIntervalMillis\n      });\n    });\n    function setProgressUpdateIntervalAsync(_x1) {\n      return _setProgressUpdateIntervalAsync.apply(this, arguments);\n    }\n    return setProgressUpdateIntervalAsync;\n  }()\n};\nexport * from \"./AV.types\";", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Platform", "PitchCorrectionQuality", "_DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS", "_DEFAULT_INITIAL_PLAYBACK_STATUS", "<PERSON><PERSON><PERSON><PERSON>", "progressUpdateIntervalMillis", "shouldPlay", "rate", "shouldCorrectPitch", "volume", "audioPan", "isMuted", "isLooping", "getNativeSourceFromSource", "source", "uri", "overridingExtension", "headers", "OS", "asset", "_getAssetFromPlaybackSource", "localUri", "overrideFileExtensionAndroid", "fromModule", "assertStatusValuesInBounds", "status", "RangeError", "getNativeSourceAndFullInitialStatusForLoadAsync", "_x", "_x2", "_x3", "_getNativeSourceAndFullInitialStatusForLoadAsync", "apply", "arguments", "_asyncToGenerator", "initialStatus", "downloadFirst", "fullInitialStatus", "_objectSpread", "nativeSource", "downloadAsync", "Error", "getUnloadedStatus", "error", "length", "undefined", "isLoaded", "PlaybackMixin", "playAsync", "_playAsync", "setStatusAsync", "playFromPositionAsync", "_playFromPositionAsync", "tolerances", "seekMillisToleranceAfter", "toleranceMillisAfter", "seekMillisToleranceBefore", "toleranceMillisBefore", "_x4", "pauseAsync", "_pauseAsync", "stopAsync", "_stopAsync", "setPositionAsync", "_setPositionAsync", "_x5", "setRateAsync", "_setRateAsync", "pitchCorrectionQuality", "Low", "_x6", "setVolumeAsync", "_setVolumeAsync", "_x7", "_x8", "setIsMutedAsync", "_setIsMutedAsync", "_x9", "setIsLoopingAsync", "_setIsLoopingAsync", "_x0", "setProgressUpdateIntervalAsync", "_setProgressUpdateIntervalAsync", "_x1"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/AV.ts"], "sourcesContent": ["import { Asset } from 'expo-asset';\nimport { Platform } from 'expo-modules-core';\n\nimport {\n  AVPlaybackSource,\n  AVPlaybackNativeSource,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  PitchCorrectionQuality,\n  AVPlaybackTolerance,\n} from './AV.types';\n\n// TODO add:\n//  disableFocusOnAndroid\n//  audio routes (at least did become noisy on android)\n//  pan\n//  pitch\n//  API to explicitly request audio focus / session\n//  API to select stream type on Android\n//  subtitles API\n\n/**\n * @hidden\n */\nexport const _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS: number = 500;\n\n// @needsAudit\n/**\n * The default initial `AVPlaybackStatusToSet` of all `Audio.Sound` objects and `Video` components is as follows:\n *\n * ```javascript\n * {\n *   progressUpdateIntervalMillis: 500,\n *   positionMillis: 0,\n *   shouldPlay: false,\n *   rate: 1.0,\n *   shouldCorrectPitch: false,\n *   volume: 1.0,\n *   isMuted: false,\n *   isLooping: false,\n * }\n * ```\n *\n * This default initial status can be overwritten by setting the optional `initialStatus` in `loadAsync()` or `Audio.Sound.createAsync()`.\n */\nexport const _DEFAULT_INITIAL_PLAYBACK_STATUS: AVPlaybackStatusToSet = {\n  positionMillis: 0,\n  progressUpdateIntervalMillis: _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS,\n  shouldPlay: false,\n  rate: 1.0,\n  shouldCorrectPitch: false,\n  volume: 1.0,\n  audioPan: 0,\n  isMuted: false,\n  isLooping: false,\n};\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function getNativeSourceFromSource(\n  source?: AVPlaybackSource | null\n): AVPlaybackNativeSource | null {\n  let uri: string | null = null;\n  let overridingExtension: string | null = null;\n  let headers: AVPlaybackNativeSource['headers'];\n\n  if (typeof source === 'string' && Platform.OS === 'web') {\n    return {\n      uri: source,\n      overridingExtension,\n      headers,\n    };\n  }\n\n  const asset: Asset | null = _getAssetFromPlaybackSource(source);\n  if (asset != null) {\n    uri = asset.localUri || asset.uri;\n  } else if (\n    source != null &&\n    typeof source !== 'number' &&\n    'uri' in source &&\n    typeof source.uri === 'string'\n  ) {\n    uri = source.uri;\n  }\n\n  if (uri == null) {\n    return null;\n  }\n\n  if (\n    source != null &&\n    typeof source !== 'number' &&\n    'overrideFileExtensionAndroid' in source &&\n    typeof source.overrideFileExtensionAndroid === 'string'\n  ) {\n    overridingExtension = source.overrideFileExtensionAndroid;\n  }\n\n  if (\n    source != null &&\n    typeof source !== 'number' &&\n    'headers' in source &&\n    typeof source.headers === 'object'\n  ) {\n    headers = source.headers;\n  }\n  return { uri, overridingExtension, headers };\n}\n\nfunction _getAssetFromPlaybackSource(source?: AVPlaybackSource | null): Asset | null {\n  if (source == null) {\n    return null;\n  }\n\n  let asset: Asset | null = null;\n  if (typeof source === 'number') {\n    asset = Asset.fromModule(source);\n  } else if (source instanceof Asset) {\n    asset = source;\n  }\n  return asset;\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function assertStatusValuesInBounds(status: AVPlaybackStatusToSet): void {\n  if (typeof status.rate === 'number' && (status.rate < 0 || status.rate > 32)) {\n    throw new RangeError('Rate value must be between 0.0 and 32.0');\n  }\n  if (typeof status.volume === 'number' && (status.volume < 0 || status.volume > 1)) {\n    throw new RangeError('Volume value must be between 0.0 and 1.0');\n  }\n  if (typeof status.audioPan === 'number' && (status.audioPan < -1 || status.audioPan > 1)) {\n    throw new RangeError('Pan value must be between -1.0 and 1.0');\n  }\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport async function getNativeSourceAndFullInitialStatusForLoadAsync(\n  source: AVPlaybackSource | null,\n  initialStatus: AVPlaybackStatusToSet | null,\n  downloadFirst: boolean\n): Promise<{\n  nativeSource: AVPlaybackNativeSource;\n  fullInitialStatus: AVPlaybackStatusToSet;\n}> {\n  // Get the full initial status\n  const fullInitialStatus: AVPlaybackStatusToSet =\n    initialStatus == null\n      ? _DEFAULT_INITIAL_PLAYBACK_STATUS\n      : {\n          ..._DEFAULT_INITIAL_PLAYBACK_STATUS,\n          ...initialStatus,\n        };\n  assertStatusValuesInBounds(fullInitialStatus);\n\n  if (typeof source === 'string' && Platform.OS === 'web') {\n    return {\n      nativeSource: {\n        uri: source,\n        overridingExtension: null,\n      },\n      fullInitialStatus,\n    };\n  }\n\n  // Download first if necessary.\n  const asset = _getAssetFromPlaybackSource(source);\n  if (downloadFirst && asset) {\n    // TODO we can download remote uri too once @nikki93 has integrated this into Asset\n    await asset.downloadAsync();\n  }\n\n  // Get the native source\n  const nativeSource: AVPlaybackNativeSource | null = getNativeSourceFromSource(source);\n\n  if (nativeSource === null) {\n    throw new Error(`Cannot load an AV asset from a null playback source`);\n  }\n\n  // If asset has been downloaded use the localUri\n  if (asset && asset.localUri) {\n    nativeSource.uri = asset.localUri;\n  }\n\n  return { nativeSource, fullInitialStatus };\n}\n\n// @needsAudit\n/**\n * @hidden\n */\nexport function getUnloadedStatus(error: string | null = null): AVPlaybackStatus {\n  return {\n    isLoaded: false,\n    ...(error ? { error } : null),\n  };\n}\n\n// @needsAudit\nexport interface AV {\n  /**\n   * Sets a new `AVPlaybackStatusToSet` on the `playbackObject`. This method can only be called if the media has been loaded.\n   * @param status The new `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the current playback status.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once the new status has been set successfully,\n   * or rejects if setting the new status failed. See below for details on `AVPlaybackStatus`.\n   */\n  setStatusAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus>;\n\n  /**\n   * Gets the `AVPlaybackStatus` of the `playbackObject`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject`.\n   */\n  getStatusAsync(): Promise<AVPlaybackStatus>;\n}\n\n// @needsAudit\n/**\n * On the `playbackObject` reference, the following API is provided.\n */\nexport interface Playback extends AV {\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: true })`.\n   *\n   * Playback may not start immediately after calling this function for reasons such as buffering. Make sure to update your UI based\n   * on the `isPlaying` and `isBuffering` properties of the `AVPlaybackStatus`.\n   */\n  playAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * Loads the media from `source` into memory and prepares it for playing. This must be called before calling `setStatusAsync()`\n   * or any of the convenience set status methods. This method can only be called if the `playbackObject` is in an unloaded state.\n   * @param source The source of the media.\n   * @param initialStatus The initial intended `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. For more information see the details on `AVPlaybackStatusToSet` type\n   * and the default initial playback status.\n   * @param downloadAsync If set to `true`, the system will attempt to download the resource to the device before loading.\n   * This value defaults to `true`. Note that at the moment, this will only work for `source`s of the form `require('path/to/file')` or `Asset` objects.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once it is loaded, or rejects if loading failed.\n   * The `Promise` will also reject if the `playbackObject` was already loaded. See below for details on `AVPlaybackStatus`.\n   */\n  loadAsync(\n    source: AVPlaybackSource,\n    initialStatus?: AVPlaybackStatusToSet,\n    downloadAsync?: boolean\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * Unloads the media from memory. `loadAsync()` must be called again in order to be able to play the media.\n   * > This cleanup function will be automatically called in the `Video` component's `componentWillUnmount`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once it is unloaded, or rejects if unloading failed.\n   */\n  unloadAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: true, positionMillis, seekMillisToleranceAfter: tolerances.seekMillisToleranceAfter, seekMillisToleranceBefore: tolerances.seekMillisToleranceBefore })`.\n   *\n   * Playback may not start immediately after calling this function for reasons such as buffering. Make sure to update your UI based\n   * on the `isPlaying` and `isBuffering` properties of the `AVPlaybackStatus`.\n   * @param positionMillis The desired position of playback in milliseconds.\n   * @param tolerances The tolerances are used only on iOS ([more details](#what-is-seek-tolerance-and-why-would)).\n   */\n  playFromPositionAsync(\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: false })`.\n   */\n  pauseAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ shouldPlay: false, positionMillis: 0 })`.\n   */\n  stopAsync(): Promise<AVPlaybackStatus>;\n\n  /**\n   * Replays the playback item. When using `playFromPositionAsync(0)` the item is seeked to the position at `0 ms`.\n   * On iOS this method uses internal implementation of the player and is able to play the item from the beginning immediately.\n   * @param status The new `AVPlaybackStatusToSet` of the `playbackObject`, whose values will override the current playback status.\n   * `positionMillis` and `shouldPlay` properties will be overridden with respectively `0` and `true`.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the `playbackObject` once the new status has been set successfully,\n   * or rejects if setting the new status failed.\n   */\n  replayAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ positionMillis })`.\n   * @param positionMillis The desired position of playback in milliseconds.\n   * @param tolerances The tolerances are used only on iOS ([more details](#what-is-seek-tolerance-and-why-would)).\n   */\n  setPositionAsync(\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ rate, shouldCorrectPitch, pitchCorrectionQuality })`.\n   * @param rate The desired playback rate of the media. This value must be between `0.0` and `32.0`. Only available on Android API version 23 and later and iOS.\n   * @param shouldCorrectPitch A boolean describing if we should correct the pitch for a changed rate. If set to `true`, the pitch of the audio will be corrected\n   * (so a rate different than `1.0` will timestretch the audio).\n   * @param pitchCorrectionQuality iOS time pitch algorithm setting, defaults to `Audio.PitchCorrectionQuality.Low`.\n   */\n  setRateAsync(\n    rate: number,\n    shouldCorrectPitch: boolean,\n    pitchCorrectionQuality?: PitchCorrectionQuality\n  ): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ volume, audioPan })`.\n   * Note: `audioPan` is currently only supported on Android using `androidImplementation: 'MediaPlayer'`\n   * @param volume A number between `0.0` (silence) and `1.0` (maximum volume).\n   * @param audioPan A number between `-1.0` (full left) and `1.0` (full right).\n   */\n  setVolumeAsync(volume: number, audioPan?: number): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ isMuted })`.\n   * @param isMuted A boolean describing if the audio of this media should be muted.\n   */\n  setIsMutedAsync(isMuted: boolean): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ isLooping })`.\n   * @param isLooping A boolean describing if the media should play once (`false`) or loop indefinitely (`true`).\n   */\n  setIsLoopingAsync(isLooping: boolean): Promise<AVPlaybackStatus>;\n\n  /**\n   * This is equivalent to `playbackObject.setStatusAsync({ progressUpdateIntervalMillis })`.\n   * @param progressUpdateIntervalMillis The new minimum interval in milliseconds between calls of `onPlaybackStatusUpdate`.\n   * See `setOnPlaybackStatusUpdate()` for details.\n   */\n  setProgressUpdateIntervalAsync(progressUpdateIntervalMillis: number): Promise<AVPlaybackStatus>;\n}\n\n/**\n * @hidden\n * A mixin that defines common playback methods for A/V classes, so they implement the `Playback`\n * interface.\n */\nexport const PlaybackMixin = {\n  async playAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ shouldPlay: true });\n  },\n\n  async playFromPositionAsync(\n    positionMillis: number,\n    tolerances: AVPlaybackTolerance = {}\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      positionMillis,\n      shouldPlay: true,\n      seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n      seekMillisToleranceBefore: tolerances.toleranceMillisBefore,\n    });\n  },\n\n  async pauseAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ shouldPlay: false });\n  },\n\n  async stopAsync(): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ positionMillis: 0, shouldPlay: false });\n  },\n\n  async setPositionAsync(\n    positionMillis: number,\n    tolerances: AVPlaybackTolerance = {}\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      positionMillis,\n      seekMillisToleranceAfter: tolerances.toleranceMillisAfter,\n      seekMillisToleranceBefore: tolerances.toleranceMillisBefore,\n    });\n  },\n\n  async setRateAsync(\n    rate: number,\n    shouldCorrectPitch: boolean = false,\n    pitchCorrectionQuality: PitchCorrectionQuality = PitchCorrectionQuality.Low\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({\n      rate,\n      shouldCorrectPitch,\n      pitchCorrectionQuality,\n    });\n  },\n\n  async setVolumeAsync(volume: number, audioPan?: number): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ volume, audioPan });\n  },\n\n  async setIsMutedAsync(isMuted: boolean): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ isMuted });\n  },\n\n  async setIsLoopingAsync(isLooping: boolean): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ isLooping });\n  },\n\n  async setProgressUpdateIntervalAsync(\n    progressUpdateIntervalMillis: number\n  ): Promise<AVPlaybackStatus> {\n    return (this as any as Playback).setStatusAsync({ progressUpdateIntervalMillis });\n  },\n};\n\nexport * from './AV.types';\n"], "mappings": ";;;;AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,QAAQ,QAAQ,mBAAmB;AAE5C,SAKEC,sBAAsB;AAgBxB,OAAO,IAAMC,wCAAwC,GAAW,GAAG;AAqBnE,OAAO,IAAMC,gCAAgC,GAA0B;EACrEC,cAAc,EAAE,CAAC;EACjBC,4BAA4B,EAAEH,wCAAwC;EACtEI,UAAU,EAAE,KAAK;EACjBC,IAAI,EAAE,GAAG;EACTC,kBAAkB,EAAE,KAAK;EACzBC,MAAM,EAAE,GAAG;EACXC,QAAQ,EAAE,CAAC;EACXC,OAAO,EAAE,KAAK;EACdC,SAAS,EAAE;CACZ;AAMD,OAAM,SAAUC,yBAAyBA,CACvCC,MAAgC;EAEhC,IAAIC,GAAG,GAAkB,IAAI;EAC7B,IAAIC,mBAAmB,GAAkB,IAAI;EAC7C,IAAIC,OAA0C;EAE9C,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAId,QAAQ,CAACkB,EAAE,KAAK,KAAK,EAAE;IACvD,OAAO;MACLH,GAAG,EAAED,MAAM;MACXE,mBAAmB,EAAnBA,mBAAmB;MACnBC,OAAO,EAAPA;KACD;;EAGH,IAAME,KAAK,GAAiBC,2BAA2B,CAACN,MAAM,CAAC;EAC/D,IAAIK,KAAK,IAAI,IAAI,EAAE;IACjBJ,GAAG,GAAGI,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACJ,GAAG;GAClC,MAAM,IACLD,MAAM,IAAI,IAAI,IACd,OAAOA,MAAM,KAAK,QAAQ,IAC1B,KAAK,IAAIA,MAAM,IACf,OAAOA,MAAM,CAACC,GAAG,KAAK,QAAQ,EAC9B;IACAA,GAAG,GAAGD,MAAM,CAACC,GAAG;;EAGlB,IAAIA,GAAG,IAAI,IAAI,EAAE;IACf,OAAO,IAAI;;EAGb,IACED,MAAM,IAAI,IAAI,IACd,OAAOA,MAAM,KAAK,QAAQ,IAC1B,8BAA8B,IAAIA,MAAM,IACxC,OAAOA,MAAM,CAACQ,4BAA4B,KAAK,QAAQ,EACvD;IACAN,mBAAmB,GAAGF,MAAM,CAACQ,4BAA4B;;EAG3D,IACER,MAAM,IAAI,IAAI,IACd,OAAOA,MAAM,KAAK,QAAQ,IAC1B,SAAS,IAAIA,MAAM,IACnB,OAAOA,MAAM,CAACG,OAAO,KAAK,QAAQ,EAClC;IACAA,OAAO,GAAGH,MAAM,CAACG,OAAO;;EAE1B,OAAO;IAAEF,GAAG,EAAHA,GAAG;IAAEC,mBAAmB,EAAnBA,mBAAmB;IAAEC,OAAO,EAAPA;EAAO,CAAE;AAC9C;AAEA,SAASG,2BAA2BA,CAACN,MAAgC;EACnE,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,IAAI;;EAGb,IAAIK,KAAK,GAAiB,IAAI;EAC9B,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAE;IAC9BK,KAAK,GAAGpB,KAAK,CAACwB,UAAU,CAACT,MAAM,CAAC;GACjC,MAAM,IAAIA,MAAM,YAAYf,KAAK,EAAE;IAClCoB,KAAK,GAAGL,MAAM;;EAEhB,OAAOK,KAAK;AACd;AAMA,OAAM,SAAUK,0BAA0BA,CAACC,MAA6B;EACtE,IAAI,OAAOA,MAAM,CAAClB,IAAI,KAAK,QAAQ,KAAKkB,MAAM,CAAClB,IAAI,GAAG,CAAC,IAAIkB,MAAM,CAAClB,IAAI,GAAG,EAAE,CAAC,EAAE;IAC5E,MAAM,IAAImB,UAAU,CAAC,yCAAyC,CAAC;;EAEjE,IAAI,OAAOD,MAAM,CAAChB,MAAM,KAAK,QAAQ,KAAKgB,MAAM,CAAChB,MAAM,GAAG,CAAC,IAAIgB,MAAM,CAAChB,MAAM,GAAG,CAAC,CAAC,EAAE;IACjF,MAAM,IAAIiB,UAAU,CAAC,0CAA0C,CAAC;;EAElE,IAAI,OAAOD,MAAM,CAACf,QAAQ,KAAK,QAAQ,KAAKe,MAAM,CAACf,QAAQ,GAAG,CAAC,CAAC,IAAIe,MAAM,CAACf,QAAQ,GAAG,CAAC,CAAC,EAAE;IACxF,MAAM,IAAIgB,UAAU,CAAC,wCAAwC,CAAC;;AAElE;AAMA,gBAAsBC,+CAA+CA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,gDAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAgDpE,SAAAF,iDAAA;EAAAA,gDAAA,GAAAG,iBAAA,CAhDM,WACLpB,MAA+B,EAC/BqB,aAA2C,EAC3CC,aAAsB;IAMtB,IAAMC,iBAAiB,GACrBF,aAAa,IAAI,IAAI,GACjBhC,gCAAgC,GAAAmC,aAAA,CAAAA,aAAA,KAE3BnC,gCAAgC,GAChCgC,aAAa,CACjB;IACPX,0BAA0B,CAACa,iBAAiB,CAAC;IAE7C,IAAI,OAAOvB,MAAM,KAAK,QAAQ,IAAId,QAAQ,CAACkB,EAAE,KAAK,KAAK,EAAE;MACvD,OAAO;QACLqB,YAAY,EAAE;UACZxB,GAAG,EAAED,MAAM;UACXE,mBAAmB,EAAE;SACtB;QACDqB,iBAAiB,EAAjBA;OACD;;IAIH,IAAMlB,KAAK,GAAGC,2BAA2B,CAACN,MAAM,CAAC;IACjD,IAAIsB,aAAa,IAAIjB,KAAK,EAAE;MAE1B,MAAMA,KAAK,CAACqB,aAAa,EAAE;;IAI7B,IAAMD,YAAY,GAAkC1B,yBAAyB,CAACC,MAAM,CAAC;IAErF,IAAIyB,YAAY,KAAK,IAAI,EAAE;MACzB,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;;IAIxE,IAAItB,KAAK,IAAIA,KAAK,CAACE,QAAQ,EAAE;MAC3BkB,YAAY,CAACxB,GAAG,GAAGI,KAAK,CAACE,QAAQ;;IAGnC,OAAO;MAAEkB,YAAY,EAAZA,YAAY;MAAEF,iBAAiB,EAAjBA;IAAiB,CAAE;EAC5C,CAAC;EAAA,OAAAN,gDAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAMD,OAAM,SAAUS,iBAAiBA,CAAA,EAA4B;EAAA,IAA3BC,KAAA,GAAAV,SAAA,CAAAW,MAAA,QAAAX,SAAA,QAAAY,SAAA,GAAAZ,SAAA,MAAuB,IAAI;EAC3D,OAAAK,aAAA;IACEQ,QAAQ,EAAE;EAAK,GACXH,KAAK,GAAG;IAAEA,KAAK,EAALA;EAAK,CAAE,GAAG,IAAI;AAEhC;AAkJA,OAAO,IAAMI,aAAa,GAAG;EACrBC,SAAS;IAAA,IAAAC,UAAA,GAAAf,iBAAA;MACb,OAAQ,IAAwB,CAACgB,cAAc,CAAC;QAAE5C,UAAU,EAAE;MAAI,CAAE,CAAC;IACvE,CAAC;IAAA,SAFK0C,SAASA,CAAA;MAAA,OAAAC,UAAA,CAAAjB,KAAA,OAAAC,SAAA;IAAA;IAAA,OAATe,SAAS;EAAA;EAITG,qBAAqB;IAAA,IAAAC,sBAAA,GAAAlB,iBAAA,YACzB9B,cAAsB,EACc;MAAA,IAApCiD,UAAA,GAAApB,SAAA,CAAAW,MAAA,QAAAX,SAAA,QAAAY,SAAA,GAAAZ,SAAA,MAAkC,EAAE;MAEpC,OAAQ,IAAwB,CAACiB,cAAc,CAAC;QAC9C9C,cAAc,EAAdA,cAAc;QACdE,UAAU,EAAE,IAAI;QAChBgD,wBAAwB,EAAED,UAAU,CAACE,oBAAoB;QACzDC,yBAAyB,EAAEH,UAAU,CAACI;OACvC,CAAC;IACJ,CAAC;IAAA,SAVKN,qBAAqBA,CAAAO,GAAA;MAAA,OAAAN,sBAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;IAAA,OAArBkB,qBAAqB;EAAA;EAYrBQ,UAAU;IAAA,IAAAC,WAAA,GAAA1B,iBAAA;MACd,OAAQ,IAAwB,CAACgB,cAAc,CAAC;QAAE5C,UAAU,EAAE;MAAK,CAAE,CAAC;IACxE,CAAC;IAAA,SAFKqD,UAAUA,CAAA;MAAA,OAAAC,WAAA,CAAA5B,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAV0B,UAAU;EAAA;EAIVE,SAAS;IAAA,IAAAC,UAAA,GAAA5B,iBAAA;MACb,OAAQ,IAAwB,CAACgB,cAAc,CAAC;QAAE9C,cAAc,EAAE,CAAC;QAAEE,UAAU,EAAE;MAAK,CAAE,CAAC;IAC3F,CAAC;IAAA,SAFKuD,SAASA,CAAA;MAAA,OAAAC,UAAA,CAAA9B,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAT4B,SAAS;EAAA;EAITE,gBAAgB;IAAA,IAAAC,iBAAA,GAAA9B,iBAAA,YACpB9B,cAAsB,EACc;MAAA,IAApCiD,UAAA,GAAApB,SAAA,CAAAW,MAAA,QAAAX,SAAA,QAAAY,SAAA,GAAAZ,SAAA,MAAkC,EAAE;MAEpC,OAAQ,IAAwB,CAACiB,cAAc,CAAC;QAC9C9C,cAAc,EAAdA,cAAc;QACdkD,wBAAwB,EAAED,UAAU,CAACE,oBAAoB;QACzDC,yBAAyB,EAAEH,UAAU,CAACI;OACvC,CAAC;IACJ,CAAC;IAAA,SATKM,gBAAgBA,CAAAE,GAAA;MAAA,OAAAD,iBAAA,CAAAhC,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAhB8B,gBAAgB;EAAA;EAWhBG,YAAY;IAAA,IAAAC,aAAA,GAAAjC,iBAAA,YAChB3B,IAAY,EAE+D;MAAA,IAD3EC,kBAAA,GAAAyB,SAAA,CAAAW,MAAA,QAAAX,SAAA,QAAAY,SAAA,GAAAZ,SAAA,MAA8B,KAAK;MAAA,IACnCmC,sBAAA,GAAAnC,SAAA,CAAAW,MAAA,QAAAX,SAAA,QAAAY,SAAA,GAAAZ,SAAA,MAAiDhC,sBAAsB,CAACoE,GAAG;MAE3E,OAAQ,IAAwB,CAACnB,cAAc,CAAC;QAC9C3C,IAAI,EAAJA,IAAI;QACJC,kBAAkB,EAAlBA,kBAAkB;QAClB4D,sBAAsB,EAAtBA;OACD,CAAC;IACJ,CAAC;IAAA,SAVKF,YAAYA,CAAAI,GAAA;MAAA,OAAAH,aAAA,CAAAnC,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAZiC,YAAY;EAAA;EAYZK,cAAc;IAAA,IAAAC,eAAA,GAAAtC,iBAAA,YAACzB,MAAc,EAAEC,QAAiB;MACpD,OAAQ,IAAwB,CAACwC,cAAc,CAAC;QAAEzC,MAAM,EAANA,MAAM;QAAEC,QAAQ,EAARA;MAAQ,CAAE,CAAC;IACvE,CAAC;IAAA,SAFK6D,cAAcA,CAAAE,GAAA,EAAAC,GAAA;MAAA,OAAAF,eAAA,CAAAxC,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAdsC,cAAc;EAAA;EAIdI,eAAe;IAAA,IAAAC,gBAAA,GAAA1C,iBAAA,YAACvB,OAAgB;MACpC,OAAQ,IAAwB,CAACuC,cAAc,CAAC;QAAEvC,OAAO,EAAPA;MAAO,CAAE,CAAC;IAC9D,CAAC;IAAA,SAFKgE,eAAeA,CAAAE,GAAA;MAAA,OAAAD,gBAAA,CAAA5C,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAf0C,eAAe;EAAA;EAIfG,iBAAiB;IAAA,IAAAC,kBAAA,GAAA7C,iBAAA,YAACtB,SAAkB;MACxC,OAAQ,IAAwB,CAACsC,cAAc,CAAC;QAAEtC,SAAS,EAATA;MAAS,CAAE,CAAC;IAChE,CAAC;IAAA,SAFKkE,iBAAiBA,CAAAE,GAAA;MAAA,OAAAD,kBAAA,CAAA/C,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjB6C,iBAAiB;EAAA;EAIjBG,8BAA8B;IAAA,IAAAC,+BAAA,GAAAhD,iBAAA,YAClC7B,4BAAoC;MAEpC,OAAQ,IAAwB,CAAC6C,cAAc,CAAC;QAAE7C,4BAA4B,EAA5BA;MAA4B,CAAE,CAAC;IACnF,CAAC;IAAA,SAJK4E,8BAA8BA,CAAAE,GAAA;MAAA,OAAAD,+BAAA,CAAAlD,KAAA,OAAAC,SAAA;IAAA;IAAA,OAA9BgD,8BAA8B;EAAA;CAKrC;AAED", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}