{"ast": null, "code": "import compareVersions from 'compare-versions';\nimport { CodedError } from \"./errors/CodedError\";\nvar postedWarnings = {};\nexport default function deprecate(library, deprecatedAPI) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var currentVersion = options.currentVersion,\n    versionToRemove = options.versionToRemove,\n    replacement = options.replacement;\n  var code = codeFromLibrary(library);\n  var key = `${code}:${deprecatedAPI}:${replacement}`;\n  if (!postedWarnings[key]) {\n    postedWarnings[key] = true;\n  }\n  if (!currentVersion || !versionToRemove || compareVersions(currentVersion, versionToRemove) >= 0) {\n    var _message = `\\`${deprecatedAPI}\\` has been removed`;\n    if (versionToRemove) {\n      _message = `${_message} as of version \"${versionToRemove}\"`;\n    }\n    if (replacement && replacement.length) {\n      _message = `${_message} please migrate to: \\`${replacement}\\``;\n    }\n    throw new CodedError(`ERR_DEPRECATED_API`, prependLibrary(library, _message));\n  }\n  var message = `\\`${deprecatedAPI}\\` has been deprecated`;\n  if (replacement && replacement.length) {\n    message = `${message} in favor of \\`${replacement}\\``;\n  }\n  if (versionToRemove && versionToRemove.length) {\n    message = `${message} and will be removed in version \"${versionToRemove}\"`;\n  }\n  console.warn(prependLibrary(library, message));\n}\nfunction prependLibrary(library, message) {\n  return `${library}: ${message}`;\n}\nfunction codeFromLibrary(library) {\n  var code = library.replace(/[-.]/g, '_').toUpperCase();\n  return code;\n}", "map": {"version": 3, "names": ["compareVersions", "CodedError", "postedWarnings", "deprecate", "library", "deprecatedAPI", "options", "arguments", "length", "undefined", "currentVersion", "versionToRemove", "replacement", "code", "codeFromLibrary", "key", "message", "prependLibrary", "console", "warn", "replace", "toUpperCase"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-modules-core/src/deprecate.ts"], "sourcesContent": ["import compareVersions from 'compare-versions';\n\nimport { CodedError } from './errors/CodedError';\n\nconst postedWarnings: { [key: string]: boolean } = {};\n\n/**\n * Used for deprecating values and throwing an error if a given version of Expo has passed.\n */\nexport default function deprecate(\n  library: string,\n  deprecatedAPI: string,\n  options: {\n    replacement?: string;\n    currentVersion?: string;\n    versionToRemove?: string;\n  } = {}\n): void {\n  const { currentVersion, versionToRemove, replacement } = options;\n  const code = codeFromLibrary(library);\n  const key = `${code}:${deprecatedAPI}:${replacement}`;\n  if (!postedWarnings[key]) {\n    postedWarnings[key] = true;\n  }\n\n  if (\n    !currentVersion ||\n    !versionToRemove ||\n    compareVersions(currentVersion, versionToRemove) >= 0\n  ) {\n    let message = `\\`${deprecatedAPI}\\` has been removed`;\n    if (versionToRemove) {\n      message = `${message} as of version \"${versionToRemove}\"`;\n    }\n    if (replacement && replacement.length) {\n      message = `${message} please migrate to: \\`${replacement}\\``;\n    }\n\n    throw new CodedError(`ERR_DEPRECATED_API`, prependLibrary(library, message));\n  }\n\n  let message = `\\`${deprecatedAPI}\\` has been deprecated`;\n  if (replacement && replacement.length) {\n    message = `${message} in favor of \\`${replacement}\\``;\n  }\n  if (versionToRemove && versionToRemove.length) {\n    message = `${message} and will be removed in version \"${versionToRemove}\"`;\n  }\n  console.warn(prependLibrary(library, message));\n}\n\nfunction prependLibrary(library: string, message: string): string {\n  return `${library}: ${message}`;\n}\n\n/**\n * Transform format:\n * Expo.AR -> EXPO_AR\n * expo-ar -> EXPO_AR\n */\nfunction codeFromLibrary(library: string): string {\n  const code = library.replace(/[-.]/g, '_').toUpperCase();\n  return code;\n}\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,kBAAkB;AAE9C,SAASC,UAAU;AAEnB,IAAMC,cAAc,GAA+B,EAAE;AAKrD,eAAc,SAAUC,SAASA,CAC/BC,OAAe,EACfC,aAAqB,EAKf;EAAA,IAJNC,OAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAII,EAAE;EAEN,IAAQG,cAAc,GAAmCJ,OAAO,CAAxDI,cAAc;IAAEC,eAAe,GAAkBL,OAAO,CAAxCK,eAAe;IAAEC,WAAW,GAAKN,OAAO,CAAvBM,WAAW;EACpD,IAAMC,IAAI,GAAGC,eAAe,CAACV,OAAO,CAAC;EACrC,IAAMW,GAAG,GAAG,GAAGF,IAAI,IAAIR,aAAa,IAAIO,WAAW,EAAE;EACrD,IAAI,CAACV,cAAc,CAACa,GAAG,CAAC,EAAE;IACxBb,cAAc,CAACa,GAAG,CAAC,GAAG,IAAI;;EAG5B,IACE,CAACL,cAAc,IACf,CAACC,eAAe,IAChBX,eAAe,CAACU,cAAc,EAAEC,eAAe,CAAC,IAAI,CAAC,EACrD;IACA,IAAIK,QAAO,GAAG,KAAKX,aAAa,qBAAqB;IACrD,IAAIM,eAAe,EAAE;MACnBK,QAAO,GAAG,GAAGA,QAAO,mBAAmBL,eAAe,GAAG;;IAE3D,IAAIC,WAAW,IAAIA,WAAW,CAACJ,MAAM,EAAE;MACrCQ,QAAO,GAAG,GAAGA,QAAO,yBAAyBJ,WAAW,IAAI;;IAG9D,MAAM,IAAIX,UAAU,CAAC,oBAAoB,EAAEgB,cAAc,CAACb,OAAO,EAAEY,QAAO,CAAC,CAAC;;EAG9E,IAAIA,OAAO,GAAG,KAAKX,aAAa,wBAAwB;EACxD,IAAIO,WAAW,IAAIA,WAAW,CAACJ,MAAM,EAAE;IACrCQ,OAAO,GAAG,GAAGA,OAAO,kBAAkBJ,WAAW,IAAI;;EAEvD,IAAID,eAAe,IAAIA,eAAe,CAACH,MAAM,EAAE;IAC7CQ,OAAO,GAAG,GAAGA,OAAO,oCAAoCL,eAAe,GAAG;;EAE5EO,OAAO,CAACC,IAAI,CAACF,cAAc,CAACb,OAAO,EAAEY,OAAO,CAAC,CAAC;AAChD;AAEA,SAASC,cAAcA,CAACb,OAAe,EAAEY,OAAe;EACtD,OAAO,GAAGZ,OAAO,KAAKY,OAAO,EAAE;AACjC;AAOA,SAASF,eAAeA,CAACV,OAAe;EACtC,IAAMS,IAAI,GAAGT,OAAO,CAACgB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAACC,WAAW,EAAE;EACxD,OAAOR,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}