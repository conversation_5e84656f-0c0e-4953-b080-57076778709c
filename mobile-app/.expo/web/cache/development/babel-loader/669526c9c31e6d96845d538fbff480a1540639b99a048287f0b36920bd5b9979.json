{"ast": null, "code": "var warnedKeys = {};\nexport function warnOnce(key, message) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (warnedKeys[key]) {\n      return;\n    }\n    console.warn(message);\n    warnedKeys[key] = true;\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "warnOnce", "key", "message", "process", "env", "NODE_ENV", "console", "warn"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/modules/warnOnce/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar warnedKeys = {};\n\n/**\n * A simple function that prints a warning message once per session.\n *\n * @param {string} key - The key used to ensure the message is printed once.\n *                       This should be unique to the callsite.\n * @param {string} message - The message to print\n */\nexport function warnOnce(key, message) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (warnedKeys[key]) {\n      return;\n    }\n    console.warn(message);\n    warnedKeys[key] = true;\n  }\n}"], "mappings": "AASA,IAAIA,UAAU,GAAG,CAAC,CAAC;AASnB,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAEC,OAAO,EAAE;EACrC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIN,UAAU,CAACE,GAAG,CAAC,EAAE;MACnB;IACF;IACAK,OAAO,CAACC,IAAI,CAACL,OAAO,CAAC;IACrBH,UAAU,CAACE,GAAG,CAAC,GAAG,IAAI;EACxB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}