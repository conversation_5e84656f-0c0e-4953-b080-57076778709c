{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport ExponentAV from \"./ExponentAV\";\nimport { requestFullscreen, exitFullscreen } from \"./FullscreenUtils.web\";\nexport default {\n  get name() {\n    return 'ExpoVideoManager';\n  },\n  get ScaleNone() {\n    return 'none';\n  },\n  get ScaleToFill() {\n    return 'fill';\n  },\n  get ScaleAspectFit() {\n    return 'contain';\n  },\n  get ScaleAspectFill() {\n    return 'cover';\n  },\n  setFullscreen: function () {\n    var _setFullscreen = _asyncToGenerator(function* (element, isFullScreenEnabled) {\n      if (isFullScreenEnabled) {\n        yield requestFullscreen(element);\n      } else {\n        yield exitFullscreen(element);\n      }\n      return ExponentAV.getStatusForVideo(element);\n    });\n    function setFullscreen(_x, _x2) {\n      return _setFullscreen.apply(this, arguments);\n    }\n    return setFullscreen;\n  }()\n};", "map": {"version": 3, "names": ["ExponentAV", "requestFullscreen", "exitFullscreen", "name", "ScaleNone", "ScaleToFill", "ScaleAspectFit", "ScaleAspectFill", "setFullscreen", "_setFullscreen", "_asyncToGenerator", "element", "isFullScreenEnabled", "getStatusForVideo", "_x", "_x2", "apply", "arguments"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/ExpoVideoManager.web.ts"], "sourcesContent": ["import { AVPlaybackStatus } from './AV';\nimport ExponentAV from './ExponentAV';\nimport { requestFullscreen, exitFullscreen } from './FullscreenUtils.web';\n\nexport default {\n  get name(): string {\n    return 'ExpoVideoManager';\n  },\n  get ScaleNone(): string {\n    return 'none';\n  },\n  get ScaleToFill(): string {\n    return 'fill';\n  },\n  get ScaleAspectFit(): string {\n    return 'contain';\n  },\n  get ScaleAspectFill(): string {\n    return 'cover';\n  },\n\n  async setFullscreen(\n    element: HTMLMediaElement,\n    isFullScreenEnabled: boolean\n  ): Promise<AVPlaybackStatus> {\n    if (isFullScreenEnabled) {\n      await requestFullscreen(element);\n    } else {\n      await exitFullscreen(element);\n    }\n    return ExponentAV.getStatusForVideo(element);\n  },\n};\n"], "mappings": ";AACA,OAAOA,UAAU;AACjB,SAASC,iBAAiB,EAAEC,cAAc;AAE1C,eAAe;EACb,IAAIC,IAAIA,CAAA;IACN,OAAO,kBAAkB;EAC3B,CAAC;EACD,IAAIC,SAASA,CAAA;IACX,OAAO,MAAM;EACf,CAAC;EACD,IAAIC,WAAWA,CAAA;IACb,OAAO,MAAM;EACf,CAAC;EACD,IAAIC,cAAcA,CAAA;IAChB,OAAO,SAAS;EAClB,CAAC;EACD,IAAIC,eAAeA,CAAA;IACjB,OAAO,OAAO;EAChB,CAAC;EAEKC,aAAa;IAAA,IAAAC,cAAA,GAAAC,iBAAA,YACjBC,OAAyB,EACzBC,mBAA4B;MAE5B,IAAIA,mBAAmB,EAAE;QACvB,MAAMX,iBAAiB,CAACU,OAAO,CAAC;OACjC,MAAM;QACL,MAAMT,cAAc,CAACS,OAAO,CAAC;;MAE/B,OAAOX,UAAU,CAACa,iBAAiB,CAACF,OAAO,CAAC;IAC9C,CAAC;IAAA,SAVKH,aAAaA,CAAAM,EAAA,EAAAC,GAAA;MAAA,OAAAN,cAAA,CAAAO,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAbT,aAAa;EAAA;CAWpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}