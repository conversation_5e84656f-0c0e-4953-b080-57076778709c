{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nimport * as React from 'react';\nimport findNodeHandle from \"react-native-web/dist/exports/findNodeHandle\";\nimport Image from \"react-native-web/dist/exports/Image\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport { assertStatusValuesInBounds, getNativeSourceAndFullInitialStatusForLoadAsync, getNativeSourceFromSource, getUnloadedStatus, PlaybackMixin } from \"./AV\";\nimport ExpoVideoManager from \"./ExpoVideoManager\";\nimport ExponentAV from \"./ExponentAV\";\nimport ExponentVideo from \"./ExponentVideo\";\nimport { ResizeMode } from \"./Video.types\";\nvar _STYLES = StyleSheet.create({\n  base: {\n    overflow: 'hidden'\n  },\n  poster: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    resizeMode: 'contain'\n  },\n  video: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0\n  }\n});\nvar ExpoVideoManagerConstants = ExpoVideoManager;\nvar ExpoVideoViewManager = ExpoVideoManager;\nvar Video = function (_React$Component) {\n  function Video(props) {\n    var _this;\n    _classCallCheck(this, Video);\n    _this = _callSuper(this, Video, [props]);\n    _this._nativeRef = React.createRef();\n    _this._onPlaybackStatusUpdate = null;\n    _this._handleNewStatus = function (status) {\n      if (_this.state.showPoster && status.isLoaded && (status.isPlaying || status.positionMillis !== 0)) {\n        _this.setState({\n          showPoster: false\n        });\n      }\n      if (_this.props.onPlaybackStatusUpdate) {\n        _this.props.onPlaybackStatusUpdate(status);\n      }\n      if (_this._onPlaybackStatusUpdate) {\n        _this._onPlaybackStatusUpdate(status);\n      }\n    };\n    _this._performOperationAndHandleStatusAsync = function () {\n      var _ref = _asyncToGenerator(function* (operation) {\n        var video = _this._nativeRef.current;\n        if (!video) {\n          throw new Error(`Cannot complete operation because the Video component has not yet loaded`);\n        }\n        var handle = findNodeHandle(_this._nativeRef.current);\n        var status = yield operation(handle);\n        _this._handleNewStatus(status);\n        return status;\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    _this._setFullscreen = function () {\n      var _ref2 = _asyncToGenerator(function* (value) {\n        return _this._performOperationAndHandleStatusAsync(function (tag) {\n          return ExpoVideoViewManager.setFullscreen(tag, value);\n        });\n      });\n      return function (_x2) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    _this.presentFullscreenPlayer = _asyncToGenerator(function* () {\n      return _this._setFullscreen(true);\n    });\n    _this.dismissFullscreenPlayer = _asyncToGenerator(function* () {\n      return _this._setFullscreen(false);\n    });\n    _this.getStatusAsync = _asyncToGenerator(function* () {\n      return _this._performOperationAndHandleStatusAsync(function (tag) {\n        return ExponentAV.getStatusForVideo(tag);\n      });\n    });\n    _this.loadAsync = function () {\n      var _ref6 = _asyncToGenerator(function* (source) {\n        var initialStatus = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var downloadFirst = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n        var _yield$getNativeSourc = yield getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst),\n          nativeSource = _yield$getNativeSourc.nativeSource,\n          fullInitialStatus = _yield$getNativeSourc.fullInitialStatus;\n        return _this._performOperationAndHandleStatusAsync(function (tag) {\n          return ExponentAV.loadForVideo(tag, nativeSource, fullInitialStatus);\n        });\n      });\n      return function (_x3) {\n        return _ref6.apply(this, arguments);\n      };\n    }();\n    _this.unloadAsync = _asyncToGenerator(function* () {\n      return _this._performOperationAndHandleStatusAsync(function (tag) {\n        return ExponentAV.unloadForVideo(tag);\n      });\n    });\n    _this.setStatusAsync = function () {\n      var _ref8 = _asyncToGenerator(function* (status) {\n        assertStatusValuesInBounds(status);\n        return _this._performOperationAndHandleStatusAsync(function (tag) {\n          return ExponentAV.setStatusForVideo(tag, status);\n        });\n      });\n      return function (_x4) {\n        return _ref8.apply(this, arguments);\n      };\n    }();\n    _this.replayAsync = _asyncToGenerator(function* () {\n      var status = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      if (status.positionMillis && status.positionMillis !== 0) {\n        throw new Error('Requested position after replay has to be 0.');\n      }\n      return _this._performOperationAndHandleStatusAsync(function (tag) {\n        return ExponentAV.replayVideo(tag, _objectSpread(_objectSpread({}, status), {}, {\n          positionMillis: 0,\n          shouldPlay: true\n        }));\n      });\n    });\n    _this._nativeOnPlaybackStatusUpdate = function (event) {\n      _this._handleNewStatus(event.nativeEvent);\n    };\n    _this._nativeOnLoadStart = function () {\n      if (_this.props.onLoadStart) {\n        _this.props.onLoadStart();\n      }\n    };\n    _this._nativeOnLoad = function (event) {\n      if (_this.props.onLoad) {\n        _this.props.onLoad(event.nativeEvent);\n      }\n      _this._handleNewStatus(event.nativeEvent);\n    };\n    _this._nativeOnError = function (event) {\n      var error = event.nativeEvent.error;\n      if (_this.props.onError) {\n        _this.props.onError(error);\n      }\n      _this._handleNewStatus(getUnloadedStatus(error));\n    };\n    _this._nativeOnReadyForDisplay = function (event) {\n      if (_this.props.onReadyForDisplay) {\n        _this.props.onReadyForDisplay(event.nativeEvent);\n      }\n    };\n    _this._nativeOnFullscreenUpdate = function (event) {\n      if (_this.props.onFullscreenUpdate) {\n        _this.props.onFullscreenUpdate(event.nativeEvent);\n      }\n    };\n    _this._renderPoster = function () {\n      var _this$props$PosterCom;\n      var PosterComponent = (_this$props$PosterCom = _this.props.PosterComponent) != null ? _this$props$PosterCom : Image;\n      return _this.props.usePoster && _this.state.showPoster ? React.createElement(PosterComponent, {\n        style: [_STYLES.poster, _this.props.posterStyle],\n        source: _this.props.posterSource\n      }) : null;\n    };\n    _this.state = {\n      showPoster: !!props.usePoster\n    };\n    return _this;\n  }\n  _inherits(Video, _React$Component);\n  return _createClass(Video, [{\n    key: \"setNativeProps\",\n    value: function setNativeProps(nativeProps) {\n      var nativeVideo = this._nativeRef.current;\n      if (!nativeVideo) throw new Error(`native video reference is not defined.`);\n      nativeVideo.setNativeProps(nativeProps);\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.unloadAsync().catch(function () {});\n    }\n  }, {\n    key: \"setOnPlaybackStatusUpdate\",\n    value: function setOnPlaybackStatusUpdate(onPlaybackStatusUpdate) {\n      this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n      this.getStatusAsync();\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var source = getNativeSourceFromSource(this.props.source) || undefined;\n      var nativeResizeMode = ExpoVideoManagerConstants.ScaleNone;\n      if (this.props.resizeMode) {\n        var resizeMode = this.props.resizeMode;\n        if (resizeMode === ResizeMode.STRETCH) {\n          nativeResizeMode = ExpoVideoManagerConstants.ScaleToFill;\n        } else if (resizeMode === ResizeMode.CONTAIN) {\n          nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFit;\n        } else if (resizeMode === ResizeMode.COVER) {\n          nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFill;\n        }\n      }\n      var status = _objectSpread({}, this.props.status);\n      ['progressUpdateIntervalMillis', 'positionMillis', 'shouldPlay', 'rate', 'shouldCorrectPitch', 'volume', 'isMuted', 'isLooping'].forEach(function (prop) {\n        if (prop in _this2.props) {\n          status[prop] = _this2.props[prop];\n        }\n      });\n      var nativeProps = _objectSpread(_objectSpread({}, omit(this.props, ['source', 'onPlaybackStatusUpdate', 'usePoster', 'posterSource', 'posterStyle'].concat(_toConsumableArray(Object.keys(status))))), {}, {\n        style: [_STYLES.base, this.props.style],\n        videoStyle: [_STYLES.video, this.props.videoStyle],\n        source: source,\n        resizeMode: nativeResizeMode,\n        status: status,\n        onStatusUpdate: this._nativeOnPlaybackStatusUpdate,\n        onLoadStart: this._nativeOnLoadStart,\n        onLoad: this._nativeOnLoad,\n        onError: this._nativeOnError,\n        onReadyForDisplay: this._nativeOnReadyForDisplay,\n        onFullscreenUpdate: this._nativeOnFullscreenUpdate\n      });\n      return React.createElement(View, {\n        style: nativeProps.style,\n        pointerEvents: \"box-none\"\n      }, React.createElement(ExponentVideo, _objectSpread(_objectSpread({\n        ref: this._nativeRef\n      }, nativeProps), {}, {\n        style: nativeProps.videoStyle\n      })), this._renderPoster());\n    }\n  }]);\n}(React.Component);\nfunction omit(props, propNames) {\n  var copied = _objectSpread({}, props);\n  for (var propName of propNames) {\n    delete copied[propName];\n  }\n  return copied;\n}\nObject.assign(Video.prototype, PlaybackMixin);\nexport default Video;", "map": {"version": 3, "names": ["React", "findNodeHandle", "Image", "StyleSheet", "View", "assertStatusValuesInBounds", "getNativeSourceAndFullInitialStatusForLoadAsync", "getNativeSourceFromSource", "getUnloadedStatus", "PlaybackMixin", "ExpoVideoManager", "ExponentAV", "ExponentVideo", "ResizeMode", "_STYLES", "create", "base", "overflow", "poster", "position", "left", "top", "right", "bottom", "resizeMode", "video", "ExpoVideoManagerConstants", "ExpoVideoViewManager", "Video", "_React$Component", "props", "_this", "_classCallCheck", "_callSuper", "_nativeRef", "createRef", "_onPlaybackStatusUpdate", "_handleNewStatus", "status", "state", "showPoster", "isLoaded", "isPlaying", "<PERSON><PERSON><PERSON><PERSON>", "setState", "onPlaybackStatusUpdate", "_performOperationAndHandleStatusAsync", "_ref", "_asyncToGenerator", "operation", "current", "Error", "handle", "_x", "apply", "arguments", "_setFullscreen", "_ref2", "value", "tag", "setFullscreen", "_x2", "presentFullscreenPlayer", "dismissFullscreenPlayer", "getStatusAsync", "getStatusForVideo", "loadAsync", "_ref6", "source", "initialStatus", "length", "undefined", "downloadFirst", "_yield$getNativeSourc", "nativeSource", "fullInitialStatus", "loadForVideo", "_x3", "unloadAsync", "unloadForVideo", "setStatusAsync", "_ref8", "setStatusForVideo", "_x4", "replayAsync", "replayVideo", "_objectSpread", "shouldPlay", "_nativeOnPlaybackStatusUpdate", "event", "nativeEvent", "_nativeOnLoadStart", "onLoadStart", "_nativeOnLoad", "onLoad", "_nativeOnError", "error", "onError", "_nativeOnReadyForDisplay", "onReadyForDisplay", "_nativeOnFullscreenUpdate", "onFullscreenUpdate", "_renderPoster", "_this$props$PosterCom", "PosterComponent", "usePoster", "createElement", "style", "posterStyle", "posterSource", "_inherits", "_createClass", "key", "setNativeProps", "nativeProps", "nativeVideo", "componentWillUnmount", "catch", "setOnPlaybackStatusUpdate", "render", "_this2", "nativeResizeMode", "ScaleNone", "STRETCH", "ScaleToFill", "CONTAIN", "ScaleAspectFit", "COVER", "ScaleAspectFill", "for<PERSON>ach", "prop", "omit", "concat", "_toConsumableArray", "Object", "keys", "videoStyle", "onStatusUpdate", "pointerEvents", "ref", "Component", "propNames", "copied", "propName", "assign", "prototype"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Video.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { findNodeHandle, Image, NativeMethods, StyleSheet, View } from 'react-native';\n\nimport {\n  assertStatusValuesInBounds,\n  getNativeSourceAndFullInitialStatusForLoadAsync,\n  getNativeSourceFromSource,\n  getUnloadedStatus,\n  Playback,\n  PlaybackMixin,\n  AVPlaybackSource,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  AVPlaybackTolerance,\n} from './AV';\nimport ExpoVideoManager from './ExpoVideoManager';\nimport ExponentAV from './ExponentAV';\nimport ExponentVideo from './ExponentVideo';\nimport {\n  ExponentVideoComponent,\n  VideoFullscreenUpdateEvent,\n  VideoNativeProps,\n  VideoProps,\n  VideoReadyForDisplayEvent,\n  ResizeMode,\n  VideoState,\n} from './Video.types';\n\nconst _STYLES = StyleSheet.create({\n  base: {\n    overflow: 'hidden',\n  },\n  poster: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n    resizeMode: 'contain',\n  },\n  video: {\n    position: 'absolute',\n    left: 0,\n    top: 0,\n    right: 0,\n    bottom: 0,\n  },\n});\n\n// On a real device UIManager should be present, however when running offline tests with jest-expo\n// we have to use the provided native module mock to access constants\nconst ExpoVideoManagerConstants = ExpoVideoManager;\nconst ExpoVideoViewManager = ExpoVideoManager;\n\nclass Video extends React.Component<VideoProps, VideoState> implements Playback {\n  _nativeRef = React.createRef<InstanceType<ExponentVideoComponent> & NativeMethods>();\n  _onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null;\n\n  constructor(props: VideoProps) {\n    super(props);\n    this.state = {\n      showPoster: !!props.usePoster,\n    };\n  }\n\n  /**\n   * @hidden\n   */\n  setNativeProps(nativeProps: VideoNativeProps) {\n    const nativeVideo = this._nativeRef.current;\n    if (!nativeVideo) throw new Error(`native video reference is not defined.`);\n    nativeVideo.setNativeProps(nativeProps);\n  }\n\n  // Internal methods\n\n  _handleNewStatus = (status: AVPlaybackStatus) => {\n    if (\n      this.state.showPoster &&\n      status.isLoaded &&\n      (status.isPlaying || status.positionMillis !== 0)\n    ) {\n      this.setState({ showPoster: false });\n    }\n\n    if (this.props.onPlaybackStatusUpdate) {\n      this.props.onPlaybackStatusUpdate(status);\n    }\n    if (this._onPlaybackStatusUpdate) {\n      this._onPlaybackStatusUpdate(status);\n    }\n  };\n\n  _performOperationAndHandleStatusAsync = async (\n    operation: (tag: number) => Promise<AVPlaybackStatus>\n  ): Promise<AVPlaybackStatus> => {\n    const video = this._nativeRef.current;\n    if (!video) {\n      throw new Error(`Cannot complete operation because the Video component has not yet loaded`);\n    }\n\n    const handle = findNodeHandle(this._nativeRef.current)!;\n    const status: AVPlaybackStatus = await operation(handle);\n    this._handleNewStatus(status);\n    return status;\n  };\n\n  // Fullscreening API\n\n  _setFullscreen = async (value: boolean): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExpoVideoViewManager.setFullscreen(tag, value)\n    );\n  };\n\n  /**\n   * This presents a fullscreen view of your video component on top of your app's UI. Note that even if `useNativeControls` is set to `false`,\n   * native controls will be visible in fullscreen mode.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the video once the fullscreen player has finished presenting,\n   * or rejects if there was an error, or if this was called on an Android device.\n   */\n  presentFullscreenPlayer = async (): Promise<AVPlaybackStatus> => {\n    return this._setFullscreen(true);\n  };\n\n  /**\n   * This dismisses the fullscreen video view.\n   * @return A `Promise` that is fulfilled with the `AVPlaybackStatus` of the video once the fullscreen player has finished dismissing,\n   * or rejects if there was an error, or if this was called on an Android device.\n   */\n  dismissFullscreenPlayer = async (): Promise<AVPlaybackStatus> => {\n    return this._setFullscreen(false);\n  };\n\n  // ### Unified playback API ### (consistent with Audio.js)\n  // All calls automatically call onPlaybackStatusUpdate as a side effect.\n\n  /**\n   * @hidden\n   */\n  getStatusAsync = async (): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.getStatusForVideo(tag)\n    );\n  };\n\n  /**\n   * @hidden\n   */\n  loadAsync = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    downloadFirst: boolean = true\n  ): Promise<AVPlaybackStatus> => {\n    const { nativeSource, fullInitialStatus } =\n      await getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst);\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.loadForVideo(tag, nativeSource, fullInitialStatus)\n    );\n  };\n\n  /**\n   * Equivalent to setting URI to `null`.\n   * @hidden\n   */\n  unloadAsync = async (): Promise<AVPlaybackStatus> => {\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.unloadForVideo(tag)\n    );\n  };\n\n  componentWillUnmount() {\n    // Auto unload video to perform necessary cleanup safely\n    this.unloadAsync().catch(() => {\n      // Ignored rejection. Sometimes the unloadAsync code is executed when video is already unloaded.\n      // In such cases, it throws:\n      // \"[Unhandled promise rejection: Error: Invalid view returned from registry,\n      //  expecting EXVideo, got: (null)]\"\n    });\n  }\n\n  /**\n   * Set status API, only available while `isLoaded = true`.\n   * @hidden\n   */\n  setStatusAsync = async (status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus> => {\n    assertStatusValuesInBounds(status);\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.setStatusForVideo(tag, status)\n    );\n  };\n\n  /**\n   * @hidden\n   */\n  replayAsync = async (status: AVPlaybackStatusToSet = {}): Promise<AVPlaybackStatus> => {\n    if (status.positionMillis && status.positionMillis !== 0) {\n      throw new Error('Requested position after replay has to be 0.');\n    }\n\n    return this._performOperationAndHandleStatusAsync((tag: number) =>\n      ExponentAV.replayVideo(tag, {\n        ...status,\n        positionMillis: 0,\n        shouldPlay: true,\n      })\n    );\n  };\n\n  /**\n   * Sets a function to be called regularly with the `AVPlaybackStatus` of the playback object.\n   *\n   * `onPlaybackStatusUpdate` will be called whenever a call to the API for this playback object completes\n   * (such as `setStatusAsync()`, `getStatusAsync()`, or `unloadAsync()`), nd will also be called at regular intervals\n   * while the media is in the loaded state.\n   *\n   * Set `progressUpdateIntervalMillis` via `setStatusAsync()` or `setProgressUpdateIntervalAsync()` to modify\n   * the interval with which `onPlaybackStatusUpdate` is called while loaded.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `AVPlaybackStatus`.\n   */\n  setOnPlaybackStatusUpdate(onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null) {\n    this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n    this.getStatusAsync();\n  }\n\n  // Methods of the Playback interface that are set via PlaybackMixin\n  playAsync!: () => Promise<AVPlaybackStatus>;\n  playFromPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  pauseAsync!: () => Promise<AVPlaybackStatus>;\n  stopAsync!: () => Promise<AVPlaybackStatus>;\n  setPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  setRateAsync!: (rate: number, shouldCorrectPitch: boolean) => Promise<AVPlaybackStatus>;\n  setVolumeAsync!: (volume: number, audioPan?: number) => Promise<AVPlaybackStatus>;\n  setIsMutedAsync!: (isMuted: boolean) => Promise<AVPlaybackStatus>;\n  setIsLoopingAsync!: (isLooping: boolean) => Promise<AVPlaybackStatus>;\n  setProgressUpdateIntervalAsync!: (\n    progressUpdateIntervalMillis: number\n  ) => Promise<AVPlaybackStatus>;\n\n  // Callback wrappers\n\n  _nativeOnPlaybackStatusUpdate = (event: { nativeEvent: AVPlaybackStatus }) => {\n    this._handleNewStatus(event.nativeEvent);\n  };\n\n  // TODO make sure we are passing the right stuff\n  _nativeOnLoadStart = () => {\n    if (this.props.onLoadStart) {\n      this.props.onLoadStart();\n    }\n  };\n\n  _nativeOnLoad = (event: { nativeEvent: AVPlaybackStatus }) => {\n    if (this.props.onLoad) {\n      this.props.onLoad(event.nativeEvent);\n    }\n    this._handleNewStatus(event.nativeEvent);\n  };\n\n  _nativeOnError = (event: { nativeEvent: { error: string } }) => {\n    const error: string = event.nativeEvent.error;\n    if (this.props.onError) {\n      this.props.onError(error);\n    }\n    this._handleNewStatus(getUnloadedStatus(error));\n  };\n\n  _nativeOnReadyForDisplay = (event: { nativeEvent: VideoReadyForDisplayEvent }) => {\n    if (this.props.onReadyForDisplay) {\n      this.props.onReadyForDisplay(event.nativeEvent);\n    }\n  };\n\n  _nativeOnFullscreenUpdate = (event: { nativeEvent: VideoFullscreenUpdateEvent }) => {\n    if (this.props.onFullscreenUpdate) {\n      this.props.onFullscreenUpdate(event.nativeEvent);\n    }\n  };\n\n  _renderPoster = () => {\n    const PosterComponent = this.props.PosterComponent ?? Image;\n\n    return this.props.usePoster && this.state.showPoster ? (\n      <PosterComponent\n        style={[_STYLES.poster, this.props.posterStyle]}\n        source={this.props.posterSource!}\n      />\n    ) : null;\n  };\n\n  render() {\n    const source = getNativeSourceFromSource(this.props.source) || undefined;\n\n    let nativeResizeMode = ExpoVideoManagerConstants.ScaleNone;\n    if (this.props.resizeMode) {\n      const resizeMode = this.props.resizeMode;\n      if (resizeMode === ResizeMode.STRETCH) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleToFill;\n      } else if (resizeMode === ResizeMode.CONTAIN) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFit;\n      } else if (resizeMode === ResizeMode.COVER) {\n        nativeResizeMode = ExpoVideoManagerConstants.ScaleAspectFill;\n      }\n    }\n\n    // Set status via individual props\n    const status: AVPlaybackStatusToSet = { ...this.props.status };\n    [\n      'progressUpdateIntervalMillis',\n      'positionMillis',\n      'shouldPlay',\n      'rate',\n      'shouldCorrectPitch',\n      'volume',\n      'isMuted',\n      'isLooping',\n    ].forEach((prop) => {\n      if (prop in this.props) {\n        status[prop] = this.props[prop];\n      }\n    });\n\n    // Replace selected native props\n    const nativeProps: VideoNativeProps = {\n      ...omit(this.props, [\n        'source',\n        'onPlaybackStatusUpdate',\n        'usePoster',\n        'posterSource',\n        'posterStyle',\n        ...Object.keys(status),\n      ]),\n      style: [_STYLES.base, this.props.style],\n      videoStyle: [_STYLES.video, this.props.videoStyle],\n      source,\n      resizeMode: nativeResizeMode,\n      status,\n      onStatusUpdate: this._nativeOnPlaybackStatusUpdate,\n      onLoadStart: this._nativeOnLoadStart,\n      onLoad: this._nativeOnLoad,\n      onError: this._nativeOnError,\n      onReadyForDisplay: this._nativeOnReadyForDisplay,\n      onFullscreenUpdate: this._nativeOnFullscreenUpdate,\n    };\n\n    return (\n      <View style={nativeProps.style} pointerEvents=\"box-none\">\n        <ExponentVideo ref={this._nativeRef} {...nativeProps} style={nativeProps.videoStyle} />\n        {this._renderPoster()}\n      </View>\n    );\n  }\n}\n\nfunction omit(props: Record<string, any>, propNames: string[]) {\n  const copied = { ...props };\n  for (const propName of propNames) {\n    delete copied[propName];\n  }\n  return copied;\n}\n\nObject.assign(Video.prototype, PlaybackMixin);\n\n// note(simek): TypeDoc cannot resolve correctly name of inline and default exported class\nexport default Video;\n"], "mappings": ";;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAC,OAAAC,cAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAG/B,SACEC,0BAA0B,EAC1BC,+CAA+C,EAC/CC,yBAAyB,EACzBC,iBAAiB,EAEjBC,aAAa;AAMf,OAAOC,gBAAgB;AACvB,OAAOC,UAAU;AACjB,OAAOC,aAAa;AACpB,SAMEC,UAAU;AAIZ,IAAMC,OAAO,GAAGX,UAAU,CAACY,MAAM,CAAC;EAChCC,IAAI,EAAE;IACJC,QAAQ,EAAE;GACX;EACDC,MAAM,EAAE;IACNC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;GACb;EACDC,KAAK,EAAE;IACLN,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;;CAEX,CAAC;AAIF,IAAMG,yBAAyB,GAAGhB,gBAAgB;AAClD,IAAMiB,oBAAoB,GAAGjB,gBAAgB;AAAC,IAExCkB,KAAM,aAAAC,gBAAA;EAIV,SAAAD,MAAYE,KAAiB;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAJ,KAAA;IAC3BG,KAAA,GAAAE,UAAA,OAAAL,KAAA,GAAME,KAAK;IAAEC,KAAA,CAJfG,UAAU,GAAGlC,KAAK,CAACmC,SAAS,EAAwD;IAAAJ,KAAA,CACpFK,uBAAuB,GAAgD,IAAI;IAAAL,KAAA,CAoB3EM,gBAAgB,GAAG,UAACC,MAAwB,EAAI;MAC9C,IACEP,KAAA,CAAKQ,KAAK,CAACC,UAAU,IACrBF,MAAM,CAACG,QAAQ,KACdH,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,cAAc,KAAK,CAAC,CAAC,EACjD;QACAZ,KAAA,CAAKa,QAAQ,CAAC;UAAEJ,UAAU,EAAE;QAAK,CAAE,CAAC;;MAGtC,IAAIT,KAAA,CAAKD,KAAK,CAACe,sBAAsB,EAAE;QACrCd,KAAA,CAAKD,KAAK,CAACe,sBAAsB,CAACP,MAAM,CAAC;;MAE3C,IAAIP,KAAA,CAAKK,uBAAuB,EAAE;QAChCL,KAAA,CAAKK,uBAAuB,CAACE,MAAM,CAAC;;IAExC,CAAC;IAAAP,KAAA,CAEDe,qCAAqC;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WACtCC,SAAqD,EACxB;QAC7B,IAAMxB,KAAK,GAAGM,KAAA,CAAKG,UAAU,CAACgB,OAAO;QACrC,IAAI,CAACzB,KAAK,EAAE;UACV,MAAM,IAAI0B,KAAK,CAAC,0EAA0E,CAAC;;QAG7F,IAAMC,MAAM,GAAGnD,cAAc,CAAC8B,KAAA,CAAKG,UAAU,CAACgB,OAAO,CAAE;QACvD,IAAMZ,MAAM,SAA2BW,SAAS,CAACG,MAAM,CAAC;QACxDrB,KAAA,CAAKM,gBAAgB,CAACC,MAAM,CAAC;QAC7B,OAAOA,MAAM;MACf,CAAC;MAAA,iBAAAe,EAAA;QAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;IAAA;IAAAxB,KAAA,CAIDyB,cAAc;MAAA,IAAAC,KAAA,GAAAT,iBAAA,CAAG,WAAOU,KAAc,EAA+B;QACnE,OAAO3B,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;UAAA,OAC5DhC,oBAAoB,CAACiC,aAAa,CAACD,GAAG,EAAED,KAAK,CAAC;QAAA,EAC/C;MACH,CAAC;MAAA,iBAAAG,GAAA;QAAA,OAAAJ,KAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;IAAA;IAAAxB,KAAA,CAQD+B,uBAAuB,GAAAd,iBAAA,CAAG,aAAsC;MAC9D,OAAOjB,KAAA,CAAKyB,cAAc,CAAC,IAAI,CAAC;IAClC,CAAC;IAAAzB,KAAA,CAODgC,uBAAuB,GAAAf,iBAAA,CAAG,aAAsC;MAC9D,OAAOjB,KAAA,CAAKyB,cAAc,CAAC,KAAK,CAAC;IACnC,CAAC;IAAAzB,KAAA,CAQDiC,cAAc,GAAAhB,iBAAA,CAAG,aAAsC;MACrD,OAAOjB,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;QAAA,OAC5DhD,UAAU,CAACsD,iBAAiB,CAACN,GAAG,CAAC;MAAA,EAClC;IACH,CAAC;IAAA5B,KAAA,CAKDmC,SAAS;MAAA,IAAAC,KAAA,GAAAnB,iBAAA,CAAG,WACVoB,MAAwB,EAGK;QAAA,IAF7BC,aAAA,GAAAd,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAgB,SAAA,GAAAhB,SAAA,MAAuC,EAAE;QAAA,IACzCiB,aAAA,GAAAjB,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAgB,SAAA,GAAAhB,SAAA,MAAyB,IAAI;QAE7B,IAAAkB,qBAAA,SACQnE,+CAA+C,CAAC8D,MAAM,EAAEC,aAAa,EAAEG,aAAa,CAAC;UADrFE,YAAY,GAAAD,qBAAA,CAAZC,YAAY;UAAEC,iBAAiB,GAAAF,qBAAA,CAAjBE,iBAAiB;QAEvC,OAAO5C,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;UAAA,OAC5DhD,UAAU,CAACiE,YAAY,CAACjB,GAAG,EAAEe,YAAY,EAAEC,iBAAiB,CAAC;QAAA,EAC9D;MACH,CAAC;MAAA,iBAAAE,GAAA;QAAA,OAAAV,KAAA,CAAAb,KAAA,OAAAC,SAAA;MAAA;IAAA;IAAAxB,KAAA,CAMD+C,WAAW,GAAA9B,iBAAA,CAAG,aAAsC;MAClD,OAAOjB,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;QAAA,OAC5DhD,UAAU,CAACoE,cAAc,CAACpB,GAAG,CAAC;MAAA,EAC/B;IACH,CAAC;IAAA5B,KAAA,CAgBDiD,cAAc;MAAA,IAAAC,KAAA,GAAAjC,iBAAA,CAAG,WAAOV,MAA6B,EAA+B;QAClFjC,0BAA0B,CAACiC,MAAM,CAAC;QAClC,OAAOP,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;UAAA,OAC5DhD,UAAU,CAACuE,iBAAiB,CAACvB,GAAG,EAAErB,MAAM,CAAC;QAAA,EAC1C;MACH,CAAC;MAAA,iBAAA6C,GAAA;QAAA,OAAAF,KAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;IAAA;IAAAxB,KAAA,CAKDqD,WAAW,GAAApC,iBAAA,CAAG,aAAwE;MAAA,IAAjEV,MAAA,GAAAiB,SAAA,CAAAe,MAAA,QAAAf,SAAA,QAAAgB,SAAA,GAAAhB,SAAA,MAAgC,EAAE;MACrD,IAAIjB,MAAM,CAACK,cAAc,IAAIL,MAAM,CAACK,cAAc,KAAK,CAAC,EAAE;QACxD,MAAM,IAAIQ,KAAK,CAAC,8CAA8C,CAAC;;MAGjE,OAAOpB,KAAA,CAAKe,qCAAqC,CAAC,UAACa,GAAW;QAAA,OAC5DhD,UAAU,CAAC0E,WAAW,CAAC1B,GAAG,EAAA2B,aAAA,CAAAA,aAAA,KACrBhD,MAAM;UACTK,cAAc,EAAE,CAAC;UACjB4C,UAAU,EAAE;QAAI,EACjB,CAAC;MAAA,EACH;IACH,CAAC;IAAAxD,KAAA,CAyCDyD,6BAA6B,GAAG,UAACC,KAAwC,EAAI;MAC3E1D,KAAA,CAAKM,gBAAgB,CAACoD,KAAK,CAACC,WAAW,CAAC;IAC1C,CAAC;IAAA3D,KAAA,CAGD4D,kBAAkB,GAAG,YAAK;MACxB,IAAI5D,KAAA,CAAKD,KAAK,CAAC8D,WAAW,EAAE;QAC1B7D,KAAA,CAAKD,KAAK,CAAC8D,WAAW,EAAE;;IAE5B,CAAC;IAAA7D,KAAA,CAED8D,aAAa,GAAG,UAACJ,KAAwC,EAAI;MAC3D,IAAI1D,KAAA,CAAKD,KAAK,CAACgE,MAAM,EAAE;QACrB/D,KAAA,CAAKD,KAAK,CAACgE,MAAM,CAACL,KAAK,CAACC,WAAW,CAAC;;MAEtC3D,KAAA,CAAKM,gBAAgB,CAACoD,KAAK,CAACC,WAAW,CAAC;IAC1C,CAAC;IAAA3D,KAAA,CAEDgE,cAAc,GAAG,UAACN,KAAyC,EAAI;MAC7D,IAAMO,KAAK,GAAWP,KAAK,CAACC,WAAW,CAACM,KAAK;MAC7C,IAAIjE,KAAA,CAAKD,KAAK,CAACmE,OAAO,EAAE;QACtBlE,KAAA,CAAKD,KAAK,CAACmE,OAAO,CAACD,KAAK,CAAC;;MAE3BjE,KAAA,CAAKM,gBAAgB,CAAC7B,iBAAiB,CAACwF,KAAK,CAAC,CAAC;IACjD,CAAC;IAAAjE,KAAA,CAEDmE,wBAAwB,GAAG,UAACT,KAAiD,EAAI;MAC/E,IAAI1D,KAAA,CAAKD,KAAK,CAACqE,iBAAiB,EAAE;QAChCpE,KAAA,CAAKD,KAAK,CAACqE,iBAAiB,CAACV,KAAK,CAACC,WAAW,CAAC;;IAEnD,CAAC;IAAA3D,KAAA,CAEDqE,yBAAyB,GAAG,UAACX,KAAkD,EAAI;MACjF,IAAI1D,KAAA,CAAKD,KAAK,CAACuE,kBAAkB,EAAE;QACjCtE,KAAA,CAAKD,KAAK,CAACuE,kBAAkB,CAACZ,KAAK,CAACC,WAAW,CAAC;;IAEpD,CAAC;IAAA3D,KAAA,CAEDuE,aAAa,GAAG,YAAK;MAAA,IAAAC,qBAAA;MACnB,IAAMC,eAAe,IAAAD,qBAAA,GAAGxE,KAAA,CAAKD,KAAK,CAAC0E,eAAe,YAAAD,qBAAA,GAAIrG,KAAK;MAE3D,OAAO6B,KAAA,CAAKD,KAAK,CAAC2E,SAAS,IAAI1E,KAAA,CAAKQ,KAAK,CAACC,UAAU,GAClDxC,KAAA,CAAA0G,aAAA,CAACF,eAAe;QACdG,KAAK,EAAE,CAAC7F,OAAO,CAACI,MAAM,EAAEa,KAAA,CAAKD,KAAK,CAAC8E,WAAW,CAAC;QAC/CxC,MAAM,EAAErC,KAAA,CAAKD,KAAK,CAAC+E;MAAa,EAChC,GACA,IAAI;IACV,CAAC;IA3OC9E,KAAA,CAAKQ,KAAK,GAAG;MACXC,UAAU,EAAE,CAAC,CAACV,KAAK,CAAC2E;KACrB;IAAC,OAAA1E,KAAA;EACJ;EAAC+E,SAAA,CAAAlF,KAAA,EAAAC,gBAAA;EAAA,OAAAkF,YAAA,CAAAnF,KAAA;IAAAoF,GAAA;IAAAtD,KAAA,EAKD,SAAAuD,cAAcA,CAACC,WAA6B;MAC1C,IAAMC,WAAW,GAAG,IAAI,CAACjF,UAAU,CAACgB,OAAO;MAC3C,IAAI,CAACiE,WAAW,EAAE,MAAM,IAAIhE,KAAK,CAAC,wCAAwC,CAAC;MAC3EgE,WAAW,CAACF,cAAc,CAACC,WAAW,CAAC;IACzC;EAAC;IAAAF,GAAA;IAAAtD,KAAA,EAmGD,SAAA0D,oBAAoBA,CAAA;MAElB,IAAI,CAACtC,WAAW,EAAE,CAACuC,KAAK,CAAC,YAAK,CAK9B,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAtD,KAAA,EA0CD,SAAA4D,yBAAyBA,CAACzE,sBAAmE;MAC3F,IAAI,CAACT,uBAAuB,GAAGS,sBAAsB;MACrD,IAAI,CAACmB,cAAc,EAAE;IACvB;EAAC;IAAAgD,GAAA;IAAAtD,KAAA,EAyED,SAAA6D,MAAMA,CAAA;MAAA,IAAAC,MAAA;MACJ,IAAMpD,MAAM,GAAG7D,yBAAyB,CAAC,IAAI,CAACuB,KAAK,CAACsC,MAAM,CAAC,IAAIG,SAAS;MAExE,IAAIkD,gBAAgB,GAAG/F,yBAAyB,CAACgG,SAAS;MAC1D,IAAI,IAAI,CAAC5F,KAAK,CAACN,UAAU,EAAE;QACzB,IAAMA,UAAU,GAAG,IAAI,CAACM,KAAK,CAACN,UAAU;QACxC,IAAIA,UAAU,KAAKX,UAAU,CAAC8G,OAAO,EAAE;UACrCF,gBAAgB,GAAG/F,yBAAyB,CAACkG,WAAW;SACzD,MAAM,IAAIpG,UAAU,KAAKX,UAAU,CAACgH,OAAO,EAAE;UAC5CJ,gBAAgB,GAAG/F,yBAAyB,CAACoG,cAAc;SAC5D,MAAM,IAAItG,UAAU,KAAKX,UAAU,CAACkH,KAAK,EAAE;UAC1CN,gBAAgB,GAAG/F,yBAAyB,CAACsG,eAAe;;;MAKhE,IAAM1F,MAAM,GAAAgD,aAAA,KAA+B,IAAI,CAACxD,KAAK,CAACQ,MAAM,CAAE;MAC9D,CACE,8BAA8B,EAC9B,gBAAgB,EAChB,YAAY,EACZ,MAAM,EACN,oBAAoB,EACpB,QAAQ,EACR,SAAS,EACT,WAAW,CACZ,CAAC2F,OAAO,CAAC,UAACC,IAAI,EAAI;QACjB,IAAIA,IAAI,IAAIV,MAAI,CAAC1F,KAAK,EAAE;UACtBQ,MAAM,CAAC4F,IAAI,CAAC,GAAGV,MAAI,CAAC1F,KAAK,CAACoG,IAAI,CAAC;;MAEnC,CAAC,CAAC;MAGF,IAAMhB,WAAW,GAAA5B,aAAA,CAAAA,aAAA,KACZ6C,IAAI,CAAC,IAAI,CAACrG,KAAK,GAChB,QAAQ,EACR,wBAAwB,EACxB,WAAW,EACX,cAAc,EACd,aAAa,EAAAsG,MAAA,CAAAC,kBAAA,CACVC,MAAM,CAACC,IAAI,CAACjG,MAAM,CAAC,EACvB,CAAC;QACFqE,KAAK,EAAE,CAAC7F,OAAO,CAACE,IAAI,EAAE,IAAI,CAACc,KAAK,CAAC6E,KAAK,CAAC;QACvC6B,UAAU,EAAE,CAAC1H,OAAO,CAACW,KAAK,EAAE,IAAI,CAACK,KAAK,CAAC0G,UAAU,CAAC;QAClDpE,MAAM,EAANA,MAAM;QACN5C,UAAU,EAAEiG,gBAAgB;QAC5BnF,MAAM,EAANA,MAAM;QACNmG,cAAc,EAAE,IAAI,CAACjD,6BAA6B;QAClDI,WAAW,EAAE,IAAI,CAACD,kBAAkB;QACpCG,MAAM,EAAE,IAAI,CAACD,aAAa;QAC1BI,OAAO,EAAE,IAAI,CAACF,cAAc;QAC5BI,iBAAiB,EAAE,IAAI,CAACD,wBAAwB;QAChDG,kBAAkB,EAAE,IAAI,CAACD;MAAyB,EACnD;MAED,OACEpG,KAAA,CAAA0G,aAAA,CAACtG,IAAI;QAACuG,KAAK,EAAEO,WAAW,CAACP,KAAK;QAAE+B,aAAa,EAAC;MAAU,GACtD1I,KAAA,CAAA0G,aAAA,CAAC9F,aAAa,EAAA0E,aAAA,CAAAA,aAAA;QAACqD,GAAG,EAAE,IAAI,CAACzG;MAAU,GAAMgF,WAAW;QAAEP,KAAK,EAAEO,WAAW,CAACsB;MAAU,GAAI,EACtF,IAAI,CAAClC,aAAa,EAAE,CAChB;IAEX;EAAC;AAAA,EAhTiBtG,KAAK,CAAC4I,SAAiC;AAmT3D,SAAST,IAAIA,CAACrG,KAA0B,EAAE+G,SAAmB;EAC3D,IAAMC,MAAM,GAAAxD,aAAA,KAAQxD,KAAK,CAAE;EAC3B,KAAK,IAAMiH,QAAQ,IAAIF,SAAS,EAAE;IAChC,OAAOC,MAAM,CAACC,QAAQ,CAAC;;EAEzB,OAAOD,MAAM;AACf;AAEAR,MAAM,CAACU,MAAM,CAACpH,KAAK,CAACqH,SAAS,EAAExI,aAAa,CAAC;AAG7C,eAAemB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}