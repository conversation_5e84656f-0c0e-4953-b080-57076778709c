{"ast": null, "code": "export { createRoot } from 'react-dom/client';", "map": {"version": 3, "names": ["createRoot"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo/src/launch/createRoot.tsx"], "sourcesContent": ["export { createRoot } from 'react-dom/client';\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}