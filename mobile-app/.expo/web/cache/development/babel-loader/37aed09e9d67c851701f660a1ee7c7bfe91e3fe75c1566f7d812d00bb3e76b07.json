{"ast": null, "code": "export var ResizeMode;\n(function (ResizeMode) {\n  ResizeMode[\"CONTAIN\"] = \"contain\";\n  ResizeMode[\"COVER\"] = \"cover\";\n  ResizeMode[\"STRETCH\"] = \"stretch\";\n})(ResizeMode || (ResizeMode = {}));\nexport var VideoFullscreenUpdate;\n(function (VideoFullscreenUpdate) {\n  VideoFullscreenUpdate[VideoFullscreenUpdate[\"PLAYER_WILL_PRESENT\"] = 0] = \"PLAYER_WILL_PRESENT\";\n  VideoFullscreenUpdate[VideoFullscreenUpdate[\"PLAYER_DID_PRESENT\"] = 1] = \"PLAYER_DID_PRESENT\";\n  VideoFullscreenUpdate[VideoFullscreenUpdate[\"PLAYER_WILL_DISMISS\"] = 2] = \"PLAYER_WILL_DISMISS\";\n  VideoFullscreenUpdate[VideoFullscreenUpdate[\"PLAYER_DID_DISMISS\"] = 3] = \"PLAYER_DID_DISMISS\";\n})(VideoFullscreenUpdate || (VideoFullscreenUpdate = {}));", "map": {"version": 3, "names": ["ResizeMode", "VideoFullscreenUpdate"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Video.types.ts"], "sourcesContent": ["import * as React from 'react';\nimport { ImageProps, ViewProps, StyleProp, ViewStyle } from 'react-native';\n\nimport {\n  AVPlaybackNativeSource,\n  AVPlaybackSource,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n} from './AV';\n\n// @needsAudit\nexport type VideoNaturalSize = {\n  /**\n   * A number describing the width in pixels of the video data.\n   */\n  width: number;\n  /**\n   * A number describing the height in pixels of the video data.\n   */\n  height: number;\n  /**\n   * A string describing the natural orientation of the video data.\n   */\n  orientation: 'portrait' | 'landscape';\n};\n\n// @needsAudit\nexport enum ResizeMode {\n  /**\n   * Fit within component bounds while preserving aspect ratio.\n   */\n  CONTAIN = 'contain',\n  /**\n   * Fill component bounds while preserving aspect ratio.\n   */\n  COVER = 'cover',\n  /**\n   * Stretch to fill component bounds.\n   */\n  STRETCH = 'stretch',\n}\n\n// @needsAudit\nexport type VideoReadyForDisplayEvent = {\n  /**\n   * An object containing the basic data about video size.\n   */\n  naturalSize: VideoNaturalSize;\n  /**\n   * The `AVPlaybackStatus` of the video. See the [AV documentation](./av/#playback-status) for further information.\n   */\n  status?: AVPlaybackStatus;\n};\n\n// @needsAudit\nexport enum VideoFullscreenUpdate {\n  /**\n   * Describing that the fullscreen player is about to present.\n   */\n  PLAYER_WILL_PRESENT = 0,\n  /**\n   * Describing that the fullscreen player just finished presenting.\n   */\n  PLAYER_DID_PRESENT = 1,\n  /**\n   * Describing that the fullscreen player is about to dismiss.\n   */\n  PLAYER_WILL_DISMISS = 2,\n  /**\n   * Describing that the fullscreen player just finished dismissing.\n   */\n  PLAYER_DID_DISMISS = 3,\n}\n\n// @needsAudit\nexport type VideoFullscreenUpdateEvent = {\n  /**\n   * The kind of the fullscreen update.\n   */\n  fullscreenUpdate: VideoFullscreenUpdate;\n  /**\n   * The `AVPlaybackStatus` of the video. See the [AV documentation](./av) for further information.\n   */\n  status?: AVPlaybackStatus;\n};\n\n// @needsAudit\n/**\n * The Video component props can be divided into following groups:\n * - The `source` and `posterSource` props customize the source of the video content.\n * - The `useNativeControls`, `resizeMode`, and `usePoster` props customize the UI of the component.\n * - The `onPlaybackStatusUpdate`, `onReadyForDisplay`, and `onIOSFullscreenUpdate` props pass information of the state of the `Video` component.\n * - The `onLoadStart`, `onLoad`, and `onError` props are also provided for backwards compatibility with `Image`\n *   (but they are redundant with `onPlaybackStatusUpdate`).\n * Finally, the rest of props are available to control the playback of the video, but we recommend that, for finer control, you use the methods\n * available on the `ref` described in the [AV documentation](./av).\n */\nexport type VideoProps = {\n  // Source stuff\n\n  /**\n   * The source of the video data to display. If this prop is `null`, or left blank, the video component will display nothing.\n   * Note that this can also be set on the `ref` via `loadAsync()`. See the [AV documentation](./av) for further information.\n   *\n   * @see\n   * - The [Android developer documentation](https://developer.android.com/guide/topics/media/media-formats#video-formats)\n   * lists of the video formats supported on Android.\n   * - The [iOS developer documentation](https://developer.apple.com/documentation/coremedia/1564239-video_codec_constants)\n   * lists of the video formats supported on iOS.\n   */\n  source?: AVPlaybackSource;\n  /**\n   * The source of an optional image to display over the video while it is loading. The following forms are supported:\n   * - A dictionary of the form `{ uri: 'http://path/to/file' }` with a network URL pointing to an image file on the web.\n   * - `require('path/to/file')` for an image file asset in the source code directory.\n   */\n  posterSource?: ImageProps['source'];\n  /**\n   * An optional property to pass custom styles to the poster image.\n   */\n  posterStyle?: ImageProps['style'];\n  /**\n   * An optional property to pass custom styles to the internal video component.\n   */\n  videoStyle?: StyleProp<ViewStyle>;\n\n  // Callbacks\n  /**\n   * A function to be called regularly with the `AVPlaybackStatus` of the video. You will likely be using this a lot.\n   * See the [AV documentation](./av) for further information on `onPlaybackStatusUpdate`, and the interval at which it is called.\n   * @param status\n   */\n  onPlaybackStatusUpdate?: (status: AVPlaybackStatus) => void;\n  /**\n   * A function to be called when the video begins to be loaded into memory. Called without any arguments.\n   */\n  onLoadStart?: () => void;\n  /**\n   * A function to be called once the video has been loaded. The data is streamed so all of it may not have been fetched yet, just enough to render the first frame.\n   * The function is called with the `AVPlaybackStatus` of the video as its parameter. See the [AV documentation](./av) for further information.\n   * @param status\n   */\n  onLoad?: (status: AVPlaybackStatus) => void;\n  /**\n   * A function to be called if load or playback have encountered a fatal error. The function is passed a single error message string as a parameter.\n   * Errors sent here are also set on `playbackStatus.error` that are passed into the `onPlaybackStatusUpdate` callback.\n   * @param error\n   */\n  onError?: (error: string) => void;\n  /**\n   * A function to be called when the video is ready for display. Note that this function gets called whenever the video's natural size changes.\n   * @param event\n   */\n  onReadyForDisplay?: (event: VideoReadyForDisplayEvent) => void;\n  /**\n   * A function to be called when the state of the native iOS fullscreen view changes (controlled via the `presentFullscreenPlayer()`\n   * and `dismissFullscreenPlayer()` methods on the `Video`'s `ref`).\n   * @param event\n   */\n  onFullscreenUpdate?: (event: VideoFullscreenUpdateEvent) => void;\n\n  // UI stuff\n\n  /**\n   * A boolean which, if set to `true`, will display native playback controls (such as play and pause) within the `Video` component.\n   * If you'd prefer to use custom controls, you can write them yourself, and/or check out the [`VideoPlayer` component](https://github.com/ihmpavel/expo-video-player).\n   */\n  useNativeControls?: boolean;\n  /**\n   * A string describing how the video should be scaled for display in the component view bounds.\n   * Must be one of the [`ResizeMode`](#resizemode) enum values.\n   */\n  resizeMode?: ResizeMode;\n  /**\n   * A boolean which, if set to `true`, will display an image (whose source is set via the prop `posterSource`) while the video is loading.\n   */\n  usePoster?: boolean;\n  /**\n   * A react-native `Image` like component to display the poster image.\n   */\n  PosterComponent?: React.ComponentType<{\n    style: ImageProps['style'];\n    source: ImageProps['source'];\n  }>;\n\n  // Playback API\n  /**\n   * A dictionary setting a new `AVPlaybackStatusToSet` on the video.\n   * See the [AV documentation](./av#default-initial--avplaybackstatustoset) for more information on `AVPlaybackStatusToSet`.\n   */\n  status?: AVPlaybackStatusToSet;\n  /**\n   * A number describing the new minimum interval in milliseconds between calls of `onPlaybackStatusUpdate`.\n   * See the [AV documentation](./av) for more information.\n   */\n  progressUpdateIntervalMillis?: number;\n  /**\n   * The desired position of playback in milliseconds.\n   * See the [AV documentation](./av) for more information.\n   */\n  positionMillis?: number;\n  /**\n   * A boolean describing if the media is supposed to play. Playback may not start immediately after setting this value for reasons such as buffering.\n   * Make sure to update your UI based on the `isPlaying` and `isBuffering` properties of the `AVPlaybackStatus`.\n   * See the [AV documentation](./av) for more information.\n   */\n  shouldPlay?: boolean;\n  /**\n   * The desired playback rate of the media. This value must be between `0.0` and `32.0`. Only available on Android API version 23 and later and iOS.\n   * See the [AV documentation](./av) for more information.\n   */\n  rate?: number;\n  /**\n   * A boolean describing if we should correct the pitch for a changed rate. If set to `true`, the pitch of the audio will be corrected\n   * (so a rate different than `1.0` will timestretch the audio).\n   * See the [AV documentation](./av) for more information.\n   */\n  shouldCorrectPitch?: boolean;\n  /**\n   * The desired volume of the audio for this media. This value must be between `0.0` (silence) and `1.0` (maximum volume).\n   * See the [AV documentation](./av) for more information.\n   */\n  volume?: number;\n  /**\n   * A boolean describing if the audio of this media should be muted.\n   * See the [AV documentation](./av) for more information.\n   */\n  isMuted?: boolean;\n  /**\n   * The desired audio panning value of the audio for this media. This value must be between `-1.0` (full left) and `1.0` (full right).\n   * See the [AV documentation](./av) for more information.\n   */\n  audioPan?: number;\n  /**\n   * A boolean describing if the media should play once (`false`) or loop indefinitely (`true`).\n   * See the [AV documentation](./av) for more information.\n   */\n  isLooping?: boolean;\n\n  // Required by react-native\n  /**\n   * @hidden\n   */\n  scaleX?: number;\n  /**\n   * @hidden\n   */\n  scaleY?: number;\n  /**\n   * @hidden\n   */\n  translateX?: number;\n  /**\n   * @hidden\n   */\n  translateY?: number;\n  /**\n   * @hidden\n   */\n  rotation?: number;\n} & ViewProps;\n\n/**\n * @hidden\n */\nexport type VideoNativeProps = {\n  source?: AVPlaybackNativeSource | null;\n  resizeMode?: unknown;\n  status?: AVPlaybackStatusToSet;\n  onLoadStart?: () => void;\n  onLoad?: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onError?: (event: { nativeEvent: { error: string } }) => void;\n  onStatusUpdate?: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onReadyForDisplay?: (event: { nativeEvent: VideoReadyForDisplayEvent }) => void;\n  onFullscreenUpdate?: (event: { nativeEvent: VideoFullscreenUpdateEvent }) => void;\n  useNativeControls?: boolean;\n  videoStyle?: StyleProp<ViewStyle>;\n} & ViewProps;\n\n// @docsMissing\nexport type VideoState = {\n  showPoster: boolean;\n};\n\n/**\n * @hidden\n */\nexport type ExponentVideoComponent = React.ComponentClass<VideoNativeProps>;\n"], "mappings": "AA2BA,WAAYA,UAaX;AAbD,WAAYA,UAAU;EAIpBA,UAAA,uBAAmB;EAInBA,UAAA,mBAAe;EAIfA,UAAA,uBAAmB;AACrB,CAAC,EAbWA,UAAU,KAAVA,UAAU;AA4BtB,WAAYC,qBAiBX;AAjBD,WAAYA,qBAAqB;EAI/BA,qBAAA,CAAAA,qBAAA,oDAAuB;EAIvBA,qBAAA,CAAAA,qBAAA,kDAAsB;EAItBA,qBAAA,CAAAA,qBAAA,oDAAuB;EAIvBA,qBAAA,CAAAA,qBAAA,kDAAsB;AACxB,CAAC,EAjBWA,qBAAqB,KAArBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}