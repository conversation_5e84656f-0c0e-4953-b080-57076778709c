{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport function resolve(styleProp, cleanedProps) {\n  if (styleProp) {\n    return StyleSheet ? [styleProp, cleanedProps] : styleProp[Symbol.iterator] ? Object.assign.apply(Object, [{}].concat(_toConsumableArray(styleProp), [cleanedProps])) : Object.assign({}, styleProp, cleanedProps);\n  } else {\n    return cleanedProps;\n  }\n}", "map": {"version": 3, "names": ["resolve", "styleProp", "cleanedProps", "StyleSheet", "Symbol", "iterator", "Object", "assign", "apply", "concat", "_toConsumableArray"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-svg/src/lib/resolve.ts"], "sourcesContent": ["import { StyleSheet } from 'react-native';\n\n// Kept in separate file, to avoid name collision with Symbol element\nexport function resolve<T>(styleProp: Iterable<T>, cleanedProps: T) {\n  if (styleProp) {\n    return StyleSheet\n      ? [styleProp, cleanedProps]\n      : // Compatibility for arrays of styles in plain react web\n      styleProp[Symbol.iterator]\n      ? Object.assign({}, ...styleProp, cleanedProps)\n      : Object.assign({}, styleProp, cleanedProps);\n  } else {\n    return cleanedProps;\n  }\n}\n"], "mappings": ";;AAGA,OAAO,SAASA,OAAOA,CAAIC,SAAsB,EAAEC,YAAe,EAAE;EAClE,IAAID,SAAS,EAAE;IACb,OAAOE,UAAU,GACb,CAACF,SAAS,EAAEC,YAAY,CAAC,GAE3BD,SAAS,CAACG,MAAM,CAACC,QAAQ,CAAC,GACxBC,MAAM,CAACC,MAAM,CAAAC,KAAA,CAAbF,MAAM,GAAQ,CAAC,CAAC,EAAAG,MAAA,CAAAC,kBAAA,CAAKT,SAAS,IAAEC,YAAY,GAAC,GAC7CI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,SAAS,EAAEC,YAAY,CAAC;EAChD,CAAC,MAAM;IACL,OAAOA,YAAY;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}