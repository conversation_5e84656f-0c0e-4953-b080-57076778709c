{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { StatusBar } from 'expo-status-bar';\nimport Agent from \"./screens/Agent\";\nimport Inbox from \"./screens/Inbox\";\nimport TabBar from \"./components/TabBar\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function App() {\n  var _useState = useState('agent'),\n    _useState2 = _slicedToArray(_useState, 2),\n    activeTab = _useState2[0],\n    setActiveTab = _useState2[1];\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(StatusBar, {\n      style: \"auto\"\n    }), activeTab === 'agent' ? _jsx(Agent, {}) : _jsx(Inbox, {}), _jsx(TabBar, {\n      activeTab: activeTab,\n      onTabChange: setActiveTab\n    })]\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff'\n  }\n});", "map": {"version": 3, "names": ["React", "useState", "View", "StyleSheet", "StatusBar", "Agent", "Inbox", "TabBar", "jsx", "_jsx", "jsxs", "_jsxs", "App", "_useState", "_useState2", "_slicedToArray", "activeTab", "setActiveTab", "style", "styles", "container", "children", "onTabChange", "create", "flex", "backgroundColor"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/App.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { View, StyleSheet } from 'react-native';\nimport { StatusBar } from 'expo-status-bar';\nimport Agent from './screens/Agent';\nimport Inbox from './screens/Inbox';\nimport TabBar from './components/TabBar';\n\nexport default function App() {\n  const [activeTab, setActiveTab] = useState('agent');\n\n  return (\n    <View style={styles.container}>\n      <StatusBar style=\"auto\" />\n      \n      {activeTab === 'agent' ? (\n        <Agent />\n      ) : (\n        <Inbox />\n      )}\n      \n      <TabBar activeTab={activeTab} onTabChange={setActiveTab} />\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n});\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAExC,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,KAAK;AACZ,OAAOC,KAAK;AACZ,OAAOC,MAAM;AAA4B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B,IAAAC,SAAA,GAAkCZ,QAAQ,CAAC,OAAO,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA5CG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAE9B,OACEH,KAAA,CAACT,IAAI;IAACgB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BZ,IAAA,CAACL,SAAS;MAACc,KAAK,EAAC;IAAM,CAAE,CAAC,EAEzBF,SAAS,KAAK,OAAO,GACpBP,IAAA,CAACJ,KAAK,IAAE,CAAC,GAETI,IAAA,CAACH,KAAK,IAAE,CACT,EAEDG,IAAA,CAACF,MAAM;MAACS,SAAS,EAAEA,SAAU;MAACM,WAAW,EAAEL;IAAa,CAAE,CAAC;EAAA,CACvD,CAAC;AAEX;AAEA,IAAME,MAAM,GAAGhB,UAAU,CAACoB,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}