{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nvar _excluded = [\"uri\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { PermissionStatus, SyntheticPlatformEmitter } from 'expo-modules-core';\nimport { RecordingOptionsPresets } from \"./Audio/RecordingConstants\";\nfunction getPermissionWithQueryAsync(_x) {\n  return _getPermissionWithQueryAsync.apply(this, arguments);\n}\nfunction _getPermissionWithQueryAsync() {\n  _getPermissionWithQueryAsync = _asyncToGenerator(function* (name) {\n    if (!navigator || !navigator.permissions || !navigator.permissions.query) return null;\n    try {\n      var _yield$navigator$perm = yield navigator.permissions.query({\n          name: name\n        }),\n        state = _yield$navigator$perm.state;\n      switch (state) {\n        case 'granted':\n          return PermissionStatus.GRANTED;\n        case 'denied':\n          return PermissionStatus.DENIED;\n        default:\n          return PermissionStatus.UNDETERMINED;\n      }\n    } catch (_unused2) {\n      return PermissionStatus.UNDETERMINED;\n    }\n  });\n  return _getPermissionWithQueryAsync.apply(this, arguments);\n}\nfunction getUserMedia(constraints) {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n  var getUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || function () {\n    var error = new Error('Permission unimplemented');\n    error.code = 0;\n    error.name = 'NotAllowedError';\n    throw error;\n  };\n  return new Promise(function (resolve, reject) {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\nfunction getStatusFromMedia(media) {\n  if (!media) {\n    return {\n      isLoaded: false,\n      error: undefined\n    };\n  }\n  var isPlaying = !!(media.currentTime > 0 && !media.paused && !media.ended && media.readyState > 2);\n  var status = {\n    isLoaded: true,\n    uri: media.src,\n    progressUpdateIntervalMillis: 100,\n    durationMillis: media.duration * 1000,\n    positionMillis: media.currentTime * 1000,\n    shouldPlay: media.autoplay,\n    isPlaying: isPlaying,\n    isBuffering: false,\n    rate: media.playbackRate,\n    shouldCorrectPitch: false,\n    volume: media.volume,\n    audioPan: 0,\n    isMuted: media.muted,\n    isLooping: media.loop,\n    didJustFinish: media.ended\n  };\n  return status;\n}\nfunction setStatusForMedia(_x2, _x3) {\n  return _setStatusForMedia.apply(this, arguments);\n}\nfunction _setStatusForMedia() {\n  _setStatusForMedia = _asyncToGenerator(function* (media, status) {\n    if (status.positionMillis !== undefined) {\n      media.currentTime = status.positionMillis / 1000;\n    }\n    if (status.shouldPlay !== undefined) {\n      if (status.shouldPlay) {\n        yield media.play();\n      } else {\n        yield media.pause();\n      }\n    }\n    if (status.rate !== undefined) {\n      media.playbackRate = status.rate;\n    }\n    if (status.volume !== undefined) {\n      media.volume = status.volume;\n    }\n    if (status.isMuted !== undefined) {\n      media.muted = status.isMuted;\n    }\n    if (status.isLooping !== undefined) {\n      media.loop = status.isLooping;\n    }\n    return getStatusFromMedia(media);\n  });\n  return _setStatusForMedia.apply(this, arguments);\n}\nvar mediaRecorder = null;\nvar mediaRecorderUptimeOfLastStartResume = 0;\nvar mediaRecorderDurationAlreadyRecorded = 0;\nvar mediaRecorderIsRecording = false;\nfunction getAudioRecorderDurationMillis() {\n  var duration = mediaRecorderDurationAlreadyRecorded;\n  if (mediaRecorderIsRecording && mediaRecorderUptimeOfLastStartResume > 0) {\n    duration += Date.now() - mediaRecorderUptimeOfLastStartResume;\n  }\n  return duration;\n}\nexport default {\n  get name() {\n    return 'ExponentAV';\n  },\n  getStatusForVideo: function () {\n    var _getStatusForVideo = _asyncToGenerator(function* (element) {\n      return getStatusFromMedia(element);\n    });\n    function getStatusForVideo(_x4) {\n      return _getStatusForVideo.apply(this, arguments);\n    }\n    return getStatusForVideo;\n  }(),\n  loadForVideo: function () {\n    var _loadForVideo = _asyncToGenerator(function* (element, nativeSource, fullInitialStatus) {\n      return getStatusFromMedia(element);\n    });\n    function loadForVideo(_x5, _x6, _x7) {\n      return _loadForVideo.apply(this, arguments);\n    }\n    return loadForVideo;\n  }(),\n  unloadForVideo: function () {\n    var _unloadForVideo = _asyncToGenerator(function* (element) {\n      return getStatusFromMedia(element);\n    });\n    function unloadForVideo(_x8) {\n      return _unloadForVideo.apply(this, arguments);\n    }\n    return unloadForVideo;\n  }(),\n  setStatusForVideo: function () {\n    var _setStatusForVideo = _asyncToGenerator(function* (element, status) {\n      return setStatusForMedia(element, status);\n    });\n    function setStatusForVideo(_x9, _x0) {\n      return _setStatusForVideo.apply(this, arguments);\n    }\n    return setStatusForVideo;\n  }(),\n  replayVideo: function () {\n    var _replayVideo = _asyncToGenerator(function* (element, status) {\n      return setStatusForMedia(element, status);\n    });\n    function replayVideo(_x1, _x10) {\n      return _replayVideo.apply(this, arguments);\n    }\n    return replayVideo;\n  }(),\n  setAudioMode: function () {\n    var _setAudioMode = _asyncToGenerator(function* () {});\n    function setAudioMode() {\n      return _setAudioMode.apply(this, arguments);\n    }\n    return setAudioMode;\n  }(),\n  setAudioIsEnabled: function () {\n    var _setAudioIsEnabled = _asyncToGenerator(function* () {});\n    function setAudioIsEnabled() {\n      return _setAudioIsEnabled.apply(this, arguments);\n    }\n    return setAudioIsEnabled;\n  }(),\n  getStatusForSound: function () {\n    var _getStatusForSound = _asyncToGenerator(function* (element) {\n      return getStatusFromMedia(element);\n    });\n    function getStatusForSound(_x11) {\n      return _getStatusForSound.apply(this, arguments);\n    }\n    return getStatusForSound;\n  }(),\n  loadForSound: function () {\n    var _loadForSound = _asyncToGenerator(function* (nativeSource, fullInitialStatus) {\n      var source = typeof nativeSource === 'string' ? nativeSource : nativeSource.uri;\n      var media = new Audio(source);\n      media.ontimeupdate = function () {\n        SyntheticPlatformEmitter.emit('didUpdatePlaybackStatus', {\n          key: media,\n          status: getStatusFromMedia(media)\n        });\n      };\n      media.onerror = function () {\n        SyntheticPlatformEmitter.emit('ExponentAV.onError', {\n          key: media,\n          error: media.error.message\n        });\n      };\n      var status = yield setStatusForMedia(media, fullInitialStatus);\n      return [media, status];\n    });\n    function loadForSound(_x12, _x13) {\n      return _loadForSound.apply(this, arguments);\n    }\n    return loadForSound;\n  }(),\n  unloadForSound: function () {\n    var _unloadForSound = _asyncToGenerator(function* (element) {\n      element.pause();\n      element.removeAttribute('src');\n      element.load();\n      return getStatusFromMedia(element);\n    });\n    function unloadForSound(_x14) {\n      return _unloadForSound.apply(this, arguments);\n    }\n    return unloadForSound;\n  }(),\n  setStatusForSound: function () {\n    var _setStatusForSound = _asyncToGenerator(function* (element, status) {\n      return setStatusForMedia(element, status);\n    });\n    function setStatusForSound(_x15, _x16) {\n      return _setStatusForSound.apply(this, arguments);\n    }\n    return setStatusForSound;\n  }(),\n  replaySound: function () {\n    var _replaySound = _asyncToGenerator(function* (element, status) {\n      return setStatusForMedia(element, status);\n    });\n    function replaySound(_x17, _x18) {\n      return _replaySound.apply(this, arguments);\n    }\n    return replaySound;\n  }(),\n  getAudioRecordingStatus: function () {\n    var _getAudioRecordingStatus = _asyncToGenerator(function* () {\n      var _mediaRecorder, _mediaRecorder2, _mediaRecorder3;\n      return {\n        canRecord: ((_mediaRecorder = mediaRecorder) == null ? void 0 : _mediaRecorder.state) === 'recording' || ((_mediaRecorder2 = mediaRecorder) == null ? void 0 : _mediaRecorder2.state) === 'inactive',\n        isRecording: ((_mediaRecorder3 = mediaRecorder) == null ? void 0 : _mediaRecorder3.state) === 'recording',\n        isDoneRecording: false,\n        durationMillis: getAudioRecorderDurationMillis(),\n        uri: null\n      };\n    });\n    function getAudioRecordingStatus() {\n      return _getAudioRecordingStatus.apply(this, arguments);\n    }\n    return getAudioRecordingStatus;\n  }(),\n  prepareAudioRecorder: function () {\n    var _prepareAudioRecorder = _asyncToGenerator(function* (options) {\n      if (typeof navigator !== 'undefined' && !navigator.mediaDevices) {\n        throw new Error('No media devices available');\n      }\n      mediaRecorderUptimeOfLastStartResume = 0;\n      mediaRecorderDurationAlreadyRecorded = 0;\n      var stream = yield getUserMedia({\n        audio: true\n      });\n      mediaRecorder = new window.MediaRecorder(stream, (options == null ? void 0 : options.web) || RecordingOptionsPresets.HIGH_QUALITY.web);\n      mediaRecorder.addEventListener('pause', function () {\n        mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n        mediaRecorderIsRecording = false;\n      });\n      mediaRecorder.addEventListener('resume', function () {\n        mediaRecorderUptimeOfLastStartResume = Date.now();\n        mediaRecorderIsRecording = true;\n      });\n      mediaRecorder.addEventListener('start', function () {\n        mediaRecorderUptimeOfLastStartResume = Date.now();\n        mediaRecorderDurationAlreadyRecorded = 0;\n        mediaRecorderIsRecording = true;\n      });\n      mediaRecorder.addEventListener('stop', function () {\n        mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n        mediaRecorderIsRecording = false;\n        stream.getTracks().forEach(function (track) {\n          return track.stop();\n        });\n      });\n      var _yield$this$getAudioR = yield this.getAudioRecordingStatus(),\n        uri = _yield$this$getAudioR.uri,\n        status = _objectWithoutProperties(_yield$this$getAudioR, _excluded);\n      return {\n        uri: null,\n        status: status\n      };\n    });\n    function prepareAudioRecorder(_x19) {\n      return _prepareAudioRecorder.apply(this, arguments);\n    }\n    return prepareAudioRecorder;\n  }(),\n  startAudioRecording: function () {\n    var _startAudioRecording = _asyncToGenerator(function* () {\n      if (mediaRecorder === null) {\n        throw new Error('Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.');\n      }\n      if (mediaRecorder.state === 'paused') {\n        mediaRecorder.resume();\n      } else {\n        mediaRecorder.start();\n      }\n      return this.getAudioRecordingStatus();\n    });\n    function startAudioRecording() {\n      return _startAudioRecording.apply(this, arguments);\n    }\n    return startAudioRecording;\n  }(),\n  pauseAudioRecording: function () {\n    var _pauseAudioRecording = _asyncToGenerator(function* () {\n      if (mediaRecorder === null) {\n        throw new Error('Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.');\n      }\n      mediaRecorder.pause();\n      return this.getAudioRecordingStatus();\n    });\n    function pauseAudioRecording() {\n      return _pauseAudioRecording.apply(this, arguments);\n    }\n    return pauseAudioRecording;\n  }(),\n  stopAudioRecording: function () {\n    var _stopAudioRecording = _asyncToGenerator(function* () {\n      if (mediaRecorder === null) {\n        throw new Error('Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.');\n      }\n      if (mediaRecorder.state === 'inactive') {\n        return this.getAudioRecordingStatus();\n      }\n      var dataPromise = new Promise(function (resolve) {\n        return mediaRecorder.addEventListener('dataavailable', function (e) {\n          return resolve(e.data);\n        });\n      });\n      mediaRecorder.stop();\n      var data = yield dataPromise;\n      var url = URL.createObjectURL(data);\n      return _objectSpread(_objectSpread({}, yield this.getAudioRecordingStatus()), {}, {\n        uri: url\n      });\n    });\n    function stopAudioRecording() {\n      return _stopAudioRecording.apply(this, arguments);\n    }\n    return stopAudioRecording;\n  }(),\n  unloadAudioRecorder: function () {\n    var _unloadAudioRecorder = _asyncToGenerator(function* () {\n      mediaRecorder = null;\n    });\n    function unloadAudioRecorder() {\n      return _unloadAudioRecorder.apply(this, arguments);\n    }\n    return unloadAudioRecorder;\n  }(),\n  getPermissionsAsync: function () {\n    var _getPermissionsAsync = _asyncToGenerator(function* () {\n      var maybeStatus = yield getPermissionWithQueryAsync('microphone');\n      switch (maybeStatus) {\n        case PermissionStatus.GRANTED:\n          return {\n            status: PermissionStatus.GRANTED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: true\n          };\n        case PermissionStatus.DENIED:\n          return {\n            status: PermissionStatus.DENIED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: false\n          };\n        default:\n          return yield this.requestPermissionsAsync();\n      }\n    });\n    function getPermissionsAsync() {\n      return _getPermissionsAsync.apply(this, arguments);\n    }\n    return getPermissionsAsync;\n  }(),\n  requestPermissionsAsync: function () {\n    var _requestPermissionsAsync = _asyncToGenerator(function* () {\n      try {\n        var stream = yield getUserMedia({\n          audio: true\n        });\n        stream.getTracks().forEach(function (track) {\n          return track.stop();\n        });\n        return {\n          status: PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true\n        };\n      } catch (_unused) {\n        return {\n          status: PermissionStatus.DENIED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false\n        };\n      }\n    });\n    function requestPermissionsAsync() {\n      return _requestPermissionsAsync.apply(this, arguments);\n    }\n    return requestPermissionsAsync;\n  }()\n};", "map": {"version": 3, "names": ["PermissionStatus", "SyntheticPlatformEmitter", "RecordingOptionsPresets", "getPermissionWithQueryAsync", "_x", "_getPermissionWithQueryAsync", "apply", "arguments", "_asyncToGenerator", "name", "navigator", "permissions", "query", "_yield$navigator$perm", "state", "GRANTED", "DENIED", "UNDETERMINED", "_unused2", "getUserMedia", "constraints", "mediaDevices", "webkitGetUserMedia", "mozGetUserMedia", "error", "Error", "code", "Promise", "resolve", "reject", "call", "getStatusFromMedia", "media", "isLoaded", "undefined", "isPlaying", "currentTime", "paused", "ended", "readyState", "status", "uri", "src", "progressUpdateIntervalMillis", "<PERSON><PERSON><PERSON><PERSON>", "duration", "<PERSON><PERSON><PERSON><PERSON>", "shouldPlay", "autoplay", "isBuffering", "rate", "playbackRate", "shouldCorrectPitch", "volume", "audioPan", "isMuted", "muted", "isLooping", "loop", "did<PERSON>ust<PERSON><PERSON>sh", "setStatusForMedia", "_x2", "_x3", "_setStatusForMedia", "play", "pause", "mediaRecorder", "mediaRecorderUptimeOfLastStartResume", "mediaRecorderDurationAlreadyRecorded", "mediaRecorderIsRecording", "getAudioRecorderDurationMillis", "Date", "now", "getStatusForVideo", "_getStatusForVideo", "element", "_x4", "loadForVideo", "_loadForVideo", "nativeSource", "fullInitialStatus", "_x5", "_x6", "_x7", "unloadForVideo", "_unloadForVideo", "_x8", "setStatusForVideo", "_setStatusForVideo", "_x9", "_x0", "replayVideo", "_replayVideo", "_x1", "_x10", "setAudioMode", "_setAudioMode", "setAudioIsEnabled", "_setAudioIsEnabled", "getStatusForSound", "_getStatusForSound", "_x11", "loadForSound", "_loadForSound", "source", "Audio", "ontimeupdate", "emit", "key", "onerror", "message", "_x12", "_x13", "unloadForSound", "_unloadForSound", "removeAttribute", "load", "_x14", "setStatusForSound", "_setStatusForSound", "_x15", "_x16", "replaySound", "_replaySound", "_x17", "_x18", "getAudioRecordingStatus", "_getAudioRecordingStatus", "_mediaRecorder", "_mediaRecorder2", "_mediaRecorder3", "canRecord", "isRecording", "isDoneRecording", "prepareAudioRecorder", "_prepareAudioRecorder", "options", "stream", "audio", "window", "MediaRecorder", "web", "HIGH_QUALITY", "addEventListener", "getTracks", "for<PERSON>ach", "track", "stop", "_yield$this$getAudioR", "_objectWithoutProperties", "_excluded", "_x19", "startAudioRecording", "_startAudioRecording", "resume", "start", "pauseAudioRecording", "_pauseAudioRecording", "stopAudioRecording", "_stopAudioRecording", "dataPromise", "e", "data", "url", "URL", "createObjectURL", "_objectSpread", "unloadAudioRecorder", "_unloadAudioRecorder", "getPermissionsAsync", "_getPermissionsAsync", "<PERSON><PERSON><PERSON><PERSON>", "expires", "canAskAgain", "granted", "requestPermissionsAsync", "_requestPermissionsAsync", "_unused"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/ExponentAV.web.ts"], "sourcesContent": ["import { PermissionResponse, PermissionStatus, SyntheticPlatformEmitter } from 'expo-modules-core';\n\nimport type { AVPlaybackNativeSource, AVPlaybackStatus, AVPlaybackStatusToSet } from './AV.types';\nimport type { RecordingStatus } from './Audio/Recording.types';\nimport { RecordingOptionsPresets } from './Audio/RecordingConstants';\n\nasync function getPermissionWithQueryAsync(\n  name: PermissionNameWithAdditionalValues\n): Promise<PermissionStatus | null> {\n  if (!navigator || !navigator.permissions || !navigator.permissions.query) return null;\n\n  try {\n    const { state } = await navigator.permissions.query({ name });\n    switch (state) {\n      case 'granted':\n        return PermissionStatus.GRANTED;\n      case 'denied':\n        return PermissionStatus.DENIED;\n      default:\n        return PermissionStatus.UNDETERMINED;\n    }\n  } catch {\n    // Firefox - TypeError: 'microphone' (value of 'name' member of PermissionDescriptor) is not a valid value for enumeration PermissionName.\n    return PermissionStatus.UNDETERMINED;\n  }\n}\n\nfunction getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {\n  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n    return navigator.mediaDevices.getUserMedia(constraints);\n  }\n\n  // Some browsers partially implement mediaDevices. We can't just assign an object\n  // with getUserMedia as it would overwrite existing properties.\n  // Here, we will just add the getUserMedia property if it's missing.\n\n  // First get ahold of the legacy getUserMedia, if present\n  const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia ||\n    navigator.webkitGetUserMedia ||\n    navigator.mozGetUserMedia ||\n    function () {\n      const error: any = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n\n  return new Promise((resolve, reject) => {\n    getUserMedia.call(navigator, constraints, resolve, reject);\n  });\n}\n\nfunction getStatusFromMedia(media?: HTMLMediaElement): AVPlaybackStatus {\n  if (!media) {\n    return {\n      isLoaded: false,\n      error: undefined,\n    };\n  }\n\n  const isPlaying = !!(\n    media.currentTime > 0 &&\n    !media.paused &&\n    !media.ended &&\n    media.readyState > 2\n  );\n\n  const status: AVPlaybackStatus = {\n    isLoaded: true,\n    uri: media.src,\n    progressUpdateIntervalMillis: 100, //TODO: Bacon: Add interval between calls\n    durationMillis: media.duration * 1000,\n    positionMillis: media.currentTime * 1000,\n    // playableDurationMillis: media.buffered * 1000,\n    // seekMillisToleranceBefore?: number\n    // seekMillisToleranceAfter?: number\n    shouldPlay: media.autoplay,\n    isPlaying,\n    isBuffering: false, //media.waiting,\n    rate: media.playbackRate,\n    // TODO: Bacon: This seems too complicated right now: https://webaudio.github.io/web-audio-api/#dom-biquadfilternode-frequency\n    shouldCorrectPitch: false,\n    volume: media.volume,\n    audioPan: 0,\n    isMuted: media.muted,\n    isLooping: media.loop,\n    didJustFinish: media.ended,\n  };\n\n  return status;\n}\n\nasync function setStatusForMedia(\n  media: HTMLMediaElement,\n  status: AVPlaybackStatusToSet\n): Promise<AVPlaybackStatus> {\n  if (status.positionMillis !== undefined) {\n    media.currentTime = status.positionMillis / 1000;\n  }\n  // if (status.progressUpdateIntervalMillis !== undefined) {\n  //   media.progressUpdateIntervalMillis = status.progressUpdateIntervalMillis;\n  // }\n  // if (status.seekMillisToleranceBefore !== undefined) {\n  //   media.seekMillisToleranceBefore = status.seekMillisToleranceBefore;\n  // }\n  // if (status.seekMillisToleranceAfter !== undefined) {\n  //   media.seekMillisToleranceAfter = status.seekMillisToleranceAfter;\n  // }\n  // if (status.shouldCorrectPitch !== undefined) {\n  //   media.shouldCorrectPitch = status.shouldCorrectPitch;\n  // }\n  if (status.shouldPlay !== undefined) {\n    if (status.shouldPlay) {\n      await media.play();\n    } else {\n      await media.pause();\n    }\n  }\n  if (status.rate !== undefined) {\n    media.playbackRate = status.rate;\n  }\n  if (status.volume !== undefined) {\n    media.volume = status.volume;\n  }\n  if (status.isMuted !== undefined) {\n    media.muted = status.isMuted;\n  }\n  if (status.isLooping !== undefined) {\n    media.loop = status.isLooping;\n  }\n\n  return getStatusFromMedia(media);\n}\n\nlet mediaRecorder: null | any /*MediaRecorder*/ = null;\nlet mediaRecorderUptimeOfLastStartResume: number = 0;\nlet mediaRecorderDurationAlreadyRecorded: number = 0;\nlet mediaRecorderIsRecording: boolean = false;\n\nfunction getAudioRecorderDurationMillis() {\n  let duration = mediaRecorderDurationAlreadyRecorded;\n  if (mediaRecorderIsRecording && mediaRecorderUptimeOfLastStartResume > 0) {\n    duration += Date.now() - mediaRecorderUptimeOfLastStartResume;\n  }\n  return duration;\n}\n\nexport default {\n  get name(): string {\n    return 'ExponentAV';\n  },\n  async getStatusForVideo(element: HTMLMediaElement): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async loadForVideo(\n    element: HTMLMediaElement,\n    nativeSource: AVPlaybackNativeSource,\n    fullInitialStatus: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async unloadForVideo(element: HTMLMediaElement): Promise<AVPlaybackStatus> {\n    return getStatusFromMedia(element);\n  },\n  async setStatusForVideo(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  async replayVideo(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  /* Audio */\n  async setAudioMode() {},\n  async setAudioIsEnabled() {},\n  async getStatusForSound(element: HTMLMediaElement) {\n    return getStatusFromMedia(element);\n  },\n  async loadForSound(\n    nativeSource: string | { uri: string; [key: string]: any },\n    fullInitialStatus: AVPlaybackStatusToSet\n  ): Promise<[HTMLMediaElement, AVPlaybackStatus]> {\n    const source = typeof nativeSource === 'string' ? nativeSource : nativeSource.uri;\n    const media = new Audio(source);\n\n    media.ontimeupdate = () => {\n      SyntheticPlatformEmitter.emit('didUpdatePlaybackStatus', {\n        key: media,\n        status: getStatusFromMedia(media),\n      });\n    };\n\n    media.onerror = () => {\n      SyntheticPlatformEmitter.emit('ExponentAV.onError', {\n        key: media,\n        error: media.error!.message,\n      });\n    };\n\n    const status = await setStatusForMedia(media, fullInitialStatus);\n\n    return [media, status];\n  },\n  async unloadForSound(element: HTMLMediaElement) {\n    element.pause();\n    element.removeAttribute('src');\n    element.load();\n    return getStatusFromMedia(element);\n  },\n  async setStatusForSound(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n  async replaySound(\n    element: HTMLMediaElement,\n    status: AVPlaybackStatusToSet\n  ): Promise<AVPlaybackStatus> {\n    return setStatusForMedia(element, status);\n  },\n\n  /* Recording */\n  //   async setUnloadedCallbackForAndroidRecording() {},\n  async getAudioRecordingStatus(): Promise<RecordingStatus> {\n    return {\n      canRecord: mediaRecorder?.state === 'recording' || mediaRecorder?.state === 'inactive',\n      isRecording: mediaRecorder?.state === 'recording',\n      isDoneRecording: false,\n      durationMillis: getAudioRecorderDurationMillis(),\n      uri: null,\n    };\n  },\n  async prepareAudioRecorder(options): Promise<{\n    uri: string | null;\n    // status is of type RecordingStatus, but without the canRecord field populated\n    status: Pick<RecordingStatus, Exclude<keyof RecordingStatus, 'canRecord'>>;\n  }> {\n    if (typeof navigator !== 'undefined' && !navigator.mediaDevices) {\n      throw new Error('No media devices available');\n    }\n\n    mediaRecorderUptimeOfLastStartResume = 0;\n    mediaRecorderDurationAlreadyRecorded = 0;\n\n    const stream = await getUserMedia({ audio: true });\n\n    mediaRecorder = new (window as any).MediaRecorder(\n      stream,\n      options?.web || RecordingOptionsPresets.HIGH_QUALITY.web\n    );\n\n    mediaRecorder.addEventListener('pause', () => {\n      mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n      mediaRecorderIsRecording = false;\n    });\n\n    mediaRecorder.addEventListener('resume', () => {\n      mediaRecorderUptimeOfLastStartResume = Date.now();\n      mediaRecorderIsRecording = true;\n    });\n\n    mediaRecorder.addEventListener('start', () => {\n      mediaRecorderUptimeOfLastStartResume = Date.now();\n      mediaRecorderDurationAlreadyRecorded = 0;\n      mediaRecorderIsRecording = true;\n    });\n\n    mediaRecorder.addEventListener('stop', () => {\n      mediaRecorderDurationAlreadyRecorded = getAudioRecorderDurationMillis();\n      mediaRecorderIsRecording = false;\n\n      // Clears recording icon in Chrome tab\n      stream.getTracks().forEach((track) => track.stop());\n    });\n\n    const { uri, ...status } = await this.getAudioRecordingStatus();\n\n    return { uri: null, status };\n  },\n  async startAudioRecording(): Promise<RecordingStatus> {\n    if (mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    if (mediaRecorder.state === 'paused') {\n      mediaRecorder.resume();\n    } else {\n      mediaRecorder.start();\n    }\n\n    return this.getAudioRecordingStatus();\n  },\n  async pauseAudioRecording(): Promise<RecordingStatus> {\n    if (mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    // Set status to paused\n    mediaRecorder.pause();\n\n    return this.getAudioRecordingStatus();\n  },\n  async stopAudioRecording(): Promise<RecordingStatus> {\n    if (mediaRecorder === null) {\n      throw new Error(\n        'Cannot start an audio recording without initializing a MediaRecorder. Run prepareToRecordAsync() before attempting to start an audio recording.'\n      );\n    }\n\n    if (mediaRecorder.state === 'inactive') {\n      return this.getAudioRecordingStatus();\n    }\n\n    const dataPromise = new Promise<Blob>((resolve) =>\n      mediaRecorder.addEventListener('dataavailable', (e) => resolve(e.data))\n    );\n\n    mediaRecorder.stop();\n\n    const data = await dataPromise;\n    const url = URL.createObjectURL(data);\n\n    return {\n      ...(await this.getAudioRecordingStatus()),\n      uri: url,\n    };\n  },\n  async unloadAudioRecorder(): Promise<void> {\n    mediaRecorder = null;\n  },\n\n  async getPermissionsAsync(): Promise<PermissionResponse> {\n    const maybeStatus = await getPermissionWithQueryAsync('microphone');\n    switch (maybeStatus) {\n      case PermissionStatus.GRANTED:\n        return {\n          status: PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true,\n        };\n      case PermissionStatus.DENIED:\n        return {\n          status: PermissionStatus.DENIED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false,\n        };\n      default:\n        return await this.requestPermissionsAsync();\n    }\n  },\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    try {\n      const stream = await getUserMedia({ audio: true });\n      stream.getTracks().forEach((track) => track.stop());\n      return {\n        status: PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true,\n      };\n    } catch {\n      return {\n        status: PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false,\n      };\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA,SAA6BA,gBAAgB,EAAEC,wBAAwB,QAAQ,mBAAmB;AAIlG,SAASC,uBAAuB;AAAqC,SAEtDC,2BAA2BA,CAAAC,EAAA;EAAA,OAAAC,4BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,6BAAA;EAAAA,4BAAA,GAAAG,iBAAA,CAA1C,WACEC,IAAwC;IAExC,IAAI,CAACC,SAAS,IAAI,CAACA,SAAS,CAACC,WAAW,IAAI,CAACD,SAAS,CAACC,WAAW,CAACC,KAAK,EAAE,OAAO,IAAI;IAErF,IAAI;MACF,IAAAC,qBAAA,SAAwBH,SAAS,CAACC,WAAW,CAACC,KAAK,CAAC;UAAEH,IAAI,EAAJA;QAAI,CAAE,CAAC;QAArDK,KAAK,GAAAD,qBAAA,CAALC,KAAK;MACb,QAAQA,KAAK;QACX,KAAK,SAAS;UACZ,OAAOd,gBAAgB,CAACe,OAAO;QACjC,KAAK,QAAQ;UACX,OAAOf,gBAAgB,CAACgB,MAAM;QAChC;UACE,OAAOhB,gBAAgB,CAACiB,YAAY;;KAEzC,CAAC,OAAAC,QAAA,EAAM;MAEN,OAAOlB,gBAAgB,CAACiB,YAAY;;EAExC,CAAC;EAAA,OAAAZ,4BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,SAASY,YAAYA,CAACC,WAAmC;EACvD,IAAIV,SAAS,CAACW,YAAY,IAAIX,SAAS,CAACW,YAAY,CAACF,YAAY,EAAE;IACjE,OAAOT,SAAS,CAACW,YAAY,CAACF,YAAY,CAACC,WAAW,CAAC;;EAQzD,IAAMD,YAAY,GAEhBT,SAAS,CAACS,YAAY,IACtBT,SAAS,CAACY,kBAAkB,IAC5BZ,SAAS,CAACa,eAAe,IACzB;IACE,IAAMC,KAAK,GAAQ,IAAIC,KAAK,CAAC,0BAA0B,CAAC;IACxDD,KAAK,CAACE,IAAI,GAAG,CAAC;IACdF,KAAK,CAACf,IAAI,GAAG,iBAAiB;IAC9B,MAAMe,KAAK;EACb,CAAC;EAEH,OAAO,IAAIG,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAI;IACrCV,YAAY,CAACW,IAAI,CAACpB,SAAS,EAAEU,WAAW,EAAEQ,OAAO,EAAEC,MAAM,CAAC;EAC5D,CAAC,CAAC;AACJ;AAEA,SAASE,kBAAkBA,CAACC,KAAwB;EAClD,IAAI,CAACA,KAAK,EAAE;IACV,OAAO;MACLC,QAAQ,EAAE,KAAK;MACfT,KAAK,EAAEU;KACR;;EAGH,IAAMC,SAAS,GAAG,CAAC,EACjBH,KAAK,CAACI,WAAW,GAAG,CAAC,IACrB,CAACJ,KAAK,CAACK,MAAM,IACb,CAACL,KAAK,CAACM,KAAK,IACZN,KAAK,CAACO,UAAU,GAAG,CAAC,CACrB;EAED,IAAMC,MAAM,GAAqB;IAC/BP,QAAQ,EAAE,IAAI;IACdQ,GAAG,EAAET,KAAK,CAACU,GAAG;IACdC,4BAA4B,EAAE,GAAG;IACjCC,cAAc,EAAEZ,KAAK,CAACa,QAAQ,GAAG,IAAI;IACrCC,cAAc,EAAEd,KAAK,CAACI,WAAW,GAAG,IAAI;IAIxCW,UAAU,EAAEf,KAAK,CAACgB,QAAQ;IAC1Bb,SAAS,EAATA,SAAS;IACTc,WAAW,EAAE,KAAK;IAClBC,IAAI,EAAElB,KAAK,CAACmB,YAAY;IAExBC,kBAAkB,EAAE,KAAK;IACzBC,MAAM,EAAErB,KAAK,CAACqB,MAAM;IACpBC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAEvB,KAAK,CAACwB,KAAK;IACpBC,SAAS,EAAEzB,KAAK,CAAC0B,IAAI;IACrBC,aAAa,EAAE3B,KAAK,CAACM;GACtB;EAED,OAAOE,MAAM;AACf;AAAC,SAEcoB,iBAAiBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,kBAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwD,mBAAA;EAAAA,kBAAA,GAAAvD,iBAAA,CAAhC,WACEwB,KAAuB,EACvBQ,MAA6B;IAE7B,IAAIA,MAAM,CAACM,cAAc,KAAKZ,SAAS,EAAE;MACvCF,KAAK,CAACI,WAAW,GAAGI,MAAM,CAACM,cAAc,GAAG,IAAI;;IAclD,IAAIN,MAAM,CAACO,UAAU,KAAKb,SAAS,EAAE;MACnC,IAAIM,MAAM,CAACO,UAAU,EAAE;QACrB,MAAMf,KAAK,CAACgC,IAAI,EAAE;OACnB,MAAM;QACL,MAAMhC,KAAK,CAACiC,KAAK,EAAE;;;IAGvB,IAAIzB,MAAM,CAACU,IAAI,KAAKhB,SAAS,EAAE;MAC7BF,KAAK,CAACmB,YAAY,GAAGX,MAAM,CAACU,IAAI;;IAElC,IAAIV,MAAM,CAACa,MAAM,KAAKnB,SAAS,EAAE;MAC/BF,KAAK,CAACqB,MAAM,GAAGb,MAAM,CAACa,MAAM;;IAE9B,IAAIb,MAAM,CAACe,OAAO,KAAKrB,SAAS,EAAE;MAChCF,KAAK,CAACwB,KAAK,GAAGhB,MAAM,CAACe,OAAO;;IAE9B,IAAIf,MAAM,CAACiB,SAAS,KAAKvB,SAAS,EAAE;MAClCF,KAAK,CAAC0B,IAAI,GAAGlB,MAAM,CAACiB,SAAS;;IAG/B,OAAO1B,kBAAkB,CAACC,KAAK,CAAC;EAClC,CAAC;EAAA,OAAA+B,kBAAA,CAAAzD,KAAA,OAAAC,SAAA;AAAA;AAED,IAAI2D,aAAa,GAAiC,IAAI;AACtD,IAAIC,oCAAoC,GAAW,CAAC;AACpD,IAAIC,oCAAoC,GAAW,CAAC;AACpD,IAAIC,wBAAwB,GAAY,KAAK;AAE7C,SAASC,8BAA8BA,CAAA;EACrC,IAAIzB,QAAQ,GAAGuB,oCAAoC;EACnD,IAAIC,wBAAwB,IAAIF,oCAAoC,GAAG,CAAC,EAAE;IACxEtB,QAAQ,IAAI0B,IAAI,CAACC,GAAG,EAAE,GAAGL,oCAAoC;;EAE/D,OAAOtB,QAAQ;AACjB;AAEA,eAAe;EACb,IAAIpC,IAAIA,CAAA;IACN,OAAO,YAAY;EACrB,CAAC;EACKgE,iBAAiB;IAAA,IAAAC,kBAAA,GAAAlE,iBAAA,YAACmE,OAAyB;MAC/C,OAAO5C,kBAAkB,CAAC4C,OAAO,CAAC;IACpC,CAAC;IAAA,SAFKF,iBAAiBA,CAAAG,GAAA;MAAA,OAAAF,kBAAA,CAAApE,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjBkE,iBAAiB;EAAA;EAGjBI,YAAY;IAAA,IAAAC,aAAA,GAAAtE,iBAAA,YAChBmE,OAAyB,EACzBI,YAAoC,EACpCC,iBAAwC;MAExC,OAAOjD,kBAAkB,CAAC4C,OAAO,CAAC;IACpC,CAAC;IAAA,SANKE,YAAYA,CAAAI,GAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAL,aAAA,CAAAxE,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAZsE,YAAY;EAAA;EAOZO,cAAc;IAAA,IAAAC,eAAA,GAAA7E,iBAAA,YAACmE,OAAyB;MAC5C,OAAO5C,kBAAkB,CAAC4C,OAAO,CAAC;IACpC,CAAC;IAAA,SAFKS,cAAcA,CAAAE,GAAA;MAAA,OAAAD,eAAA,CAAA/E,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAd6E,cAAc;EAAA;EAGdG,iBAAiB;IAAA,IAAAC,kBAAA,GAAAhF,iBAAA,YACrBmE,OAAyB,EACzBnC,MAA6B;MAE7B,OAAOoB,iBAAiB,CAACe,OAAO,EAAEnC,MAAM,CAAC;IAC3C,CAAC;IAAA,SALK+C,iBAAiBA,CAAAE,GAAA,EAAAC,GAAA;MAAA,OAAAF,kBAAA,CAAAlF,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjBgF,iBAAiB;EAAA;EAMjBI,WAAW;IAAA,IAAAC,YAAA,GAAApF,iBAAA,YACfmE,OAAyB,EACzBnC,MAA6B;MAE7B,OAAOoB,iBAAiB,CAACe,OAAO,EAAEnC,MAAM,CAAC;IAC3C,CAAC;IAAA,SALKmD,WAAWA,CAAAE,GAAA,EAAAC,IAAA;MAAA,OAAAF,YAAA,CAAAtF,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAXoF,WAAW;EAAA;EAOXI,YAAY;IAAA,IAAAC,aAAA,GAAAxF,iBAAA,eAAI,CAAC;IAAA,SAAjBuF,YAAYA,CAAA;MAAA,OAAAC,aAAA,CAAA1F,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAZwF,YAAY;EAAA;EACZE,iBAAiB;IAAA,IAAAC,kBAAA,GAAA1F,iBAAA,eAAI,CAAC;IAAA,SAAtByF,iBAAiBA,CAAA;MAAA,OAAAC,kBAAA,CAAA5F,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjB0F,iBAAiB;EAAA;EACjBE,iBAAiB;IAAA,IAAAC,kBAAA,GAAA5F,iBAAA,YAACmE,OAAyB;MAC/C,OAAO5C,kBAAkB,CAAC4C,OAAO,CAAC;IACpC,CAAC;IAAA,SAFKwB,iBAAiBA,CAAAE,IAAA;MAAA,OAAAD,kBAAA,CAAA9F,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjB4F,iBAAiB;EAAA;EAGjBG,YAAY;IAAA,IAAAC,aAAA,GAAA/F,iBAAA,YAChBuE,YAA0D,EAC1DC,iBAAwC;MAExC,IAAMwB,MAAM,GAAG,OAAOzB,YAAY,KAAK,QAAQ,GAAGA,YAAY,GAAGA,YAAY,CAACtC,GAAG;MACjF,IAAMT,KAAK,GAAG,IAAIyE,KAAK,CAACD,MAAM,CAAC;MAE/BxE,KAAK,CAAC0E,YAAY,GAAG,YAAK;QACxBzG,wBAAwB,CAAC0G,IAAI,CAAC,yBAAyB,EAAE;UACvDC,GAAG,EAAE5E,KAAK;UACVQ,MAAM,EAAET,kBAAkB,CAACC,KAAK;SACjC,CAAC;MACJ,CAAC;MAEDA,KAAK,CAAC6E,OAAO,GAAG,YAAK;QACnB5G,wBAAwB,CAAC0G,IAAI,CAAC,oBAAoB,EAAE;UAClDC,GAAG,EAAE5E,KAAK;UACVR,KAAK,EAAEQ,KAAK,CAACR,KAAM,CAACsF;SACrB,CAAC;MACJ,CAAC;MAED,IAAMtE,MAAM,SAASoB,iBAAiB,CAAC5B,KAAK,EAAEgD,iBAAiB,CAAC;MAEhE,OAAO,CAAChD,KAAK,EAAEQ,MAAM,CAAC;IACxB,CAAC;IAAA,SAxBK8D,YAAYA,CAAAS,IAAA,EAAAC,IAAA;MAAA,OAAAT,aAAA,CAAAjG,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAZ+F,YAAY;EAAA;EAyBZW,cAAc;IAAA,IAAAC,eAAA,GAAA1G,iBAAA,YAACmE,OAAyB;MAC5CA,OAAO,CAACV,KAAK,EAAE;MACfU,OAAO,CAACwC,eAAe,CAAC,KAAK,CAAC;MAC9BxC,OAAO,CAACyC,IAAI,EAAE;MACd,OAAOrF,kBAAkB,CAAC4C,OAAO,CAAC;IACpC,CAAC;IAAA,SALKsC,cAAcA,CAAAI,IAAA;MAAA,OAAAH,eAAA,CAAA5G,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAd0G,cAAc;EAAA;EAMdK,iBAAiB;IAAA,IAAAC,kBAAA,GAAA/G,iBAAA,YACrBmE,OAAyB,EACzBnC,MAA6B;MAE7B,OAAOoB,iBAAiB,CAACe,OAAO,EAAEnC,MAAM,CAAC;IAC3C,CAAC;IAAA,SALK8E,iBAAiBA,CAAAE,IAAA,EAAAC,IAAA;MAAA,OAAAF,kBAAA,CAAAjH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjB+G,iBAAiB;EAAA;EAMjBI,WAAW;IAAA,IAAAC,YAAA,GAAAnH,iBAAA,YACfmE,OAAyB,EACzBnC,MAA6B;MAE7B,OAAOoB,iBAAiB,CAACe,OAAO,EAAEnC,MAAM,CAAC;IAC3C,CAAC;IAAA,SALKkF,WAAWA,CAAAE,IAAA,EAAAC,IAAA;MAAA,OAAAF,YAAA,CAAArH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAXmH,WAAW;EAAA;EASXI,uBAAuB;IAAA,IAAAC,wBAAA,GAAAvH,iBAAA;MAAA,IAAAwH,cAAA,EAAAC,eAAA,EAAAC,eAAA;MAC3B,OAAO;QACLC,SAAS,EAAE,EAAAH,cAAA,GAAA9D,aAAa,qBAAb8D,cAAA,CAAelH,KAAK,MAAK,WAAW,IAAI,EAAAmH,eAAA,GAAA/D,aAAa,qBAAb+D,eAAA,CAAenH,KAAK,MAAK,UAAU;QACtFsH,WAAW,EAAE,EAAAF,eAAA,GAAAhE,aAAa,qBAAbgE,eAAA,CAAepH,KAAK,MAAK,WAAW;QACjDuH,eAAe,EAAE,KAAK;QACtBzF,cAAc,EAAE0B,8BAA8B,EAAE;QAChD7B,GAAG,EAAE;OACN;IACH,CAAC;IAAA,SARKqF,uBAAuBA,CAAA;MAAA,OAAAC,wBAAA,CAAAzH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvBuH,uBAAuB;EAAA;EASvBQ,oBAAoB;IAAA,IAAAC,qBAAA,GAAA/H,iBAAA,YAACgI,OAAO;MAKhC,IAAI,OAAO9H,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACW,YAAY,EAAE;QAC/D,MAAM,IAAII,KAAK,CAAC,4BAA4B,CAAC;;MAG/C0C,oCAAoC,GAAG,CAAC;MACxCC,oCAAoC,GAAG,CAAC;MAExC,IAAMqE,MAAM,SAAStH,YAAY,CAAC;QAAEuH,KAAK,EAAE;MAAI,CAAE,CAAC;MAElDxE,aAAa,GAAG,IAAKyE,MAAc,CAACC,aAAa,CAC/CH,MAAM,EACN,CAAAD,OAAO,oBAAPA,OAAO,CAAEK,GAAG,KAAI3I,uBAAuB,CAAC4I,YAAY,CAACD,GAAG,CACzD;MAED3E,aAAa,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,YAAK;QAC3C3E,oCAAoC,GAAGE,8BAA8B,EAAE;QACvED,wBAAwB,GAAG,KAAK;MAClC,CAAC,CAAC;MAEFH,aAAa,CAAC6E,gBAAgB,CAAC,QAAQ,EAAE,YAAK;QAC5C5E,oCAAoC,GAAGI,IAAI,CAACC,GAAG,EAAE;QACjDH,wBAAwB,GAAG,IAAI;MACjC,CAAC,CAAC;MAEFH,aAAa,CAAC6E,gBAAgB,CAAC,OAAO,EAAE,YAAK;QAC3C5E,oCAAoC,GAAGI,IAAI,CAACC,GAAG,EAAE;QACjDJ,oCAAoC,GAAG,CAAC;QACxCC,wBAAwB,GAAG,IAAI;MACjC,CAAC,CAAC;MAEFH,aAAa,CAAC6E,gBAAgB,CAAC,MAAM,EAAE,YAAK;QAC1C3E,oCAAoC,GAAGE,8BAA8B,EAAE;QACvED,wBAAwB,GAAG,KAAK;QAGhCoE,MAAM,CAACO,SAAS,EAAE,CAACC,OAAO,CAAC,UAACC,KAAK;UAAA,OAAKA,KAAK,CAACC,IAAI,EAAE;QAAA,EAAC;MACrD,CAAC,CAAC;MAEF,IAAAC,qBAAA,SAAiC,IAAI,CAACtB,uBAAuB,EAAE;QAAvDrF,GAAG,GAAA2G,qBAAA,CAAH3G,GAAG;QAAKD,MAAM,GAAA6G,wBAAA,CAAAD,qBAAA,EAAAE,SAAA;MAEtB,OAAO;QAAE7G,GAAG,EAAE,IAAI;QAAED,MAAM,EAANA;MAAM,CAAE;IAC9B,CAAC;IAAA,SA9CK8F,oBAAoBA,CAAAiB,IAAA;MAAA,OAAAhB,qBAAA,CAAAjI,KAAA,OAAAC,SAAA;IAAA;IAAA,OAApB+H,oBAAoB;EAAA;EA+CpBkB,mBAAmB;IAAA,IAAAC,oBAAA,GAAAjJ,iBAAA;MACvB,IAAI0D,aAAa,KAAK,IAAI,EAAE;QAC1B,MAAM,IAAIzC,KAAK,CACb,iJAAiJ,CAClJ;;MAGH,IAAIyC,aAAa,CAACpD,KAAK,KAAK,QAAQ,EAAE;QACpCoD,aAAa,CAACwF,MAAM,EAAE;OACvB,MAAM;QACLxF,aAAa,CAACyF,KAAK,EAAE;;MAGvB,OAAO,IAAI,CAAC7B,uBAAuB,EAAE;IACvC,CAAC;IAAA,SAdK0B,mBAAmBA,CAAA;MAAA,OAAAC,oBAAA,CAAAnJ,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBiJ,mBAAmB;EAAA;EAenBI,mBAAmB;IAAA,IAAAC,oBAAA,GAAArJ,iBAAA;MACvB,IAAI0D,aAAa,KAAK,IAAI,EAAE;QAC1B,MAAM,IAAIzC,KAAK,CACb,iJAAiJ,CAClJ;;MAIHyC,aAAa,CAACD,KAAK,EAAE;MAErB,OAAO,IAAI,CAAC6D,uBAAuB,EAAE;IACvC,CAAC;IAAA,SAXK8B,mBAAmBA,CAAA;MAAA,OAAAC,oBAAA,CAAAvJ,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBqJ,mBAAmB;EAAA;EAYnBE,kBAAkB;IAAA,IAAAC,mBAAA,GAAAvJ,iBAAA;MACtB,IAAI0D,aAAa,KAAK,IAAI,EAAE;QAC1B,MAAM,IAAIzC,KAAK,CACb,iJAAiJ,CAClJ;;MAGH,IAAIyC,aAAa,CAACpD,KAAK,KAAK,UAAU,EAAE;QACtC,OAAO,IAAI,CAACgH,uBAAuB,EAAE;;MAGvC,IAAMkC,WAAW,GAAG,IAAIrI,OAAO,CAAO,UAACC,OAAO;QAAA,OAC5CsC,aAAa,CAAC6E,gBAAgB,CAAC,eAAe,EAAE,UAACkB,CAAC;UAAA,OAAKrI,OAAO,CAACqI,CAAC,CAACC,IAAI,CAAC;QAAA,EAAC;MAAA,EACxE;MAEDhG,aAAa,CAACiF,IAAI,EAAE;MAEpB,IAAMe,IAAI,SAASF,WAAW;MAC9B,IAAMG,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MAErC,OAAAI,aAAA,CAAAA,aAAA,WACY,IAAI,CAACxC,uBAAuB,EAAE;QACxCrF,GAAG,EAAE0H;MAAG;IAEZ,CAAC;IAAA,SAxBKL,kBAAkBA,CAAA;MAAA,OAAAC,mBAAA,CAAAzJ,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAlBuJ,kBAAkB;EAAA;EAyBlBS,mBAAmB;IAAA,IAAAC,oBAAA,GAAAhK,iBAAA;MACvB0D,aAAa,GAAG,IAAI;IACtB,CAAC;IAAA,SAFKqG,mBAAmBA,CAAA;MAAA,OAAAC,oBAAA,CAAAlK,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBgK,mBAAmB;EAAA;EAInBE,mBAAmB;IAAA,IAAAC,oBAAA,GAAAlK,iBAAA;MACvB,IAAMmK,WAAW,SAASxK,2BAA2B,CAAC,YAAY,CAAC;MACnE,QAAQwK,WAAW;QACjB,KAAK3K,gBAAgB,CAACe,OAAO;UAC3B,OAAO;YACLyB,MAAM,EAAExC,gBAAgB,CAACe,OAAO;YAChC6J,OAAO,EAAE,OAAO;YAChBC,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;WACV;QACH,KAAK9K,gBAAgB,CAACgB,MAAM;UAC1B,OAAO;YACLwB,MAAM,EAAExC,gBAAgB,CAACgB,MAAM;YAC/B4J,OAAO,EAAE,OAAO;YAChBC,WAAW,EAAE,IAAI;YACjBC,OAAO,EAAE;WACV;QACH;UACE,aAAa,IAAI,CAACC,uBAAuB,EAAE;;IAEjD,CAAC;IAAA,SApBKN,mBAAmBA,CAAA;MAAA,OAAAC,oBAAA,CAAApK,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnBkK,mBAAmB;EAAA;EAqBnBM,uBAAuB;IAAA,IAAAC,wBAAA,GAAAxK,iBAAA;MAC3B,IAAI;QACF,IAAMiI,MAAM,SAAStH,YAAY,CAAC;UAAEuH,KAAK,EAAE;QAAI,CAAE,CAAC;QAClDD,MAAM,CAACO,SAAS,EAAE,CAACC,OAAO,CAAC,UAACC,KAAK;UAAA,OAAKA,KAAK,CAACC,IAAI,EAAE;QAAA,EAAC;QACnD,OAAO;UACL3G,MAAM,EAAExC,gBAAgB,CAACe,OAAO;UAChC6J,OAAO,EAAE,OAAO;UAChBC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE;SACV;OACF,CAAC,OAAAG,OAAA,EAAM;QACN,OAAO;UACLzI,MAAM,EAAExC,gBAAgB,CAACgB,MAAM;UAC/B4J,OAAO,EAAE,OAAO;UAChBC,WAAW,EAAE,IAAI;UACjBC,OAAO,EAAE;SACV;;IAEL,CAAC;IAAA,SAlBKC,uBAAuBA,CAAA;MAAA,OAAAC,wBAAA,CAAA1K,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvBwK,uBAAuB;EAAA;CAmB9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}