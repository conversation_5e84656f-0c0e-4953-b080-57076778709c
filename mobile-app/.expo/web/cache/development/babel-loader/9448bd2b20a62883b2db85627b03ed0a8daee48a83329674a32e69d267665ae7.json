{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/MaterialCommunityIcons.json\";\nexport default createIconSet(glyphMap, 'material-community', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/@expo/vector-icons/src/MaterialCommunityIcons.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/MaterialCommunityIcons.json';\n\nexport default createIconSet(glyphMap, 'material-community', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,oBAAoB,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}