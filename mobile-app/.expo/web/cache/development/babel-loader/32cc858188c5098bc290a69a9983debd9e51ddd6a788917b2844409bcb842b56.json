{"ast": null, "code": "import * as Audio from \"./Audio\";\nexport { Audio };\nexport { default as Video } from \"./Video\";\nexport * from \"./AV.types\";\nexport * from \"./Audio.types\";\nexport * from \"./Video.types\";", "map": {"version": 3, "names": ["Audio", "default", "Video"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/index.ts"], "sourcesContent": ["import * as Audio from './Audio';\nexport { Audio };\nexport { default as Video } from './Video';\n\nexport * from './AV.types';\nexport * from './Audio.types';\nexport * from './Video.types';\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK;AACjB,SAASA,KAAK;AACd,SAASC,OAAO,IAAIC,KAAK;AAEzB;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}