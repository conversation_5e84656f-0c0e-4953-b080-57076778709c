{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport createElement from \"../createElement\";\nimport * as forwardedProps from \"../../modules/forwardedProps\";\nimport pick from \"../../modules/pick\";\nimport useElementLayout from \"../../modules/useElementLayout\";\nimport useLayoutEffect from \"../../modules/useLayoutEffect\";\nimport useMergeRefs from \"../../modules/useMergeRefs\";\nimport usePlatformMethods from \"../../modules/usePlatformMethods\";\nimport useResponderEvents from \"../../modules/useResponderEvents\";\nimport { getLocaleDirection, useLocaleContext } from \"../../modules/useLocale\";\nimport StyleSheet from \"../StyleSheet\";\nimport TextInputState from \"../../modules/TextInputState\";\nvar isSelectionStale = function isSelectionStale(node, selection) {\n  var selectionEnd = node.selectionEnd,\n    selectionStart = node.selectionStart;\n  var start = selection.start,\n    end = selection.end;\n  return start !== selectionStart || end !== selectionEnd;\n};\nvar setSelection = function setSelection(node, selection) {\n  if (isSelectionStale(node, selection)) {\n    var start = selection.start,\n      end = selection.end;\n    try {\n      node.setSelectionRange(start, end || start);\n    } catch (e) {}\n  }\n};\nvar forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {\n  autoCapitalize: true,\n  autoComplete: true,\n  autoCorrect: true,\n  autoFocus: true,\n  defaultValue: true,\n  disabled: true,\n  lang: true,\n  maxLength: true,\n  onChange: true,\n  onScroll: true,\n  placeholder: true,\n  pointerEvents: true,\n  readOnly: true,\n  rows: true,\n  spellCheck: true,\n  value: true,\n  type: true\n});\nvar pickProps = function pickProps(props) {\n  return pick(props, forwardPropsList);\n};\nfunction isEventComposing(nativeEvent) {\n  return nativeEvent.isComposing || nativeEvent.keyCode === 229;\n}\nvar focusTimeout = null;\nvar TextInput = React.forwardRef(function (props, forwardedRef) {\n  var _props$autoCapitalize = props.autoCapitalize,\n    autoCapitalize = _props$autoCapitalize === void 0 ? 'sentences' : _props$autoCapitalize,\n    autoComplete = props.autoComplete,\n    autoCompleteType = props.autoCompleteType,\n    _props$autoCorrect = props.autoCorrect,\n    autoCorrect = _props$autoCorrect === void 0 ? true : _props$autoCorrect,\n    blurOnSubmit = props.blurOnSubmit,\n    caretHidden = props.caretHidden,\n    clearTextOnFocus = props.clearTextOnFocus,\n    dir = props.dir,\n    editable = props.editable,\n    enterKeyHint = props.enterKeyHint,\n    inputMode = props.inputMode,\n    keyboardType = props.keyboardType,\n    _props$multiline = props.multiline,\n    multiline = _props$multiline === void 0 ? false : _props$multiline,\n    numberOfLines = props.numberOfLines,\n    onBlur = props.onBlur,\n    onChange = props.onChange,\n    onChangeText = props.onChangeText,\n    onContentSizeChange = props.onContentSizeChange,\n    onFocus = props.onFocus,\n    onKeyPress = props.onKeyPress,\n    onLayout = props.onLayout,\n    onMoveShouldSetResponder = props.onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,\n    onResponderEnd = props.onResponderEnd,\n    onResponderGrant = props.onResponderGrant,\n    onResponderMove = props.onResponderMove,\n    onResponderReject = props.onResponderReject,\n    onResponderRelease = props.onResponderRelease,\n    onResponderStart = props.onResponderStart,\n    onResponderTerminate = props.onResponderTerminate,\n    onResponderTerminationRequest = props.onResponderTerminationRequest,\n    onScrollShouldSetResponder = props.onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,\n    onSelectionChange = props.onSelectionChange,\n    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder = props.onStartShouldSetResponder,\n    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,\n    onSubmitEditing = props.onSubmitEditing,\n    placeholderTextColor = props.placeholderTextColor,\n    _props$readOnly = props.readOnly,\n    readOnly = _props$readOnly === void 0 ? false : _props$readOnly,\n    returnKeyType = props.returnKeyType,\n    rows = props.rows,\n    _props$secureTextEntr = props.secureTextEntry,\n    secureTextEntry = _props$secureTextEntr === void 0 ? false : _props$secureTextEntr,\n    selection = props.selection,\n    selectTextOnFocus = props.selectTextOnFocus,\n    showSoftInputOnFocus = props.showSoftInputOnFocus,\n    spellCheck = props.spellCheck;\n  var type;\n  var _inputMode;\n  if (inputMode != null) {\n    _inputMode = inputMode;\n    if (inputMode === 'email') {\n      type = 'email';\n    } else if (inputMode === 'tel') {\n      type = 'tel';\n    } else if (inputMode === 'search') {\n      type = 'search';\n    } else if (inputMode === 'url') {\n      type = 'url';\n    } else {\n      type = 'text';\n    }\n  } else if (keyboardType != null) {\n    switch (keyboardType) {\n      case 'email-address':\n        type = 'email';\n        break;\n      case 'number-pad':\n      case 'numeric':\n        _inputMode = 'numeric';\n        break;\n      case 'decimal-pad':\n        _inputMode = 'decimal';\n        break;\n      case 'phone-pad':\n        type = 'tel';\n        break;\n      case 'search':\n      case 'web-search':\n        type = 'search';\n        break;\n      case 'url':\n        type = 'url';\n        break;\n      default:\n        type = 'text';\n    }\n  }\n  if (secureTextEntry) {\n    type = 'password';\n  }\n  var dimensions = React.useRef({\n    height: null,\n    width: null\n  });\n  var hostRef = React.useRef(null);\n  var prevSelection = React.useRef(null);\n  var prevSecureTextEntry = React.useRef(false);\n  React.useEffect(function () {\n    if (hostRef.current && prevSelection.current) {\n      setSelection(hostRef.current, prevSelection.current);\n    }\n    prevSecureTextEntry.current = secureTextEntry;\n  }, [secureTextEntry]);\n  var handleContentSizeChange = React.useCallback(function (hostNode) {\n    if (multiline && onContentSizeChange && hostNode != null) {\n      var newHeight = hostNode.scrollHeight;\n      var newWidth = hostNode.scrollWidth;\n      if (newHeight !== dimensions.current.height || newWidth !== dimensions.current.width) {\n        dimensions.current.height = newHeight;\n        dimensions.current.width = newWidth;\n        onContentSizeChange({\n          nativeEvent: {\n            contentSize: {\n              height: dimensions.current.height,\n              width: dimensions.current.width\n            }\n          }\n        });\n      }\n    }\n  }, [multiline, onContentSizeChange]);\n  var imperativeRef = React.useMemo(function () {\n    return function (hostNode) {\n      if (hostNode != null) {\n        hostNode.clear = function () {\n          if (hostNode != null) {\n            hostNode.value = '';\n          }\n        };\n        hostNode.isFocused = function () {\n          return hostNode != null && TextInputState.currentlyFocusedField() === hostNode;\n        };\n        handleContentSizeChange(hostNode);\n      }\n    };\n  }, [handleContentSizeChange]);\n  function handleBlur(e) {\n    TextInputState._currentlyFocusedNode = null;\n    if (onBlur) {\n      e.nativeEvent.text = e.target.value;\n      onBlur(e);\n    }\n  }\n  function handleChange(e) {\n    var hostNode = e.target;\n    var text = hostNode.value;\n    e.nativeEvent.text = text;\n    handleContentSizeChange(hostNode);\n    if (onChange) {\n      onChange(e);\n    }\n    if (onChangeText) {\n      onChangeText(text);\n    }\n  }\n  function handleFocus(e) {\n    var hostNode = e.target;\n    if (onFocus) {\n      e.nativeEvent.text = hostNode.value;\n      onFocus(e);\n    }\n    if (hostNode != null) {\n      TextInputState._currentlyFocusedNode = hostNode;\n      if (clearTextOnFocus) {\n        hostNode.value = '';\n      }\n      if (selectTextOnFocus) {\n        if (focusTimeout != null) {\n          clearTimeout(focusTimeout);\n        }\n        focusTimeout = setTimeout(function () {\n          if (hostNode != null && document.activeElement === hostNode) {\n            hostNode.select();\n          }\n        }, 0);\n      }\n    }\n  }\n  function handleKeyDown(e) {\n    var hostNode = e.target;\n    e.stopPropagation();\n    var blurOnSubmitDefault = !multiline;\n    var shouldBlurOnSubmit = blurOnSubmit == null ? blurOnSubmitDefault : blurOnSubmit;\n    var nativeEvent = e.nativeEvent;\n    var isComposing = isEventComposing(nativeEvent);\n    if (onKeyPress) {\n      onKeyPress(e);\n    }\n    if (e.key === 'Enter' && !e.shiftKey && !isComposing && !e.isDefaultPrevented()) {\n      if ((blurOnSubmit || !multiline) && onSubmitEditing) {\n        e.preventDefault();\n        nativeEvent.text = e.target.value;\n        onSubmitEditing(e);\n      }\n      if (shouldBlurOnSubmit && hostNode != null) {\n        setTimeout(function () {\n          return hostNode.blur();\n        }, 0);\n      }\n    }\n  }\n  function handleSelectionChange(e) {\n    try {\n      var _e$target = e.target,\n        selectionStart = _e$target.selectionStart,\n        selectionEnd = _e$target.selectionEnd;\n      var _selection = {\n        start: selectionStart,\n        end: selectionEnd\n      };\n      if (onSelectionChange) {\n        e.nativeEvent.selection = _selection;\n        e.nativeEvent.text = e.target.value;\n        onSelectionChange(e);\n      }\n      if (prevSecureTextEntry.current === secureTextEntry) {\n        prevSelection.current = _selection;\n      }\n    } catch (e) {}\n  }\n  useLayoutEffect(function () {\n    var node = hostRef.current;\n    if (node != null && selection != null) {\n      setSelection(node, selection);\n    }\n    if (document.activeElement === node) {\n      TextInputState._currentlyFocusedNode = node;\n    }\n  }, [hostRef, selection]);\n  var component = multiline ? 'textarea' : 'input';\n  useElementLayout(hostRef, onLayout);\n  useResponderEvents(hostRef, {\n    onMoveShouldSetResponder: onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture: onMoveShouldSetResponderCapture,\n    onResponderEnd: onResponderEnd,\n    onResponderGrant: onResponderGrant,\n    onResponderMove: onResponderMove,\n    onResponderReject: onResponderReject,\n    onResponderRelease: onResponderRelease,\n    onResponderStart: onResponderStart,\n    onResponderTerminate: onResponderTerminate,\n    onResponderTerminationRequest: onResponderTerminationRequest,\n    onScrollShouldSetResponder: onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture: onScrollShouldSetResponderCapture,\n    onSelectionChangeShouldSetResponder: onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture: onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder: onStartShouldSetResponder,\n    onStartShouldSetResponderCapture: onStartShouldSetResponderCapture\n  });\n  var _useLocaleContext = useLocaleContext(),\n    contextDirection = _useLocaleContext.direction;\n  var supportedProps = pickProps(props);\n  supportedProps.autoCapitalize = autoCapitalize;\n  supportedProps.autoComplete = autoComplete || autoCompleteType || 'on';\n  supportedProps.autoCorrect = autoCorrect ? 'on' : 'off';\n  supportedProps.dir = dir !== undefined ? dir : 'auto';\n  supportedProps.enterKeyHint = enterKeyHint || returnKeyType;\n  supportedProps.inputMode = _inputMode;\n  supportedProps.onBlur = handleBlur;\n  supportedProps.onChange = handleChange;\n  supportedProps.onFocus = handleFocus;\n  supportedProps.onKeyDown = handleKeyDown;\n  supportedProps.onSelect = handleSelectionChange;\n  supportedProps.readOnly = readOnly === true || editable === false;\n  supportedProps.rows = multiline ? rows != null ? rows : numberOfLines : 1;\n  supportedProps.spellCheck = spellCheck != null ? spellCheck : autoCorrect;\n  supportedProps.style = [{\n    '--placeholderTextColor': placeholderTextColor\n  }, styles.textinput$raw, styles.placeholder, props.style, caretHidden && styles.caretHidden];\n  supportedProps.type = multiline ? undefined : type;\n  supportedProps.virtualkeyboardpolicy = showSoftInputOnFocus === false ? 'manual' : 'auto';\n  var platformMethodsRef = usePlatformMethods(supportedProps);\n  var setRef = useMergeRefs(hostRef, platformMethodsRef, imperativeRef, forwardedRef);\n  supportedProps.ref = setRef;\n  var langDirection = props.lang != null ? getLocaleDirection(props.lang) : null;\n  var componentDirection = props.dir || langDirection;\n  var writingDirection = componentDirection || contextDirection;\n  var element = createElement(component, supportedProps, {\n    writingDirection: writingDirection\n  });\n  return element;\n});\nTextInput.displayName = 'TextInput';\nTextInput.State = TextInputState;\nvar styles = StyleSheet.create({\n  textinput$raw: {\n    MozAppearance: 'textfield',\n    WebkitAppearance: 'none',\n    backgroundColor: 'transparent',\n    border: '0 solid black',\n    borderRadius: 0,\n    boxSizing: 'border-box',\n    font: '14px System',\n    margin: 0,\n    padding: 0,\n    resize: 'none'\n  },\n  placeholder: {\n    placeholderTextColor: 'var(--placeholderTextColor)'\n  },\n  caretHidden: {\n    caretColor: 'transparent'\n  }\n});\nexport default TextInput;", "map": {"version": 3, "names": ["React", "createElement", "forwardedProps", "pick", "useElementLayout", "useLayoutEffect", "useMergeRefs", "usePlatformMethods", "useResponderEvents", "getLocaleDirection", "useLocaleContext", "StyleSheet", "TextInputState", "isSelectionStale", "node", "selection", "selectionEnd", "selectionStart", "start", "end", "setSelection", "setSelectionRange", "e", "forwardPropsList", "Object", "assign", "defaultProps", "accessibilityProps", "clickProps", "focusProps", "keyboardProps", "mouseProps", "touchProps", "styleProps", "autoCapitalize", "autoComplete", "autoCorrect", "autoFocus", "defaultValue", "disabled", "lang", "max<PERSON><PERSON><PERSON>", "onChange", "onScroll", "placeholder", "pointerEvents", "readOnly", "rows", "spell<PERSON>heck", "value", "type", "pickProps", "props", "isEventComposing", "nativeEvent", "isComposing", "keyCode", "focusTimeout", "TextInput", "forwardRef", "forwardedRef", "_props$autoCapitalize", "autoCompleteType", "_props$autoCorrect", "blurOnSubmit", "caretHidden", "clearTextOnFocus", "dir", "editable", "enterKeyHint", "inputMode", "keyboardType", "_props$multiline", "multiline", "numberOfLines", "onBlur", "onChangeText", "onContentSizeChange", "onFocus", "onKeyPress", "onLayout", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onResponderEnd", "onResponderGrant", "onResponderMove", "onResponderReject", "onResponderRelease", "onResponderStart", "onResponderTerminate", "onResponderTerminationRequest", "onScrollShouldSetResponder", "onScrollShouldSetResponderCapture", "onSelectionChange", "onSelectionChangeShouldSetResponder", "onSelectionChangeShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "onSubmitEditing", "placeholderTextColor", "_props$readOnly", "returnKeyType", "_props$secureTextEntr", "secureTextEntry", "selectTextOnFocus", "showSoftInputOnFocus", "_inputMode", "dimensions", "useRef", "height", "width", "hostRef", "prevSelection", "prevSecureTextEntry", "useEffect", "current", "handleContentSizeChange", "useCallback", "hostNode", "newHeight", "scrollHeight", "newWidth", "scrollWidth", "contentSize", "imperativeRef", "useMemo", "clear", "isFocused", "currentlyFocusedField", "handleBlur", "_currentlyFocusedNode", "text", "target", "handleChange", "handleFocus", "clearTimeout", "setTimeout", "document", "activeElement", "select", "handleKeyDown", "stopPropagation", "blurOnSubmitDefault", "shouldBlurOnSubmit", "key", "shift<PERSON>ey", "isDefaultPrevented", "preventDefault", "blur", "handleSelectionChange", "_e$target", "_selection", "component", "_useLocaleContext", "contextDirection", "direction", "supportedProps", "undefined", "onKeyDown", "onSelect", "style", "styles", "textinput$raw", "virtualkeyboardpolicy", "platformMethodsRef", "setRef", "ref", "langDirection", "componentDirection", "writingDirection", "element", "displayName", "State", "create", "MozAppearance", "WebkitAppearance", "backgroundColor", "border", "borderRadius", "boxSizing", "font", "margin", "padding", "resize", "caretColor"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/TextInput/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport * as React from 'react';\nimport createElement from '../createElement';\nimport * as forwardedProps from '../../modules/forwardedProps';\nimport pick from '../../modules/pick';\nimport useElementLayout from '../../modules/useElementLayout';\nimport useLayoutEffect from '../../modules/useLayoutEffect';\nimport useMergeRefs from '../../modules/useMergeRefs';\nimport usePlatformMethods from '../../modules/usePlatformMethods';\nimport useResponderEvents from '../../modules/useResponderEvents';\nimport { getLocaleDirection, useLocaleContext } from '../../modules/useLocale';\nimport StyleSheet from '../StyleSheet';\nimport TextInputState from '../../modules/TextInputState';\n//import { warnOnce } from '../../modules/warnOnce';\n\n/**\n * Determines whether a 'selection' prop differs from a node's existing\n * selection state.\n */\nvar isSelectionStale = (node, selection) => {\n  var selectionEnd = node.selectionEnd,\n    selectionStart = node.selectionStart;\n  var start = selection.start,\n    end = selection.end;\n  return start !== selectionStart || end !== selectionEnd;\n};\n\n/**\n * Certain input types do no support 'selectSelectionRange' and will throw an\n * error.\n */\nvar setSelection = (node, selection) => {\n  if (isSelectionStale(node, selection)) {\n    var start = selection.start,\n      end = selection.end;\n    try {\n      node.setSelectionRange(start, end || start);\n    } catch (e) {}\n  }\n};\nvar forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {\n  autoCapitalize: true,\n  autoComplete: true,\n  autoCorrect: true,\n  autoFocus: true,\n  defaultValue: true,\n  disabled: true,\n  lang: true,\n  maxLength: true,\n  onChange: true,\n  onScroll: true,\n  placeholder: true,\n  pointerEvents: true,\n  readOnly: true,\n  rows: true,\n  spellCheck: true,\n  value: true,\n  type: true\n});\nvar pickProps = props => pick(props, forwardPropsList);\n\n// If an Input Method Editor is processing key input, the 'keyCode' is 229.\n// https://www.w3.org/TR/uievents/#determine-keydown-keyup-keyCode\nfunction isEventComposing(nativeEvent) {\n  return nativeEvent.isComposing || nativeEvent.keyCode === 229;\n}\nvar focusTimeout = null;\nvar TextInput = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var _props$autoCapitalize = props.autoCapitalize,\n    autoCapitalize = _props$autoCapitalize === void 0 ? 'sentences' : _props$autoCapitalize,\n    autoComplete = props.autoComplete,\n    autoCompleteType = props.autoCompleteType,\n    _props$autoCorrect = props.autoCorrect,\n    autoCorrect = _props$autoCorrect === void 0 ? true : _props$autoCorrect,\n    blurOnSubmit = props.blurOnSubmit,\n    caretHidden = props.caretHidden,\n    clearTextOnFocus = props.clearTextOnFocus,\n    dir = props.dir,\n    editable = props.editable,\n    enterKeyHint = props.enterKeyHint,\n    inputMode = props.inputMode,\n    keyboardType = props.keyboardType,\n    _props$multiline = props.multiline,\n    multiline = _props$multiline === void 0 ? false : _props$multiline,\n    numberOfLines = props.numberOfLines,\n    onBlur = props.onBlur,\n    onChange = props.onChange,\n    onChangeText = props.onChangeText,\n    onContentSizeChange = props.onContentSizeChange,\n    onFocus = props.onFocus,\n    onKeyPress = props.onKeyPress,\n    onLayout = props.onLayout,\n    onMoveShouldSetResponder = props.onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,\n    onResponderEnd = props.onResponderEnd,\n    onResponderGrant = props.onResponderGrant,\n    onResponderMove = props.onResponderMove,\n    onResponderReject = props.onResponderReject,\n    onResponderRelease = props.onResponderRelease,\n    onResponderStart = props.onResponderStart,\n    onResponderTerminate = props.onResponderTerminate,\n    onResponderTerminationRequest = props.onResponderTerminationRequest,\n    onScrollShouldSetResponder = props.onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,\n    onSelectionChange = props.onSelectionChange,\n    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder = props.onStartShouldSetResponder,\n    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,\n    onSubmitEditing = props.onSubmitEditing,\n    placeholderTextColor = props.placeholderTextColor,\n    _props$readOnly = props.readOnly,\n    readOnly = _props$readOnly === void 0 ? false : _props$readOnly,\n    returnKeyType = props.returnKeyType,\n    rows = props.rows,\n    _props$secureTextEntr = props.secureTextEntry,\n    secureTextEntry = _props$secureTextEntr === void 0 ? false : _props$secureTextEntr,\n    selection = props.selection,\n    selectTextOnFocus = props.selectTextOnFocus,\n    showSoftInputOnFocus = props.showSoftInputOnFocus,\n    spellCheck = props.spellCheck;\n  var type;\n  var _inputMode;\n  if (inputMode != null) {\n    _inputMode = inputMode;\n    if (inputMode === 'email') {\n      type = 'email';\n    } else if (inputMode === 'tel') {\n      type = 'tel';\n    } else if (inputMode === 'search') {\n      type = 'search';\n    } else if (inputMode === 'url') {\n      type = 'url';\n    } else {\n      type = 'text';\n    }\n  } else if (keyboardType != null) {\n    // warnOnce('keyboardType', 'keyboardType is deprecated. Use inputMode.');\n    switch (keyboardType) {\n      case 'email-address':\n        type = 'email';\n        break;\n      case 'number-pad':\n      case 'numeric':\n        _inputMode = 'numeric';\n        break;\n      case 'decimal-pad':\n        _inputMode = 'decimal';\n        break;\n      case 'phone-pad':\n        type = 'tel';\n        break;\n      case 'search':\n      case 'web-search':\n        type = 'search';\n        break;\n      case 'url':\n        type = 'url';\n        break;\n      default:\n        type = 'text';\n    }\n  }\n  if (secureTextEntry) {\n    type = 'password';\n  }\n  var dimensions = React.useRef({\n    height: null,\n    width: null\n  });\n  var hostRef = React.useRef(null);\n  var prevSelection = React.useRef(null);\n  var prevSecureTextEntry = React.useRef(false);\n  React.useEffect(() => {\n    if (hostRef.current && prevSelection.current) {\n      setSelection(hostRef.current, prevSelection.current);\n    }\n    prevSecureTextEntry.current = secureTextEntry;\n  }, [secureTextEntry]);\n  var handleContentSizeChange = React.useCallback(hostNode => {\n    if (multiline && onContentSizeChange && hostNode != null) {\n      var newHeight = hostNode.scrollHeight;\n      var newWidth = hostNode.scrollWidth;\n      if (newHeight !== dimensions.current.height || newWidth !== dimensions.current.width) {\n        dimensions.current.height = newHeight;\n        dimensions.current.width = newWidth;\n        onContentSizeChange({\n          nativeEvent: {\n            contentSize: {\n              height: dimensions.current.height,\n              width: dimensions.current.width\n            }\n          }\n        });\n      }\n    }\n  }, [multiline, onContentSizeChange]);\n  var imperativeRef = React.useMemo(() => hostNode => {\n    // TextInput needs to add more methods to the hostNode in addition to those\n    // added by `usePlatformMethods`. This is temporarily until an API like\n    // `TextInput.clear(hostRef)` is added to React Native.\n    if (hostNode != null) {\n      hostNode.clear = function () {\n        if (hostNode != null) {\n          hostNode.value = '';\n        }\n      };\n      hostNode.isFocused = function () {\n        return hostNode != null && TextInputState.currentlyFocusedField() === hostNode;\n      };\n      handleContentSizeChange(hostNode);\n    }\n  }, [handleContentSizeChange]);\n  function handleBlur(e) {\n    TextInputState._currentlyFocusedNode = null;\n    if (onBlur) {\n      e.nativeEvent.text = e.target.value;\n      onBlur(e);\n    }\n  }\n  function handleChange(e) {\n    var hostNode = e.target;\n    var text = hostNode.value;\n    e.nativeEvent.text = text;\n    handleContentSizeChange(hostNode);\n    if (onChange) {\n      onChange(e);\n    }\n    if (onChangeText) {\n      onChangeText(text);\n    }\n  }\n  function handleFocus(e) {\n    var hostNode = e.target;\n    if (onFocus) {\n      e.nativeEvent.text = hostNode.value;\n      onFocus(e);\n    }\n    if (hostNode != null) {\n      TextInputState._currentlyFocusedNode = hostNode;\n      if (clearTextOnFocus) {\n        hostNode.value = '';\n      }\n      if (selectTextOnFocus) {\n        // Safari requires selection to occur in a setTimeout\n        if (focusTimeout != null) {\n          clearTimeout(focusTimeout);\n        }\n        focusTimeout = setTimeout(() => {\n          // Check if the input is still focused after the timeout\n          // (see #2704)\n          if (hostNode != null && document.activeElement === hostNode) {\n            hostNode.select();\n          }\n        }, 0);\n      }\n    }\n  }\n  function handleKeyDown(e) {\n    var hostNode = e.target;\n    // Prevent key events bubbling (see #612)\n    e.stopPropagation();\n    var blurOnSubmitDefault = !multiline;\n    var shouldBlurOnSubmit = blurOnSubmit == null ? blurOnSubmitDefault : blurOnSubmit;\n    var nativeEvent = e.nativeEvent;\n    var isComposing = isEventComposing(nativeEvent);\n    if (onKeyPress) {\n      onKeyPress(e);\n    }\n    if (e.key === 'Enter' && !e.shiftKey &&\n    // Do not call submit if composition is occuring.\n    !isComposing && !e.isDefaultPrevented()) {\n      if ((blurOnSubmit || !multiline) && onSubmitEditing) {\n        // prevent \"Enter\" from inserting a newline or submitting a form\n        e.preventDefault();\n        nativeEvent.text = e.target.value;\n        onSubmitEditing(e);\n      }\n      if (shouldBlurOnSubmit && hostNode != null) {\n        setTimeout(() => hostNode.blur(), 0);\n      }\n    }\n  }\n  function handleSelectionChange(e) {\n    try {\n      var _e$target = e.target,\n        selectionStart = _e$target.selectionStart,\n        selectionEnd = _e$target.selectionEnd;\n      var _selection = {\n        start: selectionStart,\n        end: selectionEnd\n      };\n      if (onSelectionChange) {\n        e.nativeEvent.selection = _selection;\n        e.nativeEvent.text = e.target.value;\n        onSelectionChange(e);\n      }\n      if (prevSecureTextEntry.current === secureTextEntry) {\n        prevSelection.current = _selection;\n      }\n    } catch (e) {}\n  }\n  useLayoutEffect(() => {\n    var node = hostRef.current;\n    if (node != null && selection != null) {\n      setSelection(node, selection);\n    }\n    if (document.activeElement === node) {\n      TextInputState._currentlyFocusedNode = node;\n    }\n  }, [hostRef, selection]);\n  var component = multiline ? 'textarea' : 'input';\n  useElementLayout(hostRef, onLayout);\n  useResponderEvents(hostRef, {\n    onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture,\n    onResponderEnd,\n    onResponderGrant,\n    onResponderMove,\n    onResponderReject,\n    onResponderRelease,\n    onResponderStart,\n    onResponderTerminate,\n    onResponderTerminationRequest,\n    onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture,\n    onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder,\n    onStartShouldSetResponderCapture\n  });\n  var _useLocaleContext = useLocaleContext(),\n    contextDirection = _useLocaleContext.direction;\n  var supportedProps = pickProps(props);\n  supportedProps.autoCapitalize = autoCapitalize;\n  supportedProps.autoComplete = autoComplete || autoCompleteType || 'on';\n  supportedProps.autoCorrect = autoCorrect ? 'on' : 'off';\n  // 'auto' by default allows browsers to infer writing direction\n  supportedProps.dir = dir !== undefined ? dir : 'auto';\n  /*\n  if (returnKeyType != null) {\n    warnOnce('returnKeyType', 'returnKeyType is deprecated. Use enterKeyHint.');\n  }\n  */\n  supportedProps.enterKeyHint = enterKeyHint || returnKeyType;\n  supportedProps.inputMode = _inputMode;\n  supportedProps.onBlur = handleBlur;\n  supportedProps.onChange = handleChange;\n  supportedProps.onFocus = handleFocus;\n  supportedProps.onKeyDown = handleKeyDown;\n  supportedProps.onSelect = handleSelectionChange;\n  /*\n  if (editable != null) {\n    warnOnce('editable', 'editable is deprecated. Use readOnly.');\n  }\n  */\n  supportedProps.readOnly = readOnly === true || editable === false;\n  /*\n  if (numberOfLines != null) {\n    warnOnce(\n      'numberOfLines',\n      'TextInput numberOfLines is deprecated. Use rows.'\n    );\n  }\n  */\n  supportedProps.rows = multiline ? rows != null ? rows : numberOfLines : 1;\n  supportedProps.spellCheck = spellCheck != null ? spellCheck : autoCorrect;\n  supportedProps.style = [{\n    '--placeholderTextColor': placeholderTextColor\n  }, styles.textinput$raw, styles.placeholder, props.style, caretHidden && styles.caretHidden];\n  supportedProps.type = multiline ? undefined : type;\n  supportedProps.virtualkeyboardpolicy = showSoftInputOnFocus === false ? 'manual' : 'auto';\n  var platformMethodsRef = usePlatformMethods(supportedProps);\n  var setRef = useMergeRefs(hostRef, platformMethodsRef, imperativeRef, forwardedRef);\n  supportedProps.ref = setRef;\n  var langDirection = props.lang != null ? getLocaleDirection(props.lang) : null;\n  var componentDirection = props.dir || langDirection;\n  var writingDirection = componentDirection || contextDirection;\n  var element = createElement(component, supportedProps, {\n    writingDirection\n  });\n  return element;\n});\nTextInput.displayName = 'TextInput';\n// $FlowFixMe\nTextInput.State = TextInputState;\nvar styles = StyleSheet.create({\n  textinput$raw: {\n    MozAppearance: 'textfield',\n    WebkitAppearance: 'none',\n    backgroundColor: 'transparent',\n    border: '0 solid black',\n    borderRadius: 0,\n    boxSizing: 'border-box',\n    font: '14px System',\n    margin: 0,\n    padding: 0,\n    resize: 'none'\n  },\n  placeholder: {\n    placeholderTextColor: 'var(--placeholderTextColor)'\n  },\n  caretHidden: {\n    caretColor: 'transparent'\n  }\n});\nexport default TextInput;"], "mappings": "AAUA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa;AACpB,OAAO,KAAKC,cAAc;AAC1B,OAAOC,IAAI;AACX,OAAOC,gBAAgB;AACvB,OAAOC,eAAe;AACtB,OAAOC,YAAY;AACnB,OAAOC,kBAAkB;AACzB,OAAOC,kBAAkB;AACzB,SAASC,kBAAkB,EAAEC,gBAAgB;AAC7C,OAAOC,UAAU;AACjB,OAAOC,cAAc;AAOrB,IAAIC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAEC,SAAS,EAAK;EAC1C,IAAIC,YAAY,GAAGF,IAAI,CAACE,YAAY;IAClCC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACtC,IAAIC,KAAK,GAAGH,SAAS,CAACG,KAAK;IACzBC,GAAG,GAAGJ,SAAS,CAACI,GAAG;EACrB,OAAOD,KAAK,KAAKD,cAAc,IAAIE,GAAG,KAAKH,YAAY;AACzD,CAAC;AAMD,IAAII,YAAY,GAAG,SAAfA,YAAYA,CAAIN,IAAI,EAAEC,SAAS,EAAK;EACtC,IAAIF,gBAAgB,CAACC,IAAI,EAAEC,SAAS,CAAC,EAAE;IACrC,IAAIG,KAAK,GAAGH,SAAS,CAACG,KAAK;MACzBC,GAAG,GAAGJ,SAAS,CAACI,GAAG;IACrB,IAAI;MACFL,IAAI,CAACO,iBAAiB,CAACH,KAAK,EAAEC,GAAG,IAAID,KAAK,CAAC;IAC7C,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;EACf;AACF,CAAC;AACD,IAAIC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvB,cAAc,CAACwB,YAAY,EAAExB,cAAc,CAACyB,kBAAkB,EAAEzB,cAAc,CAAC0B,UAAU,EAAE1B,cAAc,CAAC2B,UAAU,EAAE3B,cAAc,CAAC4B,aAAa,EAAE5B,cAAc,CAAC6B,UAAU,EAAE7B,cAAc,CAAC8B,UAAU,EAAE9B,cAAc,CAAC+B,UAAU,EAAE;EAC5QC,cAAc,EAAE,IAAI;EACpBC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,IAAI;EACjBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,IAAI;EACnBC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE;AACR,CAAC,CAAC;AACF,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAGC,KAAK;EAAA,OAAIjD,IAAI,CAACiD,KAAK,EAAE7B,gBAAgB,CAAC;AAAA;AAItD,SAAS8B,gBAAgBA,CAACC,WAAW,EAAE;EACrC,OAAOA,WAAW,CAACC,WAAW,IAAID,WAAW,CAACE,OAAO,KAAK,GAAG;AAC/D;AACA,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,SAAS,GAAgB1D,KAAK,CAAC2D,UAAU,CAAC,UAACP,KAAK,EAAEQ,YAAY,EAAK;EACrE,IAAIC,qBAAqB,GAAGT,KAAK,CAAClB,cAAc;IAC9CA,cAAc,GAAG2B,qBAAqB,KAAK,KAAK,CAAC,GAAG,WAAW,GAAGA,qBAAqB;IACvF1B,YAAY,GAAGiB,KAAK,CAACjB,YAAY;IACjC2B,gBAAgB,GAAGV,KAAK,CAACU,gBAAgB;IACzCC,kBAAkB,GAAGX,KAAK,CAAChB,WAAW;IACtCA,WAAW,GAAG2B,kBAAkB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,kBAAkB;IACvEC,YAAY,GAAGZ,KAAK,CAACY,YAAY;IACjCC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,gBAAgB,GAAGd,KAAK,CAACc,gBAAgB;IACzCC,GAAG,GAAGf,KAAK,CAACe,GAAG;IACfC,QAAQ,GAAGhB,KAAK,CAACgB,QAAQ;IACzBC,YAAY,GAAGjB,KAAK,CAACiB,YAAY;IACjCC,SAAS,GAAGlB,KAAK,CAACkB,SAAS;IAC3BC,YAAY,GAAGnB,KAAK,CAACmB,YAAY;IACjCC,gBAAgB,GAAGpB,KAAK,CAACqB,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEE,aAAa,GAAGtB,KAAK,CAACsB,aAAa;IACnCC,MAAM,GAAGvB,KAAK,CAACuB,MAAM;IACrBjC,QAAQ,GAAGU,KAAK,CAACV,QAAQ;IACzBkC,YAAY,GAAGxB,KAAK,CAACwB,YAAY;IACjCC,mBAAmB,GAAGzB,KAAK,CAACyB,mBAAmB;IAC/CC,OAAO,GAAG1B,KAAK,CAAC0B,OAAO;IACvBC,UAAU,GAAG3B,KAAK,CAAC2B,UAAU;IAC7BC,QAAQ,GAAG5B,KAAK,CAAC4B,QAAQ;IACzBC,wBAAwB,GAAG7B,KAAK,CAAC6B,wBAAwB;IACzDC,+BAA+B,GAAG9B,KAAK,CAAC8B,+BAA+B;IACvEC,cAAc,GAAG/B,KAAK,CAAC+B,cAAc;IACrCC,gBAAgB,GAAGhC,KAAK,CAACgC,gBAAgB;IACzCC,eAAe,GAAGjC,KAAK,CAACiC,eAAe;IACvCC,iBAAiB,GAAGlC,KAAK,CAACkC,iBAAiB;IAC3CC,kBAAkB,GAAGnC,KAAK,CAACmC,kBAAkB;IAC7CC,gBAAgB,GAAGpC,KAAK,CAACoC,gBAAgB;IACzCC,oBAAoB,GAAGrC,KAAK,CAACqC,oBAAoB;IACjDC,6BAA6B,GAAGtC,KAAK,CAACsC,6BAA6B;IACnEC,0BAA0B,GAAGvC,KAAK,CAACuC,0BAA0B;IAC7DC,iCAAiC,GAAGxC,KAAK,CAACwC,iCAAiC;IAC3EC,iBAAiB,GAAGzC,KAAK,CAACyC,iBAAiB;IAC3CC,mCAAmC,GAAG1C,KAAK,CAAC0C,mCAAmC;IAC/EC,0CAA0C,GAAG3C,KAAK,CAAC2C,0CAA0C;IAC7FC,yBAAyB,GAAG5C,KAAK,CAAC4C,yBAAyB;IAC3DC,gCAAgC,GAAG7C,KAAK,CAAC6C,gCAAgC;IACzEC,eAAe,GAAG9C,KAAK,CAAC8C,eAAe;IACvCC,oBAAoB,GAAG/C,KAAK,CAAC+C,oBAAoB;IACjDC,eAAe,GAAGhD,KAAK,CAACN,QAAQ;IAChCA,QAAQ,GAAGsD,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,eAAe;IAC/DC,aAAa,GAAGjD,KAAK,CAACiD,aAAa;IACnCtD,IAAI,GAAGK,KAAK,CAACL,IAAI;IACjBuD,qBAAqB,GAAGlD,KAAK,CAACmD,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IAClFvF,SAAS,GAAGqC,KAAK,CAACrC,SAAS;IAC3ByF,iBAAiB,GAAGpD,KAAK,CAACoD,iBAAiB;IAC3CC,oBAAoB,GAAGrD,KAAK,CAACqD,oBAAoB;IACjDzD,UAAU,GAAGI,KAAK,CAACJ,UAAU;EAC/B,IAAIE,IAAI;EACR,IAAIwD,UAAU;EACd,IAAIpC,SAAS,IAAI,IAAI,EAAE;IACrBoC,UAAU,GAAGpC,SAAS;IACtB,IAAIA,SAAS,KAAK,OAAO,EAAE;MACzBpB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAIoB,SAAS,KAAK,KAAK,EAAE;MAC9BpB,IAAI,GAAG,KAAK;IACd,CAAC,MAAM,IAAIoB,SAAS,KAAK,QAAQ,EAAE;MACjCpB,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAIoB,SAAS,KAAK,KAAK,EAAE;MAC9BpB,IAAI,GAAG,KAAK;IACd,CAAC,MAAM;MACLA,IAAI,GAAG,MAAM;IACf;EACF,CAAC,MAAM,IAAIqB,YAAY,IAAI,IAAI,EAAE;IAE/B,QAAQA,YAAY;MAClB,KAAK,eAAe;QAClBrB,IAAI,GAAG,OAAO;QACd;MACF,KAAK,YAAY;MACjB,KAAK,SAAS;QACZwD,UAAU,GAAG,SAAS;QACtB;MACF,KAAK,aAAa;QAChBA,UAAU,GAAG,SAAS;QACtB;MACF,KAAK,WAAW;QACdxD,IAAI,GAAG,KAAK;QACZ;MACF,KAAK,QAAQ;MACb,KAAK,YAAY;QACfA,IAAI,GAAG,QAAQ;QACf;MACF,KAAK,KAAK;QACRA,IAAI,GAAG,KAAK;QACZ;MACF;QACEA,IAAI,GAAG,MAAM;IACjB;EACF;EACA,IAAIqD,eAAe,EAAE;IACnBrD,IAAI,GAAG,UAAU;EACnB;EACA,IAAIyD,UAAU,GAAG3G,KAAK,CAAC4G,MAAM,CAAC;IAC5BC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAIC,OAAO,GAAG/G,KAAK,CAAC4G,MAAM,CAAC,IAAI,CAAC;EAChC,IAAII,aAAa,GAAGhH,KAAK,CAAC4G,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIK,mBAAmB,GAAGjH,KAAK,CAAC4G,MAAM,CAAC,KAAK,CAAC;EAC7C5G,KAAK,CAACkH,SAAS,CAAC,YAAM;IACpB,IAAIH,OAAO,CAACI,OAAO,IAAIH,aAAa,CAACG,OAAO,EAAE;MAC5C/F,YAAY,CAAC2F,OAAO,CAACI,OAAO,EAAEH,aAAa,CAACG,OAAO,CAAC;IACtD;IACAF,mBAAmB,CAACE,OAAO,GAAGZ,eAAe;EAC/C,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EACrB,IAAIa,uBAAuB,GAAGpH,KAAK,CAACqH,WAAW,CAAC,UAAAC,QAAQ,EAAI;IAC1D,IAAI7C,SAAS,IAAII,mBAAmB,IAAIyC,QAAQ,IAAI,IAAI,EAAE;MACxD,IAAIC,SAAS,GAAGD,QAAQ,CAACE,YAAY;MACrC,IAAIC,QAAQ,GAAGH,QAAQ,CAACI,WAAW;MACnC,IAAIH,SAAS,KAAKZ,UAAU,CAACQ,OAAO,CAACN,MAAM,IAAIY,QAAQ,KAAKd,UAAU,CAACQ,OAAO,CAACL,KAAK,EAAE;QACpFH,UAAU,CAACQ,OAAO,CAACN,MAAM,GAAGU,SAAS;QACrCZ,UAAU,CAACQ,OAAO,CAACL,KAAK,GAAGW,QAAQ;QACnC5C,mBAAmB,CAAC;UAClBvB,WAAW,EAAE;YACXqE,WAAW,EAAE;cACXd,MAAM,EAAEF,UAAU,CAACQ,OAAO,CAACN,MAAM;cACjCC,KAAK,EAAEH,UAAU,CAACQ,OAAO,CAACL;YAC5B;UACF;QACF,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE,CAACrC,SAAS,EAAEI,mBAAmB,CAAC,CAAC;EACpC,IAAI+C,aAAa,GAAG5H,KAAK,CAAC6H,OAAO,CAAC;IAAA,OAAM,UAAAP,QAAQ,EAAI;MAIlD,IAAIA,QAAQ,IAAI,IAAI,EAAE;QACpBA,QAAQ,CAACQ,KAAK,GAAG,YAAY;UAC3B,IAAIR,QAAQ,IAAI,IAAI,EAAE;YACpBA,QAAQ,CAACrE,KAAK,GAAG,EAAE;UACrB;QACF,CAAC;QACDqE,QAAQ,CAACS,SAAS,GAAG,YAAY;UAC/B,OAAOT,QAAQ,IAAI,IAAI,IAAI1G,cAAc,CAACoH,qBAAqB,CAAC,CAAC,KAAKV,QAAQ;QAChF,CAAC;QACDF,uBAAuB,CAACE,QAAQ,CAAC;MACnC;IACF,CAAC;EAAA,GAAE,CAACF,uBAAuB,CAAC,CAAC;EAC7B,SAASa,UAAUA,CAAC3G,CAAC,EAAE;IACrBV,cAAc,CAACsH,qBAAqB,GAAG,IAAI;IAC3C,IAAIvD,MAAM,EAAE;MACVrD,CAAC,CAACgC,WAAW,CAAC6E,IAAI,GAAG7G,CAAC,CAAC8G,MAAM,CAACnF,KAAK;MACnC0B,MAAM,CAACrD,CAAC,CAAC;IACX;EACF;EACA,SAAS+G,YAAYA,CAAC/G,CAAC,EAAE;IACvB,IAAIgG,QAAQ,GAAGhG,CAAC,CAAC8G,MAAM;IACvB,IAAID,IAAI,GAAGb,QAAQ,CAACrE,KAAK;IACzB3B,CAAC,CAACgC,WAAW,CAAC6E,IAAI,GAAGA,IAAI;IACzBf,uBAAuB,CAACE,QAAQ,CAAC;IACjC,IAAI5E,QAAQ,EAAE;MACZA,QAAQ,CAACpB,CAAC,CAAC;IACb;IACA,IAAIsD,YAAY,EAAE;MAChBA,YAAY,CAACuD,IAAI,CAAC;IACpB;EACF;EACA,SAASG,WAAWA,CAAChH,CAAC,EAAE;IACtB,IAAIgG,QAAQ,GAAGhG,CAAC,CAAC8G,MAAM;IACvB,IAAItD,OAAO,EAAE;MACXxD,CAAC,CAACgC,WAAW,CAAC6E,IAAI,GAAGb,QAAQ,CAACrE,KAAK;MACnC6B,OAAO,CAACxD,CAAC,CAAC;IACZ;IACA,IAAIgG,QAAQ,IAAI,IAAI,EAAE;MACpB1G,cAAc,CAACsH,qBAAqB,GAAGZ,QAAQ;MAC/C,IAAIpD,gBAAgB,EAAE;QACpBoD,QAAQ,CAACrE,KAAK,GAAG,EAAE;MACrB;MACA,IAAIuD,iBAAiB,EAAE;QAErB,IAAI/C,YAAY,IAAI,IAAI,EAAE;UACxB8E,YAAY,CAAC9E,YAAY,CAAC;QAC5B;QACAA,YAAY,GAAG+E,UAAU,CAAC,YAAM;UAG9B,IAAIlB,QAAQ,IAAI,IAAI,IAAImB,QAAQ,CAACC,aAAa,KAAKpB,QAAQ,EAAE;YAC3DA,QAAQ,CAACqB,MAAM,CAAC,CAAC;UACnB;QACF,CAAC,EAAE,CAAC,CAAC;MACP;IACF;EACF;EACA,SAASC,aAAaA,CAACtH,CAAC,EAAE;IACxB,IAAIgG,QAAQ,GAAGhG,CAAC,CAAC8G,MAAM;IAEvB9G,CAAC,CAACuH,eAAe,CAAC,CAAC;IACnB,IAAIC,mBAAmB,GAAG,CAACrE,SAAS;IACpC,IAAIsE,kBAAkB,GAAG/E,YAAY,IAAI,IAAI,GAAG8E,mBAAmB,GAAG9E,YAAY;IAClF,IAAIV,WAAW,GAAGhC,CAAC,CAACgC,WAAW;IAC/B,IAAIC,WAAW,GAAGF,gBAAgB,CAACC,WAAW,CAAC;IAC/C,IAAIyB,UAAU,EAAE;MACdA,UAAU,CAACzD,CAAC,CAAC;IACf;IACA,IAAIA,CAAC,CAAC0H,GAAG,KAAK,OAAO,IAAI,CAAC1H,CAAC,CAAC2H,QAAQ,IAEpC,CAAC1F,WAAW,IAAI,CAACjC,CAAC,CAAC4H,kBAAkB,CAAC,CAAC,EAAE;MACvC,IAAI,CAAClF,YAAY,IAAI,CAACS,SAAS,KAAKyB,eAAe,EAAE;QAEnD5E,CAAC,CAAC6H,cAAc,CAAC,CAAC;QAClB7F,WAAW,CAAC6E,IAAI,GAAG7G,CAAC,CAAC8G,MAAM,CAACnF,KAAK;QACjCiD,eAAe,CAAC5E,CAAC,CAAC;MACpB;MACA,IAAIyH,kBAAkB,IAAIzB,QAAQ,IAAI,IAAI,EAAE;QAC1CkB,UAAU,CAAC;UAAA,OAAMlB,QAAQ,CAAC8B,IAAI,CAAC,CAAC;QAAA,GAAE,CAAC,CAAC;MACtC;IACF;EACF;EACA,SAASC,qBAAqBA,CAAC/H,CAAC,EAAE;IAChC,IAAI;MACF,IAAIgI,SAAS,GAAGhI,CAAC,CAAC8G,MAAM;QACtBnH,cAAc,GAAGqI,SAAS,CAACrI,cAAc;QACzCD,YAAY,GAAGsI,SAAS,CAACtI,YAAY;MACvC,IAAIuI,UAAU,GAAG;QACfrI,KAAK,EAAED,cAAc;QACrBE,GAAG,EAAEH;MACP,CAAC;MACD,IAAI6E,iBAAiB,EAAE;QACrBvE,CAAC,CAACgC,WAAW,CAACvC,SAAS,GAAGwI,UAAU;QACpCjI,CAAC,CAACgC,WAAW,CAAC6E,IAAI,GAAG7G,CAAC,CAAC8G,MAAM,CAACnF,KAAK;QACnC4C,iBAAiB,CAACvE,CAAC,CAAC;MACtB;MACA,IAAI2F,mBAAmB,CAACE,OAAO,KAAKZ,eAAe,EAAE;QACnDS,aAAa,CAACG,OAAO,GAAGoC,UAAU;MACpC;IACF,CAAC,CAAC,OAAOjI,CAAC,EAAE,CAAC;EACf;EACAjB,eAAe,CAAC,YAAM;IACpB,IAAIS,IAAI,GAAGiG,OAAO,CAACI,OAAO;IAC1B,IAAIrG,IAAI,IAAI,IAAI,IAAIC,SAAS,IAAI,IAAI,EAAE;MACrCK,YAAY,CAACN,IAAI,EAAEC,SAAS,CAAC;IAC/B;IACA,IAAI0H,QAAQ,CAACC,aAAa,KAAK5H,IAAI,EAAE;MACnCF,cAAc,CAACsH,qBAAqB,GAAGpH,IAAI;IAC7C;EACF,CAAC,EAAE,CAACiG,OAAO,EAAEhG,SAAS,CAAC,CAAC;EACxB,IAAIyI,SAAS,GAAG/E,SAAS,GAAG,UAAU,GAAG,OAAO;EAChDrE,gBAAgB,CAAC2G,OAAO,EAAE/B,QAAQ,CAAC;EACnCxE,kBAAkB,CAACuG,OAAO,EAAE;IAC1B9B,wBAAwB,EAAxBA,wBAAwB;IACxBC,+BAA+B,EAA/BA,+BAA+B;IAC/BC,cAAc,EAAdA,cAAc;IACdC,gBAAgB,EAAhBA,gBAAgB;IAChBC,eAAe,EAAfA,eAAe;IACfC,iBAAiB,EAAjBA,iBAAiB;IACjBC,kBAAkB,EAAlBA,kBAAkB;IAClBC,gBAAgB,EAAhBA,gBAAgB;IAChBC,oBAAoB,EAApBA,oBAAoB;IACpBC,6BAA6B,EAA7BA,6BAA6B;IAC7BC,0BAA0B,EAA1BA,0BAA0B;IAC1BC,iCAAiC,EAAjCA,iCAAiC;IACjCE,mCAAmC,EAAnCA,mCAAmC;IACnCC,0CAA0C,EAA1CA,0CAA0C;IAC1CC,yBAAyB,EAAzBA,yBAAyB;IACzBC,gCAAgC,EAAhCA;EACF,CAAC,CAAC;EACF,IAAIwD,iBAAiB,GAAG/I,gBAAgB,CAAC,CAAC;IACxCgJ,gBAAgB,GAAGD,iBAAiB,CAACE,SAAS;EAChD,IAAIC,cAAc,GAAGzG,SAAS,CAACC,KAAK,CAAC;EACrCwG,cAAc,CAAC1H,cAAc,GAAGA,cAAc;EAC9C0H,cAAc,CAACzH,YAAY,GAAGA,YAAY,IAAI2B,gBAAgB,IAAI,IAAI;EACtE8F,cAAc,CAACxH,WAAW,GAAGA,WAAW,GAAG,IAAI,GAAG,KAAK;EAEvDwH,cAAc,CAACzF,GAAG,GAAGA,GAAG,KAAK0F,SAAS,GAAG1F,GAAG,GAAG,MAAM;EAMrDyF,cAAc,CAACvF,YAAY,GAAGA,YAAY,IAAIgC,aAAa;EAC3DuD,cAAc,CAACtF,SAAS,GAAGoC,UAAU;EACrCkD,cAAc,CAACjF,MAAM,GAAGsD,UAAU;EAClC2B,cAAc,CAAClH,QAAQ,GAAG2F,YAAY;EACtCuB,cAAc,CAAC9E,OAAO,GAAGwD,WAAW;EACpCsB,cAAc,CAACE,SAAS,GAAGlB,aAAa;EACxCgB,cAAc,CAACG,QAAQ,GAAGV,qBAAqB;EAM/CO,cAAc,CAAC9G,QAAQ,GAAGA,QAAQ,KAAK,IAAI,IAAIsB,QAAQ,KAAK,KAAK;EASjEwF,cAAc,CAAC7G,IAAI,GAAG0B,SAAS,GAAG1B,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG2B,aAAa,GAAG,CAAC;EACzEkF,cAAc,CAAC5G,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGZ,WAAW;EACzEwH,cAAc,CAACI,KAAK,GAAG,CAAC;IACtB,wBAAwB,EAAE7D;EAC5B,CAAC,EAAE8D,MAAM,CAACC,aAAa,EAAED,MAAM,CAACrH,WAAW,EAAEQ,KAAK,CAAC4G,KAAK,EAAE/F,WAAW,IAAIgG,MAAM,CAAChG,WAAW,CAAC;EAC5F2F,cAAc,CAAC1G,IAAI,GAAGuB,SAAS,GAAGoF,SAAS,GAAG3G,IAAI;EAClD0G,cAAc,CAACO,qBAAqB,GAAG1D,oBAAoB,KAAK,KAAK,GAAG,QAAQ,GAAG,MAAM;EACzF,IAAI2D,kBAAkB,GAAG7J,kBAAkB,CAACqJ,cAAc,CAAC;EAC3D,IAAIS,MAAM,GAAG/J,YAAY,CAACyG,OAAO,EAAEqD,kBAAkB,EAAExC,aAAa,EAAEhE,YAAY,CAAC;EACnFgG,cAAc,CAACU,GAAG,GAAGD,MAAM;EAC3B,IAAIE,aAAa,GAAGnH,KAAK,CAACZ,IAAI,IAAI,IAAI,GAAG/B,kBAAkB,CAAC2C,KAAK,CAACZ,IAAI,CAAC,GAAG,IAAI;EAC9E,IAAIgI,kBAAkB,GAAGpH,KAAK,CAACe,GAAG,IAAIoG,aAAa;EACnD,IAAIE,gBAAgB,GAAGD,kBAAkB,IAAId,gBAAgB;EAC7D,IAAIgB,OAAO,GAAGzK,aAAa,CAACuJ,SAAS,EAAEI,cAAc,EAAE;IACrDa,gBAAgB,EAAhBA;EACF,CAAC,CAAC;EACF,OAAOC,OAAO;AAChB,CAAC,CAAC;AACFhH,SAAS,CAACiH,WAAW,GAAG,WAAW;AAEnCjH,SAAS,CAACkH,KAAK,GAAGhK,cAAc;AAChC,IAAIqJ,MAAM,GAAGtJ,UAAU,CAACkK,MAAM,CAAC;EAC7BX,aAAa,EAAE;IACbY,aAAa,EAAE,WAAW;IAC1BC,gBAAgB,EAAE,MAAM;IACxBC,eAAe,EAAE,aAAa;IAC9BC,MAAM,EAAE,eAAe;IACvBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,YAAY;IACvBC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACVC,MAAM,EAAE;EACV,CAAC;EACD3I,WAAW,EAAE;IACXuD,oBAAoB,EAAE;EACxB,CAAC;EACDlC,WAAW,EAAE;IACXuH,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AACF,eAAe9H,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}