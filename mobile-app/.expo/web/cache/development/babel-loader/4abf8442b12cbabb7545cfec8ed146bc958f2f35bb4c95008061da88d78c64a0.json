{"ast": null, "code": "import PooledClass from \"../../vendor/react-native/PooledClass\";\nvar twoArgumentPooler = PooledClass.twoArgumentPooler;\nfunction BoundingDimensions(width, height) {\n  this.width = width;\n  this.height = height;\n}\nBoundingDimensions.prototype.destructor = function () {\n  this.width = null;\n  this.height = null;\n};\nBoundingDimensions.getPooledFromElement = function (element) {\n  return BoundingDimensions.getPooled(element.offsetWidth, element.offsetHeight);\n};\nPooledClass.addPoolingTo(BoundingDimensions, twoArgumentPooler);\nexport default BoundingDimensions;", "map": {"version": 3, "names": ["PooledClass", "twoArgumentPooler", "BoundingDimensions", "width", "height", "prototype", "destructor", "getPooledFromElement", "element", "getPooled", "offsetWidth", "offsetHeight", "addPoolingTo"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/Touchable/BoundingDimensions.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport PooledClass from '../../vendor/react-native/PooledClass';\nvar twoArgumentPooler = PooledClass.twoArgumentPooler;\n\n/**\n * PooledClass representing the bounding rectangle of a region.\n */\nfunction BoundingDimensions(width, height) {\n  this.width = width;\n  this.height = height;\n}\nBoundingDimensions.prototype.destructor = function () {\n  this.width = null;\n  this.height = null;\n};\nBoundingDimensions.getPooledFromElement = function (element) {\n  return BoundingDimensions.getPooled(element.offsetWidth, element.offsetHeight);\n};\nPooledClass.addPoolingTo(BoundingDimensions, twoArgumentPooler);\nexport default BoundingDimensions;"], "mappings": "AAUA,OAAOA,WAAW;AAClB,IAAIC,iBAAiB,GAAGD,WAAW,CAACC,iBAAiB;AAKrD,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzC,IAAI,CAACD,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;AACtB;AACAF,kBAAkB,CAACG,SAAS,CAACC,UAAU,GAAG,YAAY;EACpD,IAAI,CAACH,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,MAAM,GAAG,IAAI;AACpB,CAAC;AACDF,kBAAkB,CAACK,oBAAoB,GAAG,UAAUC,OAAO,EAAE;EAC3D,OAAON,kBAAkB,CAACO,SAAS,CAACD,OAAO,CAACE,WAAW,EAAEF,OAAO,CAACG,YAAY,CAAC;AAChF,CAAC;AACDX,WAAW,CAACY,YAAY,CAACV,kBAAkB,EAAED,iBAAiB,CAAC;AAC/D,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}