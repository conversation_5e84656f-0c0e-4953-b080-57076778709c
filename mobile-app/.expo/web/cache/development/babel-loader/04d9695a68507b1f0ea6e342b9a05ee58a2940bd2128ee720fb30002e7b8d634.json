{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"writingDirection\"];\nimport { atomic, classic, inline } from \"./compiler\";\nimport { createSheet } from \"./dom\";\nimport { localizeStyle } from 'styleq/transform-localize-style';\nimport { preprocess } from \"./preprocess\";\nimport { styleq } from 'styleq';\nimport { validate } from \"./validate\";\nimport canUseDOM from \"../../modules/canUseDom\";\nvar staticStyleMap = new WeakMap();\nvar sheet = createSheet();\nvar defaultPreprocessOptions = {\n  shadow: true,\n  textShadow: true\n};\nfunction customStyleq(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    writingDirection = _options.writingDirection,\n    preprocessOptions = _objectWithoutPropertiesLoose(_options, _excluded);\n  var isRTL = writingDirection === 'rtl';\n  return styleq.factory({\n    transform: function transform(style) {\n      var compiledStyle = staticStyleMap.get(style);\n      if (compiledStyle != null) {\n        return localizeStyle(compiledStyle, isRTL);\n      }\n      return preprocess(style, _objectSpread(_objectSpread({}, defaultPreprocessOptions), preprocessOptions));\n    }\n  })(styles);\n}\nfunction insertRules(compiledOrderedRules) {\n  compiledOrderedRules.forEach(function (_ref) {\n    var rules = _ref[0],\n      order = _ref[1];\n    if (sheet != null) {\n      rules.forEach(function (rule) {\n        sheet.insert(rule, order);\n      });\n    }\n  });\n}\nfunction compileAndInsertAtomic(style) {\n  var _atomic = atomic(preprocess(style, defaultPreprocessOptions)),\n    compiledStyle = _atomic[0],\n    compiledOrderedRules = _atomic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\nfunction compileAndInsertReset(style, key) {\n  var _classic = classic(style, key),\n    compiledStyle = _classic[0],\n    compiledOrderedRules = _classic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\nvar absoluteFillObject = {\n  position: 'absolute',\n  left: 0,\n  right: 0,\n  top: 0,\n  bottom: 0\n};\nvar absoluteFill = create({\n  x: _objectSpread({}, absoluteFillObject)\n}).x;\nfunction create(styles) {\n  Object.keys(styles).forEach(function (key) {\n    var styleObj = styles[key];\n    if (styleObj != null && styleObj.$$css !== true) {\n      var compiledStyles;\n      if (key.indexOf('$raw') > -1) {\n        compiledStyles = compileAndInsertReset(styleObj, key.split('$raw')[0]);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          validate(styleObj);\n          styles[key] = Object.freeze(styleObj);\n        }\n        compiledStyles = compileAndInsertAtomic(styleObj);\n      }\n      staticStyleMap.set(styleObj, compiledStyles);\n    }\n  });\n  return styles;\n}\nfunction compose(style1, style2) {\n  if (process.env.NODE_ENV !== 'production') {\n    var len = arguments.length;\n    if (len > 2) {\n      var readableStyles = Array.prototype.slice.call(arguments).map(function (a) {\n        return flatten(a);\n      });\n      throw new Error(\"StyleSheet.compose() only accepts 2 arguments, received \" + len + \": \" + JSON.stringify(readableStyles));\n    }\n  }\n  return [style1, style2];\n}\nfunction flatten() {\n  for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {\n    styles[_key] = arguments[_key];\n  }\n  var flatArray = styles.flat(Infinity);\n  var result = {};\n  for (var i = 0; i < flatArray.length; i++) {\n    var style = flatArray[i];\n    if (style != null && typeof style === 'object') {\n      Object.assign(result, style);\n    }\n  }\n  return result;\n}\nfunction getSheet() {\n  return {\n    id: sheet.id,\n    textContent: sheet.getTextContent()\n  };\n}\nfunction StyleSheet(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var isRTL = options.writingDirection === 'rtl';\n  var styleProps = customStyleq(styles, options);\n  if (Array.isArray(styleProps) && styleProps[1] != null) {\n    styleProps[1] = inline(styleProps[1], isRTL);\n  }\n  return styleProps;\n}\nStyleSheet.absoluteFill = absoluteFill;\nStyleSheet.absoluteFillObject = absoluteFillObject;\nStyleSheet.create = create;\nStyleSheet.compose = compose;\nStyleSheet.flatten = flatten;\nStyleSheet.getSheet = getSheet;\nStyleSheet.hairlineWidth = 1;\nif (canUseDOM && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {\n  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.resolveRNStyle = StyleSheet.flatten;\n}\nvar stylesheet = StyleSheet;\nexport default stylesheet;", "map": {"version": 3, "names": ["_objectSpread", "_objectWithoutPropertiesLoose", "_excluded", "atomic", "classic", "inline", "createSheet", "localizeStyle", "preprocess", "styleq", "validate", "canUseDOM", "staticStyleMap", "WeakMap", "sheet", "defaultPreprocessOptions", "shadow", "textShadow", "customStyleq", "styles", "options", "_options", "writingDirection", "preprocessOptions", "isRTL", "factory", "transform", "style", "compiledStyle", "get", "insertRules", "compiledOrderedRules", "for<PERSON>ach", "_ref", "rules", "order", "rule", "insert", "compileAndInsertAtomic", "_atomic", "compileAndInsertReset", "key", "_classic", "absoluteFillObject", "position", "left", "right", "top", "bottom", "absoluteFill", "create", "x", "Object", "keys", "styleObj", "$$css", "compiledStyles", "indexOf", "split", "process", "env", "NODE_ENV", "freeze", "set", "compose", "style1", "style2", "len", "arguments", "length", "readableStyles", "Array", "prototype", "slice", "call", "map", "a", "flatten", "Error", "JSON", "stringify", "_len", "_key", "flatArray", "flat", "Infinity", "result", "i", "assign", "getSheet", "id", "textContent", "getTextContent", "StyleSheet", "styleProps", "isArray", "hairlineWidth", "window", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "resolveRNStyle", "stylesheet"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/StyleSheet/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"writingDirection\"];\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { atomic, classic, inline } from './compiler';\nimport { createSheet } from './dom';\nimport { localizeStyle } from 'styleq/transform-localize-style';\nimport { preprocess } from './preprocess';\nimport { styleq } from 'styleq';\nimport { validate } from './validate';\nimport canUseDOM from '../../modules/canUseDom';\nvar staticStyleMap = new WeakMap();\nvar sheet = createSheet();\nvar defaultPreprocessOptions = {\n  shadow: true,\n  textShadow: true\n};\nfunction customStyleq(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    writingDirection = _options.writingDirection,\n    preprocessOptions = _objectWithoutPropertiesLoose(_options, _excluded);\n  var isRTL = writingDirection === 'rtl';\n  return styleq.factory({\n    transform(style) {\n      var compiledStyle = staticStyleMap.get(style);\n      if (compiledStyle != null) {\n        return localizeStyle(compiledStyle, isRTL);\n      }\n      return preprocess(style, _objectSpread(_objectSpread({}, defaultPreprocessOptions), preprocessOptions));\n    }\n  })(styles);\n}\nfunction insertRules(compiledOrderedRules) {\n  compiledOrderedRules.forEach(_ref => {\n    var rules = _ref[0],\n      order = _ref[1];\n    if (sheet != null) {\n      rules.forEach(rule => {\n        sheet.insert(rule, order);\n      });\n    }\n  });\n}\nfunction compileAndInsertAtomic(style) {\n  var _atomic = atomic(preprocess(style, defaultPreprocessOptions)),\n    compiledStyle = _atomic[0],\n    compiledOrderedRules = _atomic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\nfunction compileAndInsertReset(style, key) {\n  var _classic = classic(style, key),\n    compiledStyle = _classic[0],\n    compiledOrderedRules = _classic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\n\n/* ----- API ----- */\n\nvar absoluteFillObject = {\n  position: 'absolute',\n  left: 0,\n  right: 0,\n  top: 0,\n  bottom: 0\n};\nvar absoluteFill = create({\n  x: _objectSpread({}, absoluteFillObject)\n}).x;\n\n/**\n * create\n */\nfunction create(styles) {\n  Object.keys(styles).forEach(key => {\n    var styleObj = styles[key];\n    // Only compile at runtime if the style is not already compiled\n    if (styleObj != null && styleObj.$$css !== true) {\n      var compiledStyles;\n      if (key.indexOf('$raw') > -1) {\n        compiledStyles = compileAndInsertReset(styleObj, key.split('$raw')[0]);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          validate(styleObj);\n          styles[key] = Object.freeze(styleObj);\n        }\n        compiledStyles = compileAndInsertAtomic(styleObj);\n      }\n      staticStyleMap.set(styleObj, compiledStyles);\n    }\n  });\n  return styles;\n}\n\n/**\n * compose\n */\nfunction compose(style1, style2) {\n  if (process.env.NODE_ENV !== 'production') {\n    /* eslint-disable prefer-rest-params */\n    var len = arguments.length;\n    if (len > 2) {\n      var readableStyles = [...arguments].map(a => flatten(a));\n      throw new Error(\"StyleSheet.compose() only accepts 2 arguments, received \" + len + \": \" + JSON.stringify(readableStyles));\n    }\n    /* eslint-enable prefer-rest-params */\n    /*\n    console.warn(\n      'StyleSheet.compose(a, b) is deprecated; use array syntax, i.e., [a,b].'\n    );\n    */\n  }\n  return [style1, style2];\n}\n\n/**\n * flatten\n */\nfunction flatten() {\n  for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {\n    styles[_key] = arguments[_key];\n  }\n  var flatArray = styles.flat(Infinity);\n  var result = {};\n  for (var i = 0; i < flatArray.length; i++) {\n    var style = flatArray[i];\n    if (style != null && typeof style === 'object') {\n      // $FlowFixMe\n      Object.assign(result, style);\n    }\n  }\n  return result;\n}\n\n/**\n * getSheet\n */\nfunction getSheet() {\n  return {\n    id: sheet.id,\n    textContent: sheet.getTextContent()\n  };\n}\n\n/**\n * resolve\n */\n\nfunction StyleSheet(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var isRTL = options.writingDirection === 'rtl';\n  var styleProps = customStyleq(styles, options);\n  if (Array.isArray(styleProps) && styleProps[1] != null) {\n    styleProps[1] = inline(styleProps[1], isRTL);\n  }\n  return styleProps;\n}\nStyleSheet.absoluteFill = absoluteFill;\nStyleSheet.absoluteFillObject = absoluteFillObject;\nStyleSheet.create = create;\nStyleSheet.compose = compose;\nStyleSheet.flatten = flatten;\nStyleSheet.getSheet = getSheet;\n// `hairlineWidth` is not implemented using screen density as browsers may\n// round sub-pixel values down to `0`, causing the line not to be rendered.\nStyleSheet.hairlineWidth = 1;\nif (canUseDOM && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {\n  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.resolveRNStyle = StyleSheet.flatten;\n}\nvar stylesheet = StyleSheet;\nexport default stylesheet;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,kBAAkB,CAAC;AAUpC,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM;AAChC,SAASC,WAAW;AACpB,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,UAAU;AACnB,SAASC,MAAM,QAAQ,QAAQ;AAC/B,SAASC,QAAQ;AACjB,OAAOC,SAAS;AAChB,IAAIC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AAClC,IAAIC,KAAK,GAAGR,WAAW,CAAC,CAAC;AACzB,IAAIS,wBAAwB,GAAG;EAC7BC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE;AACd,CAAC;AACD,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACrC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,QAAQ,GAAGD,OAAO;IACpBE,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB;IAC5CC,iBAAiB,GAAGtB,6BAA6B,CAACoB,QAAQ,EAAEnB,SAAS,CAAC;EACxE,IAAIsB,KAAK,GAAGF,gBAAgB,KAAK,KAAK;EACtC,OAAOb,MAAM,CAACgB,OAAO,CAAC;IACpBC,SAAS,WAATA,SAASA,CAACC,KAAK,EAAE;MACf,IAAIC,aAAa,GAAGhB,cAAc,CAACiB,GAAG,CAACF,KAAK,CAAC;MAC7C,IAAIC,aAAa,IAAI,IAAI,EAAE;QACzB,OAAOrB,aAAa,CAACqB,aAAa,EAAEJ,KAAK,CAAC;MAC5C;MACA,OAAOhB,UAAU,CAACmB,KAAK,EAAE3B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,wBAAwB,CAAC,EAAEQ,iBAAiB,CAAC,CAAC;IACzG;EACF,CAAC,CAAC,CAACJ,MAAM,CAAC;AACZ;AACA,SAASW,WAAWA,CAACC,oBAAoB,EAAE;EACzCA,oBAAoB,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;IACnC,IAAIC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;MACjBE,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACjB,IAAInB,KAAK,IAAI,IAAI,EAAE;MACjBoB,KAAK,CAACF,OAAO,CAAC,UAAAI,IAAI,EAAI;QACpBtB,KAAK,CAACuB,MAAM,CAACD,IAAI,EAAED,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AACA,SAASG,sBAAsBA,CAACX,KAAK,EAAE;EACrC,IAAIY,OAAO,GAAGpC,MAAM,CAACK,UAAU,CAACmB,KAAK,EAAEZ,wBAAwB,CAAC,CAAC;IAC/Da,aAAa,GAAGW,OAAO,CAAC,CAAC,CAAC;IAC1BR,oBAAoB,GAAGQ,OAAO,CAAC,CAAC,CAAC;EACnCT,WAAW,CAACC,oBAAoB,CAAC;EACjC,OAAOH,aAAa;AACtB;AACA,SAASY,qBAAqBA,CAACb,KAAK,EAAEc,GAAG,EAAE;EACzC,IAAIC,QAAQ,GAAGtC,OAAO,CAACuB,KAAK,EAAEc,GAAG,CAAC;IAChCb,aAAa,GAAGc,QAAQ,CAAC,CAAC,CAAC;IAC3BX,oBAAoB,GAAGW,QAAQ,CAAC,CAAC,CAAC;EACpCZ,WAAW,CAACC,oBAAoB,CAAC;EACjC,OAAOH,aAAa;AACtB;AAIA,IAAIe,kBAAkB,GAAG;EACvBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,YAAY,GAAGC,MAAM,CAAC;EACxBC,CAAC,EAAEnD,aAAa,CAAC,CAAC,CAAC,EAAE2C,kBAAkB;AACzC,CAAC,CAAC,CAACQ,CAAC;AAKJ,SAASD,MAAMA,CAAC/B,MAAM,EAAE;EACtBiC,MAAM,CAACC,IAAI,CAAClC,MAAM,CAAC,CAACa,OAAO,CAAC,UAAAS,GAAG,EAAI;IACjC,IAAIa,QAAQ,GAAGnC,MAAM,CAACsB,GAAG,CAAC;IAE1B,IAAIa,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACC,KAAK,KAAK,IAAI,EAAE;MAC/C,IAAIC,cAAc;MAClB,IAAIf,GAAG,CAACgB,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5BD,cAAc,GAAGhB,qBAAqB,CAACc,QAAQ,EAAEb,GAAG,CAACiB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,MAAM;QACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCnD,QAAQ,CAAC4C,QAAQ,CAAC;UAClBnC,MAAM,CAACsB,GAAG,CAAC,GAAGW,MAAM,CAACU,MAAM,CAACR,QAAQ,CAAC;QACvC;QACAE,cAAc,GAAGlB,sBAAsB,CAACgB,QAAQ,CAAC;MACnD;MACA1C,cAAc,CAACmD,GAAG,CAACT,QAAQ,EAAEE,cAAc,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,OAAOrC,MAAM;AACf;AAKA,SAAS6C,OAAOA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAEzC,IAAIM,GAAG,GAAGC,SAAS,CAACC,MAAM;IAC1B,IAAIF,GAAG,GAAG,CAAC,EAAE;MACX,IAAIG,cAAc,GAAGC,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,IAAA,CAAIN,SAAS,EAAEO,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIC,OAAO,CAACD,CAAC,CAAC;MAAA,EAAC;MACxD,MAAM,IAAIE,KAAK,CAAC,0DAA0D,GAAGX,GAAG,GAAG,IAAI,GAAGY,IAAI,CAACC,SAAS,CAACV,cAAc,CAAC,CAAC;IAC3H;EAOF;EACA,OAAO,CAACL,MAAM,EAAEC,MAAM,CAAC;AACzB;AAKA,SAASW,OAAOA,CAAA,EAAG;EACjB,KAAK,IAAII,IAAI,GAAGb,SAAS,CAACC,MAAM,EAAElD,MAAM,GAAG,IAAIoD,KAAK,CAACU,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IACzF/D,MAAM,CAAC+D,IAAI,CAAC,GAAGd,SAAS,CAACc,IAAI,CAAC;EAChC;EACA,IAAIC,SAAS,GAAGhE,MAAM,CAACiE,IAAI,CAACC,QAAQ,CAAC;EACrC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACd,MAAM,EAAEkB,CAAC,EAAE,EAAE;IACzC,IAAI5D,KAAK,GAAGwD,SAAS,CAACI,CAAC,CAAC;IACxB,IAAI5D,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAE9CyB,MAAM,CAACoC,MAAM,CAACF,MAAM,EAAE3D,KAAK,CAAC;IAC9B;EACF;EACA,OAAO2D,MAAM;AACf;AAKA,SAASG,QAAQA,CAAA,EAAG;EAClB,OAAO;IACLC,EAAE,EAAE5E,KAAK,CAAC4E,EAAE;IACZC,WAAW,EAAE7E,KAAK,CAAC8E,cAAc,CAAC;EACpC,CAAC;AACH;AAMA,SAASC,UAAUA,CAAC1E,MAAM,EAAEC,OAAO,EAAE;EACnC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAII,KAAK,GAAGJ,OAAO,CAACE,gBAAgB,KAAK,KAAK;EAC9C,IAAIwE,UAAU,GAAG5E,YAAY,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC9C,IAAImD,KAAK,CAACwB,OAAO,CAACD,UAAU,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACtDA,UAAU,CAAC,CAAC,CAAC,GAAGzF,MAAM,CAACyF,UAAU,CAAC,CAAC,CAAC,EAAEtE,KAAK,CAAC;EAC9C;EACA,OAAOsE,UAAU;AACnB;AACAD,UAAU,CAAC5C,YAAY,GAAGA,YAAY;AACtC4C,UAAU,CAAClD,kBAAkB,GAAGA,kBAAkB;AAClDkD,UAAU,CAAC3C,MAAM,GAAGA,MAAM;AAC1B2C,UAAU,CAAC7B,OAAO,GAAGA,OAAO;AAC5B6B,UAAU,CAAChB,OAAO,GAAGA,OAAO;AAC5BgB,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;AAG9BI,UAAU,CAACG,aAAa,GAAG,CAAC;AAC5B,IAAIrF,SAAS,IAAIsF,MAAM,CAACC,8BAA8B,EAAE;EACtDD,MAAM,CAACC,8BAA8B,CAACC,cAAc,GAAGN,UAAU,CAAChB,OAAO;AAC3E;AACA,IAAIuB,UAAU,GAAGP,UAAU;AAC3B,eAAeO,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}