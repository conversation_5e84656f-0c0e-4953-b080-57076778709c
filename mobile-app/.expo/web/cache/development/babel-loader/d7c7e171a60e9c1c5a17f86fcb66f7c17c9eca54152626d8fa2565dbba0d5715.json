{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport { Ionicons } from '@expo/vector-icons';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar TabBar = function TabBar(_ref) {\n  var activeTab = _ref.activeTab,\n    onTabChange = _ref.onTabChange;\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsxs(TouchableOpacity, {\n      style: [styles.tab, activeTab === 'agent' && styles.activeTab],\n      onPress: function onPress() {\n        return onTabChange('agent');\n      },\n      children: [_jsx(Ionicons, {\n        name: \"mic\",\n        size: 24,\n        color: activeTab === 'agent' ? '#007AFF' : '#8E8E93'\n      }), _jsx(Text, {\n        style: [styles.tabText, activeTab === 'agent' && styles.activeTabText],\n        children: \"Agent\"\n      })]\n    }), _jsxs(TouchableOpacity, {\n      style: [styles.tab, activeTab === 'inbox' && styles.activeTab],\n      onPress: function onPress() {\n        return onTabChange('inbox');\n      },\n      children: [_jsx(Ionicons, {\n        name: \"chatbubbles\",\n        size: 24,\n        color: activeTab === 'inbox' ? '#007AFF' : '#8E8E93'\n      }), _jsx(Text, {\n        style: [styles.tabText, activeTab === 'inbox' && styles.activeTabText],\n        children: \"Inbox\"\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    backgroundColor: '#F8F8F8',\n    borderTopWidth: 1,\n    borderTopColor: '#E5E5E7',\n    paddingBottom: 34,\n    paddingTop: 8\n  },\n  tab: {\n    flex: 1,\n    alignItems: 'center',\n    paddingVertical: 8\n  },\n  activeTab: {},\n  tabText: {\n    fontSize: 12,\n    color: '#8E8E93',\n    marginTop: 4\n  },\n  activeTabText: {\n    color: '#007AFF',\n    fontWeight: '600'\n  }\n});\nexport default TabBar;", "map": {"version": 3, "names": ["React", "View", "TouchableOpacity", "Text", "StyleSheet", "Ionicons", "jsx", "_jsx", "jsxs", "_jsxs", "TabBar", "_ref", "activeTab", "onTabChange", "style", "styles", "container", "children", "tab", "onPress", "name", "size", "color", "tabText", "activeTabText", "create", "flexDirection", "backgroundColor", "borderTopWidth", "borderTopColor", "paddingBottom", "paddingTop", "flex", "alignItems", "paddingVertical", "fontSize", "marginTop", "fontWeight"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/components/TabBar.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, TouchableOpacity, Text, StyleSheet } from 'react-native';\nimport { Ionicons } from '@expo/vector-icons';\n\ninterface TabBarProps {\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nconst TabBar: React.FC<TabBarProps> = ({ activeTab, onTabChange }) => {\n  return (\n    <View style={styles.container}>\n      <TouchableOpacity\n        style={[styles.tab, activeTab === 'agent' && styles.activeTab]}\n        onPress={() => onTabChange('agent')}\n      >\n        <Ionicons \n          name=\"mic\" \n          size={24} \n          color={activeTab === 'agent' ? '#007AFF' : '#8E8E93'} \n        />\n        <Text style={[styles.tabText, activeTab === 'agent' && styles.activeTabText]}>\n          Agent\n        </Text>\n      </TouchableOpacity>\n      \n      <TouchableOpacity\n        style={[styles.tab, activeTab === 'inbox' && styles.activeTab]}\n        onPress={() => onTabChange('inbox')}\n      >\n        <Ionicons \n          name=\"chatbubbles\" \n          size={24} \n          color={activeTab === 'inbox' ? '#007AFF' : '#8E8E93'} \n        />\n        <Text style={[styles.tabText, activeTab === 'inbox' && styles.activeTabText]}>\n          Inbox\n        </Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flexDirection: 'row',\n    backgroundColor: '#F8F8F8',\n    borderTopWidth: 1,\n    borderTopColor: '#E5E5E7',\n    paddingBottom: 34, // Safe area for iPhone\n    paddingTop: 8,\n  },\n  tab: {\n    flex: 1,\n    alignItems: 'center',\n    paddingVertical: 8,\n  },\n  activeTab: {\n    // Active tab styling handled by text/icon color\n  },\n  tabText: {\n    fontSize: 12,\n    color: '#8E8E93',\n    marginTop: 4,\n  },\n  activeTabText: {\n    color: '#007AFF',\n    fontWeight: '600',\n  },\n});\n\nexport default TabBar;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE1B,SAASC,QAAQ,QAAQ,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAO9C,IAAMC,MAA6B,GAAG,SAAhCA,MAA6BA,CAAAC,IAAA,EAAmC;EAAA,IAA7BC,SAAS,GAAAD,IAAA,CAATC,SAAS;IAAEC,WAAW,GAAAF,IAAA,CAAXE,WAAW;EAC7D,OACEJ,KAAA,CAACR,IAAI;IAACa,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BR,KAAA,CAACP,gBAAgB;MACfY,KAAK,EAAE,CAACC,MAAM,CAACG,GAAG,EAAEN,SAAS,KAAK,OAAO,IAAIG,MAAM,CAACH,SAAS,CAAE;MAC/DO,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,WAAW,CAAC,OAAO,CAAC;MAAA,CAAC;MAAAI,QAAA,GAEpCV,IAAA,CAACF,QAAQ;QACPe,IAAI,EAAC,KAAK;QACVC,IAAI,EAAE,EAAG;QACTC,KAAK,EAAEV,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG;MAAU,CACtD,CAAC,EACFL,IAAA,CAACJ,IAAI;QAACW,KAAK,EAAE,CAACC,MAAM,CAACQ,OAAO,EAAEX,SAAS,KAAK,OAAO,IAAIG,MAAM,CAACS,aAAa,CAAE;QAAAP,QAAA,EAAC;MAE9E,CAAM,CAAC;IAAA,CACS,CAAC,EAEnBR,KAAA,CAACP,gBAAgB;MACfY,KAAK,EAAE,CAACC,MAAM,CAACG,GAAG,EAAEN,SAAS,KAAK,OAAO,IAAIG,MAAM,CAACH,SAAS,CAAE;MAC/DO,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQN,WAAW,CAAC,OAAO,CAAC;MAAA,CAAC;MAAAI,QAAA,GAEpCV,IAAA,CAACF,QAAQ;QACPe,IAAI,EAAC,aAAa;QAClBC,IAAI,EAAE,EAAG;QACTC,KAAK,EAAEV,SAAS,KAAK,OAAO,GAAG,SAAS,GAAG;MAAU,CACtD,CAAC,EACFL,IAAA,CAACJ,IAAI;QAACW,KAAK,EAAE,CAACC,MAAM,CAACQ,OAAO,EAAEX,SAAS,KAAK,OAAO,IAAIG,MAAM,CAACS,aAAa,CAAE;QAAAP,QAAA,EAAC;MAE9E,CAAM,CAAC;IAAA,CACS,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGX,UAAU,CAACqB,MAAM,CAAC;EAC/BT,SAAS,EAAE;IACTU,aAAa,EAAE,KAAK;IACpBC,eAAe,EAAE,SAAS;IAC1BC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE,SAAS;IACzBC,aAAa,EAAE,EAAE;IACjBC,UAAU,EAAE;EACd,CAAC;EACDb,GAAG,EAAE;IACHc,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,eAAe,EAAE;EACnB,CAAC;EACDtB,SAAS,EAAE,CAEX,CAAC;EACDW,OAAO,EAAE;IACPY,QAAQ,EAAE,EAAE;IACZb,KAAK,EAAE,SAAS;IAChBc,SAAS,EAAE;EACb,CAAC;EACDZ,aAAa,EAAE;IACbF,KAAK,EAAE,SAAS;IAChBe,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAe3B,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}