{"ast": null, "code": "'use strict';\n\nvar assets = [];\nfunction registerAsset(asset) {\n  return assets.push(asset);\n}\nfunction getAssetByID(assetId) {\n  return assets[assetId - 1];\n}\nmodule.exports = {\n  registerAsset: registerAsset,\n  getAssetByID: getAssetByID\n};", "map": {"version": 3, "names": ["assets", "registerAsset", "asset", "push", "getAssetByID", "assetId", "module", "exports"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/@react-native/assets-registry/registry.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow strict\n * @format\n */\n\n'use strict';\n\nexport type PackagerAsset = {\n  +__packager_asset: boolean,\n  +fileSystemLocation: string,\n  +httpServerLocation: string,\n  +width: ?number,\n  +height: ?number,\n  +scales: Array<number>,\n  +hash: string,\n  +name: string,\n  +type: string,\n  ...\n};\n\nconst assets: Array<PackagerAsset> = [];\n\nfunction registerAsset(asset: PackagerAsset): number {\n  // `push` returns new array length, so the first asset will\n  // get id 1 (not 0) to make the value truthy\n  return assets.push(asset);\n}\n\nfunction getAssetByID(assetId: number): PackagerAsset {\n  return assets[assetId - 1];\n}\n\nmodule.exports = {registerAsset, getAssetByID};\n"], "mappings": "AAUA,YAAY;;AAeZ,IAAMA,MAA4B,GAAG,EAAE;AAEvC,SAASC,aAAaA,CAACC,KAAoB,EAAU;EAGnD,OAAOF,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC;AAC3B;AAEA,SAASE,YAAYA,CAACC,OAAe,EAAiB;EACpD,OAAOL,MAAM,CAACK,OAAO,GAAG,CAAC,CAAC;AAC5B;AAEAC,MAAM,CAACC,OAAO,GAAG;EAACN,aAAa,EAAbA,aAAa;EAAEG,YAAY,EAAZA;AAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}