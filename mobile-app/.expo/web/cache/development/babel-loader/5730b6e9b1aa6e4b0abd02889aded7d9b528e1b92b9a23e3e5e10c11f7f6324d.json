{"ast": null, "code": "export var AndroidOutputFormat;\n(function (AndroidOutputFormat) {\n  AndroidOutputFormat[AndroidOutputFormat[\"DEFAULT\"] = 0] = \"DEFAULT\";\n  AndroidOutputFormat[AndroidOutputFormat[\"THREE_GPP\"] = 1] = \"THREE_GPP\";\n  AndroidOutputFormat[AndroidOutputFormat[\"MPEG_4\"] = 2] = \"MPEG_4\";\n  AndroidOutputFormat[AndroidOutputFormat[\"AMR_NB\"] = 3] = \"AMR_NB\";\n  AndroidOutputFormat[AndroidOutputFormat[\"AMR_WB\"] = 4] = \"AMR_WB\";\n  AndroidOutputFormat[AndroidOutputFormat[\"AAC_ADIF\"] = 5] = \"AAC_ADIF\";\n  AndroidOutputFormat[AndroidOutputFormat[\"AAC_ADTS\"] = 6] = \"AAC_ADTS\";\n  AndroidOutputFormat[AndroidOutputFormat[\"RTP_AVP\"] = 7] = \"RTP_AVP\";\n  AndroidOutputFormat[AndroidOutputFormat[\"MPEG2TS\"] = 8] = \"MPEG2TS\";\n  AndroidOutputFormat[AndroidOutputFormat[\"WEBM\"] = 9] = \"WEBM\";\n})(AndroidOutputFormat || (AndroidOutputFormat = {}));\nexport var AndroidAudioEncoder;\n(function (AndroidAudioEncoder) {\n  AndroidAudioEncoder[AndroidAudioEncoder[\"DEFAULT\"] = 0] = \"DEFAULT\";\n  AndroidAudioEncoder[AndroidAudioEncoder[\"AMR_NB\"] = 1] = \"AMR_NB\";\n  AndroidAudioEncoder[AndroidAudioEncoder[\"AMR_WB\"] = 2] = \"AMR_WB\";\n  AndroidAudioEncoder[AndroidAudioEncoder[\"AAC\"] = 3] = \"AAC\";\n  AndroidAudioEncoder[AndroidAudioEncoder[\"HE_AAC\"] = 4] = \"HE_AAC\";\n  AndroidAudioEncoder[AndroidAudioEncoder[\"AAC_ELD\"] = 5] = \"AAC_ELD\";\n})(AndroidAudioEncoder || (AndroidAudioEncoder = {}));\nexport var IOSOutputFormat;\n(function (IOSOutputFormat) {\n  IOSOutputFormat[\"LINEARPCM\"] = \"lpcm\";\n  IOSOutputFormat[\"AC3\"] = \"ac-3\";\n  IOSOutputFormat[\"60958AC3\"] = \"cac3\";\n  IOSOutputFormat[\"APPLEIMA4\"] = \"ima4\";\n  IOSOutputFormat[\"MPEG4AAC\"] = \"aac \";\n  IOSOutputFormat[\"MPEG4CELP\"] = \"celp\";\n  IOSOutputFormat[\"MPEG4HVXC\"] = \"hvxc\";\n  IOSOutputFormat[\"MPEG4TWINVQ\"] = \"twvq\";\n  IOSOutputFormat[\"MACE3\"] = \"MAC3\";\n  IOSOutputFormat[\"MACE6\"] = \"MAC6\";\n  IOSOutputFormat[\"ULAW\"] = \"ulaw\";\n  IOSOutputFormat[\"ALAW\"] = \"alaw\";\n  IOSOutputFormat[\"QDESIGN\"] = \"QDMC\";\n  IOSOutputFormat[\"QDESIGN2\"] = \"QDM2\";\n  IOSOutputFormat[\"QUALCOMM\"] = \"Qclp\";\n  IOSOutputFormat[\"MPEGLAYER1\"] = \".mp1\";\n  IOSOutputFormat[\"MPEGLAYER2\"] = \".mp2\";\n  IOSOutputFormat[\"MPEGLAYER3\"] = \".mp3\";\n  IOSOutputFormat[\"APPLELOSSLESS\"] = \"alac\";\n  IOSOutputFormat[\"MPEG4AAC_HE\"] = \"aach\";\n  IOSOutputFormat[\"MPEG4AAC_LD\"] = \"aacl\";\n  IOSOutputFormat[\"MPEG4AAC_ELD\"] = \"aace\";\n  IOSOutputFormat[\"MPEG4AAC_ELD_SBR\"] = \"aacf\";\n  IOSOutputFormat[\"MPEG4AAC_ELD_V2\"] = \"aacg\";\n  IOSOutputFormat[\"MPEG4AAC_HE_V2\"] = \"aacp\";\n  IOSOutputFormat[\"MPEG4AAC_SPATIAL\"] = \"aacs\";\n  IOSOutputFormat[\"AMR\"] = \"samr\";\n  IOSOutputFormat[\"AMR_WB\"] = \"sawb\";\n  IOSOutputFormat[\"AUDIBLE\"] = \"AUDB\";\n  IOSOutputFormat[\"ILBC\"] = \"ilbc\";\n  IOSOutputFormat[IOSOutputFormat[\"DVIINTELIMA\"] = 1836253201] = \"DVIINTELIMA\";\n  IOSOutputFormat[IOSOutputFormat[\"MICROSOFTGSM\"] = 1836253233] = \"MICROSOFTGSM\";\n  IOSOutputFormat[\"AES3\"] = \"aes3\";\n  IOSOutputFormat[\"ENHANCEDAC3\"] = \"ec-3\";\n})(IOSOutputFormat || (IOSOutputFormat = {}));\nexport var IOSAudioQuality;\n(function (IOSAudioQuality) {\n  IOSAudioQuality[IOSAudioQuality[\"MIN\"] = 0] = \"MIN\";\n  IOSAudioQuality[IOSAudioQuality[\"LOW\"] = 32] = \"LOW\";\n  IOSAudioQuality[IOSAudioQuality[\"MEDIUM\"] = 64] = \"MEDIUM\";\n  IOSAudioQuality[IOSAudioQuality[\"HIGH\"] = 96] = \"HIGH\";\n  IOSAudioQuality[IOSAudioQuality[\"MAX\"] = 127] = \"MAX\";\n})(IOSAudioQuality || (IOSAudioQuality = {}));\nexport var IOSBitRateStrategy;\n(function (IOSBitRateStrategy) {\n  IOSBitRateStrategy[IOSBitRateStrategy[\"CONSTANT\"] = 0] = \"CONSTANT\";\n  IOSBitRateStrategy[IOSBitRateStrategy[\"LONG_TERM_AVERAGE\"] = 1] = \"LONG_TERM_AVERAGE\";\n  IOSBitRateStrategy[IOSBitRateStrategy[\"VARIABLE_CONSTRAINED\"] = 2] = \"VARIABLE_CONSTRAINED\";\n  IOSBitRateStrategy[IOSBitRateStrategy[\"VARIABLE\"] = 3] = \"VARIABLE\";\n})(IOSBitRateStrategy || (IOSBitRateStrategy = {}));\nvar HIGH_QUALITY = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.m4a',\n    outputFormat: AndroidOutputFormat.MPEG_4,\n    audioEncoder: AndroidAudioEncoder.AAC,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000\n  },\n  ios: {\n    extension: '.m4a',\n    outputFormat: IOSOutputFormat.MPEG4AAC,\n    audioQuality: IOSAudioQuality.MAX,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000\n  }\n};\nvar LOW_QUALITY = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.3gp',\n    outputFormat: AndroidOutputFormat.THREE_GPP,\n    audioEncoder: AndroidAudioEncoder.AMR_NB,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000\n  },\n  ios: {\n    extension: '.caf',\n    audioQuality: IOSAudioQuality.MIN,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000\n  }\n};\nexport var RecordingOptionsPresets = {\n  HIGH_QUALITY: HIGH_QUALITY,\n  LOW_QUALITY: LOW_QUALITY\n};", "map": {"version": 3, "names": ["AndroidOutputFormat", "AndroidAudioEncoder", "IOSOutputFormat", "IOSAudioQuality", "IOSBitRateStrategy", "HIGH_QUALITY", "isMeteringEnabled", "android", "extension", "outputFormat", "MPEG_4", "audioEncoder", "AAC", "sampleRate", "numberOfChannels", "bitRate", "ios", "MPEG4AAC", "audioQuality", "MAX", "linearPCMBitDepth", "linearPCMIsBigEndian", "linearPCMIsFloat", "web", "mimeType", "bitsPerSecond", "LOW_QUALITY", "THREE_GPP", "AMR_NB", "MIN", "RecordingOptionsPresets"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio/RecordingConstants.ts"], "sourcesContent": ["import type { RecordingOptions } from './Recording.types';\n\n// @docsMissing\nexport enum AndroidOutputFormat {\n  DEFAULT = 0,\n  THREE_GPP = 1,\n  MPEG_4 = 2,\n  AMR_NB = 3,\n  AMR_WB = 4,\n  AAC_ADIF = 5,\n  AAC_ADTS = 6,\n  RTP_AVP = 7,\n  MPEG2TS = 8,\n  WEBM = 9,\n}\n\n// @docsMissing\nexport enum AndroidAudioEncoder {\n  DEFAULT = 0,\n  AMR_NB = 1,\n  AMR_WB = 2,\n  AAC = 3,\n  HE_AAC = 4,\n  AAC_ELD = 5,\n}\n\n// @docsMissing\n/**\n * > **Note** Not all of the iOS formats included in this list of constants are currently supported by iOS,\n * > in spite of appearing in the Apple source code. For an accurate list of formats supported by iOS, see\n * > [Core Audio Codecs](https://developer.apple.com/library/content/documentation/MusicAudio/Conceptual/CoreAudioOverview/CoreAudioEssentials/CoreAudioEssentials.html)\n * > and [iPhone Audio File Formats](https://developer.apple.com/library/content/documentation/MusicAudio/Conceptual/CoreAudioOverview/CoreAudioEssentials/CoreAudioEssentials.html).\n */\nexport enum IOSOutputFormat {\n  LINEARPCM = 'lpcm',\n  AC3 = 'ac-3',\n  '60958AC3' = 'cac3',\n  APPLEIMA4 = 'ima4',\n  MPEG4AAC = 'aac ',\n  MPEG4CELP = 'celp',\n  MPEG4HVXC = 'hvxc',\n  MPEG4TWINVQ = 'twvq',\n  MACE3 = 'MAC3',\n  MACE6 = 'MAC6',\n  ULAW = 'ulaw',\n  ALAW = 'alaw',\n  QDESIGN = 'QDMC',\n  QDESIGN2 = 'QDM2',\n  QUALCOMM = 'Qclp',\n  MPEGLAYER1 = '.mp1',\n  MPEGLAYER2 = '.mp2',\n  MPEGLAYER3 = '.mp3',\n  APPLELOSSLESS = 'alac',\n  MPEG4AAC_HE = 'aach',\n  MPEG4AAC_LD = 'aacl',\n  MPEG4AAC_ELD = 'aace',\n  MPEG4AAC_ELD_SBR = 'aacf',\n  MPEG4AAC_ELD_V2 = 'aacg',\n  MPEG4AAC_HE_V2 = 'aacp',\n  MPEG4AAC_SPATIAL = 'aacs',\n  AMR = 'samr',\n  AMR_WB = 'sawb',\n  AUDIBLE = 'AUDB',\n  ILBC = 'ilbc',\n  DVIINTELIMA = 0x6d730011,\n  MICROSOFTGSM = 0x6d730031,\n  AES3 = 'aes3',\n  ENHANCEDAC3 = 'ec-3',\n}\n\n// @docsMissing\nexport enum IOSAudioQuality {\n  MIN = 0,\n  LOW = 0x20,\n  MEDIUM = 0x40,\n  HIGH = 0x60,\n  MAX = 0x7f,\n}\n\n// @docsMissing\nexport enum IOSBitRateStrategy {\n  CONSTANT = 0,\n  LONG_TERM_AVERAGE = 1,\n  VARIABLE_CONSTRAINED = 2,\n  VARIABLE = 3,\n}\n\n// TODO : maybe make presets for music and speech, or lossy / lossless.\n\nconst HIGH_QUALITY: RecordingOptions = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.m4a',\n    outputFormat: AndroidOutputFormat.MPEG_4,\n    audioEncoder: AndroidAudioEncoder.AAC,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n  },\n  ios: {\n    extension: '.m4a',\n    outputFormat: IOSOutputFormat.MPEG4AAC,\n    audioQuality: IOSAudioQuality.MAX,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false,\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000,\n  },\n};\n\nconst LOW_QUALITY: RecordingOptions = {\n  isMeteringEnabled: true,\n  android: {\n    extension: '.3gp',\n    outputFormat: AndroidOutputFormat.THREE_GPP,\n    audioEncoder: AndroidAudioEncoder.AMR_NB,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n  },\n  ios: {\n    extension: '.caf',\n    audioQuality: IOSAudioQuality.MIN,\n    sampleRate: 44100,\n    numberOfChannels: 2,\n    bitRate: 128000,\n    linearPCMBitDepth: 16,\n    linearPCMIsBigEndian: false,\n    linearPCMIsFloat: false,\n  },\n  web: {\n    mimeType: 'audio/webm',\n    bitsPerSecond: 128000,\n  },\n};\n\n/**\n * Constant which contains definitions of the two preset examples of `RecordingOptions`, as implemented in the Audio SDK.\n *\n * # `HIGH_QUALITY`\n * ```ts\n * RecordingOptionsPresets.HIGH_QUALITY = {\n *   isMeteringEnabled: true,\n *   android: {\n *     extension: '.m4a',\n *     outputFormat: AndroidOutputFormat.MPEG_4,\n *     audioEncoder: AndroidAudioEncoder.AAC,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *   },\n *   ios: {\n *     extension: '.m4a',\n *     outputFormat: IOSOutputFormat.MPEG4AAC,\n *     audioQuality: IOSAudioQuality.MAX,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *     linearPCMBitDepth: 16,\n *     linearPCMIsBigEndian: false,\n *     linearPCMIsFloat: false,\n *   },\n *   web: {\n *     mimeType: 'audio/webm',\n *     bitsPerSecond: 128000,\n *   },\n * };\n * ```\n *\n * # `LOW_QUALITY`\n * ```ts\n * RecordingOptionsPresets.LOW_QUALITY = {\n *   isMeteringEnabled: true,\n *   android: {\n *     extension: '.3gp',\n *     outputFormat: AndroidOutputFormat.THREE_GPP,\n *     audioEncoder: AndroidAudioEncoder.AMR_NB,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *   },\n *   ios: {\n *     extension: '.caf',\n *     audioQuality: IOSAudioQuality.MIN,\n *     sampleRate: 44100,\n *     numberOfChannels: 2,\n *     bitRate: 128000,\n *     linearPCMBitDepth: 16,\n *     linearPCMIsBigEndian: false,\n *     linearPCMIsFloat: false,\n *   },\n *   web: {\n *     mimeType: 'audio/webm',\n *     bitsPerSecond: 128000,\n *   },\n * };\n * ```\n */\nexport const RecordingOptionsPresets: Record<string, RecordingOptions> = {\n  HIGH_QUALITY,\n  LOW_QUALITY,\n};\n"], "mappings": "AAGA,WAAYA,mBAWX;AAXD,WAAYA,mBAAmB;EAC7BA,mBAAA,CAAAA,mBAAA,4BAAW;EACXA,mBAAA,CAAAA,mBAAA,gCAAa;EACbA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,8BAAY;EACZA,mBAAA,CAAAA,mBAAA,8BAAY;EACZA,mBAAA,CAAAA,mBAAA,4BAAW;EACXA,mBAAA,CAAAA,mBAAA,4BAAW;EACXA,mBAAA,CAAAA,mBAAA,sBAAQ;AACV,CAAC,EAXWA,mBAAmB,KAAnBA,mBAAmB;AAc/B,WAAYC,mBAOX;AAPD,WAAYA,mBAAmB;EAC7BA,mBAAA,CAAAA,mBAAA,4BAAW;EACXA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,oBAAO;EACPA,mBAAA,CAAAA,mBAAA,0BAAU;EACVA,mBAAA,CAAAA,mBAAA,4BAAW;AACb,CAAC,EAPWA,mBAAmB,KAAnBA,mBAAmB;AAgB/B,WAAYC,eAmCX;AAnCD,WAAYA,eAAe;EACzBA,eAAA,sBAAkB;EAClBA,eAAA,gBAAY;EACZA,eAAA,qBAAmB;EACnBA,eAAA,sBAAkB;EAClBA,eAAA,qBAAiB;EACjBA,eAAA,sBAAkB;EAClBA,eAAA,sBAAkB;EAClBA,eAAA,wBAAoB;EACpBA,eAAA,kBAAc;EACdA,eAAA,kBAAc;EACdA,eAAA,iBAAa;EACbA,eAAA,iBAAa;EACbA,eAAA,oBAAgB;EAChBA,eAAA,qBAAiB;EACjBA,eAAA,qBAAiB;EACjBA,eAAA,uBAAmB;EACnBA,eAAA,uBAAmB;EACnBA,eAAA,uBAAmB;EACnBA,eAAA,0BAAsB;EACtBA,eAAA,wBAAoB;EACpBA,eAAA,wBAAoB;EACpBA,eAAA,yBAAqB;EACrBA,eAAA,6BAAyB;EACzBA,eAAA,4BAAwB;EACxBA,eAAA,2BAAuB;EACvBA,eAAA,6BAAyB;EACzBA,eAAA,gBAAY;EACZA,eAAA,mBAAe;EACfA,eAAA,oBAAgB;EAChBA,eAAA,iBAAa;EACbA,eAAA,CAAAA,eAAA,6CAAwB;EACxBA,eAAA,CAAAA,eAAA,+CAAyB;EACzBA,eAAA,iBAAa;EACbA,eAAA,wBAAoB;AACtB,CAAC,EAnCWA,eAAe,KAAfA,eAAe;AAsC3B,WAAYC,eAMX;AAND,WAAYA,eAAe;EACzBA,eAAA,CAAAA,eAAA,oBAAO;EACPA,eAAA,CAAAA,eAAA,qBAAU;EACVA,eAAA,CAAAA,eAAA,2BAAa;EACbA,eAAA,CAAAA,eAAA,uBAAW;EACXA,eAAA,CAAAA,eAAA,sBAAU;AACZ,CAAC,EANWA,eAAe,KAAfA,eAAe;AAS3B,WAAYC,kBAKX;AALD,WAAYA,kBAAkB;EAC5BA,kBAAA,CAAAA,kBAAA,8BAAY;EACZA,kBAAA,CAAAA,kBAAA,gDAAqB;EACrBA,kBAAA,CAAAA,kBAAA,sDAAwB;EACxBA,kBAAA,CAAAA,kBAAA,8BAAY;AACd,CAAC,EALWA,kBAAkB,KAAlBA,kBAAkB;AAS9B,IAAMC,YAAY,GAAqB;EACrCC,iBAAiB,EAAE,IAAI;EACvBC,OAAO,EAAE;IACPC,SAAS,EAAE,MAAM;IACjBC,YAAY,EAAET,mBAAmB,CAACU,MAAM;IACxCC,YAAY,EAAEV,mBAAmB,CAACW,GAAG;IACrCC,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,OAAO,EAAE;GACV;EACDC,GAAG,EAAE;IACHR,SAAS,EAAE,MAAM;IACjBC,YAAY,EAAEP,eAAe,CAACe,QAAQ;IACtCC,YAAY,EAAEf,eAAe,CAACgB,GAAG;IACjCN,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,OAAO,EAAE,MAAM;IACfK,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,KAAK;IAC3BC,gBAAgB,EAAE;GACnB;EACDC,GAAG,EAAE;IACHC,QAAQ,EAAE,YAAY;IACtBC,aAAa,EAAE;;CAElB;AAED,IAAMC,WAAW,GAAqB;EACpCpB,iBAAiB,EAAE,IAAI;EACvBC,OAAO,EAAE;IACPC,SAAS,EAAE,MAAM;IACjBC,YAAY,EAAET,mBAAmB,CAAC2B,SAAS;IAC3ChB,YAAY,EAAEV,mBAAmB,CAAC2B,MAAM;IACxCf,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,OAAO,EAAE;GACV;EACDC,GAAG,EAAE;IACHR,SAAS,EAAE,MAAM;IACjBU,YAAY,EAAEf,eAAe,CAAC0B,GAAG;IACjChB,UAAU,EAAE,KAAK;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,OAAO,EAAE,MAAM;IACfK,iBAAiB,EAAE,EAAE;IACrBC,oBAAoB,EAAE,KAAK;IAC3BC,gBAAgB,EAAE;GACnB;EACDC,GAAG,EAAE;IACHC,QAAQ,EAAE,YAAY;IACtBC,aAAa,EAAE;;CAElB;AAgED,OAAO,IAAMK,uBAAuB,GAAqC;EACvEzB,YAAY,EAAZA,YAAY;EACZqB,WAAW,EAAXA;CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}