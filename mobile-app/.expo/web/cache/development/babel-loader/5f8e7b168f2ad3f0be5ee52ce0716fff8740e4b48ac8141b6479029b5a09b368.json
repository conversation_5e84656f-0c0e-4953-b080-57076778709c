{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { InterruptionModeAndroid, InterruptionModeIOS } from \"./Audio.types\";\nimport ExponentAV from \"./ExponentAV\";\nexport * from \"./Audio/Recording\";\nexport * from \"./Audio/Sound\";\nexport { setIsEnabledAsync } from \"./Audio/AudioAvailability\";\nexport { PitchCorrectionQuality } from \"./AV\";\nvar _populateMissingKeys = function _populateMissingKeys(userAudioMode, defaultAudioMode) {\n  for (var key in defaultAudioMode) {\n    if (!userAudioMode.hasOwnProperty(key)) {\n      userAudioMode[key] = defaultAudioMode[key];\n    }\n  }\n  return userAudioMode;\n};\nvar defaultMode = {\n  allowsRecordingIOS: false,\n  interruptionModeIOS: InterruptionModeIOS.MixWithOthers,\n  playsInSilentModeIOS: false,\n  staysActiveInBackground: false,\n  interruptionModeAndroid: InterruptionModeAndroid.DuckOthers,\n  shouldDuckAndroid: true,\n  playThroughEarpieceAndroid: false\n};\nvar currentAudioMode = null;\nfunction getCurrentAudioMode() {\n  if (!currentAudioMode) {\n    return defaultMode;\n  }\n  return currentAudioMode;\n}\nexport function setAudioModeAsync(_x) {\n  return _setAudioModeAsync.apply(this, arguments);\n}\nfunction _setAudioModeAsync() {\n  _setAudioModeAsync = _asyncToGenerator(function* (partialMode) {\n    var mode = _populateMissingKeys(partialMode, getCurrentAudioMode());\n    if (!InterruptionModeIOS[mode.interruptionModeIOS]) {\n      throw new Error(`\"interruptionModeIOS\" was set to an invalid value.`);\n    }\n    if (!InterruptionModeAndroid[mode.interruptionModeAndroid]) {\n      throw new Error(`\"interruptionModeAndroid\" was set to an invalid value.`);\n    }\n    if (typeof mode.allowsRecordingIOS !== 'boolean' || typeof mode.playsInSilentModeIOS !== 'boolean' || typeof mode.staysActiveInBackground !== 'boolean' || typeof mode.shouldDuckAndroid !== 'boolean' || typeof mode.playThroughEarpieceAndroid !== 'boolean') {\n      throw new Error('\"allowsRecordingIOS\", \"playsInSilentModeIOS\", \"playThroughEarpieceAndroid\", \"staysActiveInBackground\" and \"shouldDuckAndroid\" must be booleans.');\n    }\n    currentAudioMode = mode;\n    return yield ExponentAV.setAudioMode(mode);\n  });\n  return _setAudioModeAsync.apply(this, arguments);\n}", "map": {"version": 3, "names": ["InterruptionModeAndroid", "InterruptionModeIOS", "ExponentAV", "setIsEnabledAsync", "PitchCorrectionQuality", "_populateMissingKeys", "userAudioMode", "defaultAudioMode", "key", "hasOwnProperty", "defaultMode", "allowsRecordingIOS", "interruptionModeIOS", "MixWithOthers", "playsInSilentModeIOS", "staysActiveInBackground", "interruptionModeAndroid", "<PERSON><PERSON><PERSON><PERSON>", "shouldDuckAndroid", "playThroughEarpieceAndroid", "currentAudioMode", "getCurrentAudioMode", "setAudioModeAsync", "_x", "_setAudioModeAsync", "apply", "arguments", "_asyncToGenerator", "partialMode", "mode", "Error", "setAudioMode"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio.ts"], "sourcesContent": ["import { AudioMode, InterruptionModeAndroid, InterruptionModeIOS } from './Audio.types';\nimport ExponentAV from './ExponentAV';\n\nexport * from './Audio/Recording';\nexport * from './Audio/Sound';\nexport { setIsEnabledAsync } from './Audio/AudioAvailability';\nexport { PitchCorrectionQuality } from './AV';\n\nconst _populateMissingKeys = (\n  userAudioMode: Partial<AudioMode>,\n  defaultAudioMode: AudioMode\n): AudioMode => {\n  for (const key in defaultAudioMode) {\n    if (!userAudioMode.hasOwnProperty(key)) {\n      userAudioMode[key] = defaultAudioMode[key];\n    }\n  }\n  return userAudioMode as AudioMode;\n};\n\nconst defaultMode: AudioMode = {\n  allowsRecordingIOS: false,\n  interruptionModeIOS: InterruptionModeIOS.MixWithOthers,\n  playsInSilentModeIOS: false,\n  staysActiveInBackground: false,\n  interruptionModeAndroid: InterruptionModeAndroid.DuckOthers,\n  shouldDuckAndroid: true,\n  playThroughEarpieceAndroid: false,\n};\n\nlet currentAudioMode: AudioMode | null = null;\n\nfunction getCurrentAudioMode(): AudioMode {\n  if (!currentAudioMode) {\n    return defaultMode;\n  }\n  return currentAudioMode;\n}\n\n/**\n * We provide this API to customize the audio experience on iOS and Android.\n * @param partialMode\n * @return A `Promise` that will reject if the audio mode could not be enabled for the device.\n */\nexport async function setAudioModeAsync(partialMode: Partial<AudioMode>): Promise<void> {\n  const mode = _populateMissingKeys(partialMode, getCurrentAudioMode());\n\n  if (!InterruptionModeIOS[mode.interruptionModeIOS]) {\n    throw new Error(`\"interruptionModeIOS\" was set to an invalid value.`);\n  }\n  if (!InterruptionModeAndroid[mode.interruptionModeAndroid]) {\n    throw new Error(`\"interruptionModeAndroid\" was set to an invalid value.`);\n  }\n  if (\n    typeof mode.allowsRecordingIOS !== 'boolean' ||\n    typeof mode.playsInSilentModeIOS !== 'boolean' ||\n    typeof mode.staysActiveInBackground !== 'boolean' ||\n    typeof mode.shouldDuckAndroid !== 'boolean' ||\n    typeof mode.playThroughEarpieceAndroid !== 'boolean'\n  ) {\n    throw new Error(\n      '\"allowsRecordingIOS\", \"playsInSilentModeIOS\", \"playThroughEarpieceAndroid\", \"staysActiveInBackground\" and \"shouldDuckAndroid\" must be booleans.'\n    );\n  }\n  currentAudioMode = mode;\n  return await ExponentAV.setAudioMode(mode);\n}\n"], "mappings": ";AAAA,SAAoBA,uBAAuB,EAAEC,mBAAmB;AAChE,OAAOC,UAAU;AAEjB;AACA;AACA,SAASC,iBAAiB;AAC1B,SAASC,sBAAsB;AAE/B,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CACxBC,aAAiC,EACjCC,gBAA2B,EACd;EACb,KAAK,IAAMC,GAAG,IAAID,gBAAgB,EAAE;IAClC,IAAI,CAACD,aAAa,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MACtCF,aAAa,CAACE,GAAG,CAAC,GAAGD,gBAAgB,CAACC,GAAG,CAAC;;;EAG9C,OAAOF,aAA0B;AACnC,CAAC;AAED,IAAMI,WAAW,GAAc;EAC7BC,kBAAkB,EAAE,KAAK;EACzBC,mBAAmB,EAAEX,mBAAmB,CAACY,aAAa;EACtDC,oBAAoB,EAAE,KAAK;EAC3BC,uBAAuB,EAAE,KAAK;EAC9BC,uBAAuB,EAAEhB,uBAAuB,CAACiB,UAAU;EAC3DC,iBAAiB,EAAE,IAAI;EACvBC,0BAA0B,EAAE;CAC7B;AAED,IAAIC,gBAAgB,GAAqB,IAAI;AAE7C,SAASC,mBAAmBA,CAAA;EAC1B,IAAI,CAACD,gBAAgB,EAAE;IACrB,OAAOV,WAAW;;EAEpB,OAAOU,gBAAgB;AACzB;AAOA,gBAAsBE,iBAAiBA,CAAAC,EAAA;EAAA,OAAAC,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAsBtC,SAAAF,mBAAA;EAAAA,kBAAA,GAAAG,iBAAA,CAtBM,WAAiCC,WAA+B;IACrE,IAAMC,IAAI,GAAGxB,oBAAoB,CAACuB,WAAW,EAAEP,mBAAmB,EAAE,CAAC;IAErE,IAAI,CAACpB,mBAAmB,CAAC4B,IAAI,CAACjB,mBAAmB,CAAC,EAAE;MAClD,MAAM,IAAIkB,KAAK,CAAC,oDAAoD,CAAC;;IAEvE,IAAI,CAAC9B,uBAAuB,CAAC6B,IAAI,CAACb,uBAAuB,CAAC,EAAE;MAC1D,MAAM,IAAIc,KAAK,CAAC,wDAAwD,CAAC;;IAE3E,IACE,OAAOD,IAAI,CAAClB,kBAAkB,KAAK,SAAS,IAC5C,OAAOkB,IAAI,CAACf,oBAAoB,KAAK,SAAS,IAC9C,OAAOe,IAAI,CAACd,uBAAuB,KAAK,SAAS,IACjD,OAAOc,IAAI,CAACX,iBAAiB,KAAK,SAAS,IAC3C,OAAOW,IAAI,CAACV,0BAA0B,KAAK,SAAS,EACpD;MACA,MAAM,IAAIW,KAAK,CACb,iJAAiJ,CAClJ;;IAEHV,gBAAgB,GAAGS,IAAI;IACvB,aAAa3B,UAAU,CAAC6B,YAAY,CAACF,IAAI,CAAC;EAC5C,CAAC;EAAA,OAAAL,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}