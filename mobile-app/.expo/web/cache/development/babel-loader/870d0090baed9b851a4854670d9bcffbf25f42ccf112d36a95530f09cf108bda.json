{"ast": null, "code": "import { append, appendTransform, identity, reset, toArray } from \"../Matrix2D\";\nimport { parse } from \"./transform\";\nfunction appendTransformProps(props) {\n  var x = props.x,\n    y = props.y,\n    originX = props.originX,\n    originY = props.originY,\n    scaleX = props.scaleX,\n    scaleY = props.scaleY,\n    rotation = props.rotation,\n    skewX = props.skewX,\n    skewY = props.skewY;\n  appendTransform(x + originX, y + originY, scaleX, scaleY, rotation, skewX, skewY, originX, originY);\n}\nfunction universal2axis(universal, axisX, axisY, defaultValue) {\n  var x;\n  var y;\n  if (typeof universal === 'number') {\n    x = y = universal;\n  } else if (typeof universal === 'string') {\n    var coords = universal.split(/\\s*,\\s*/);\n    if (coords.length === 2) {\n      x = +coords[0];\n      y = +coords[1];\n    } else if (coords.length === 1) {\n      x = y = +coords[0];\n    }\n  } else if (Array.isArray(universal)) {\n    if (universal.length === 2) {\n      x = +universal[0];\n      y = +universal[1];\n    } else if (universal.length === 1) {\n      x = y = +universal[0];\n    }\n  }\n  axisX = +axisX;\n  if (!isNaN(axisX)) {\n    x = axisX;\n  }\n  axisY = +axisY;\n  if (!isNaN(axisY)) {\n    y = axisY;\n  }\n  return [x || defaultValue || 0, y || defaultValue || 0];\n}\nexport function transformsArrayToProps(transformObjectsArray) {\n  var props = {};\n  transformObjectsArray === null || transformObjectsArray === void 0 ? void 0 : transformObjectsArray.forEach(function (transformObject) {\n    var keys = Object.keys(transformObject);\n    if (keys.length !== 1) {\n      console.error('You must specify exactly one property per transform object.');\n    }\n    var key = keys[0];\n    var value = transformObject[key];\n    props[key] = value;\n  });\n  return props;\n}\nexport function props2transform(props) {\n  if (!props) {\n    return null;\n  }\n  var extractedProps = Array.isArray(props) ? transformsArrayToProps(props) : props;\n  var rotation = extractedProps.rotation,\n    translate = extractedProps.translate,\n    translateX = extractedProps.translateX,\n    translateY = extractedProps.translateY,\n    origin = extractedProps.origin,\n    originX = extractedProps.originX,\n    originY = extractedProps.originY,\n    scale = extractedProps.scale,\n    scaleX = extractedProps.scaleX,\n    scaleY = extractedProps.scaleY,\n    skew = extractedProps.skew,\n    skewX = extractedProps.skewX,\n    skewY = extractedProps.skewY,\n    x = extractedProps.x,\n    y = extractedProps.y;\n  if (rotation == null && translate == null && translateX == null && translateY == null && origin == null && originX == null && originY == null && scale == null && scaleX == null && scaleY == null && skew == null && skewX == null && skewY == null && x == null && y == null) {\n    return null;\n  }\n  if (Array.isArray(x) || Array.isArray(y)) {\n    console.warn('Passing SvgLengthList to x or y attribute where SvgLength expected');\n  }\n  var tr = universal2axis(translate, translateX || (Array.isArray(x) ? x[0] : x), translateY || (Array.isArray(y) ? y[0] : y));\n  var or = universal2axis(origin, originX, originY);\n  var sc = universal2axis(scale, scaleX, scaleY, 1);\n  var sk = universal2axis(skew, skewX, skewY);\n  return {\n    rotation: rotation == null ? 0 : +rotation || 0,\n    originX: or[0],\n    originY: or[1],\n    scaleX: sc[0],\n    scaleY: sc[1],\n    skewX: sk[0],\n    skewY: sk[1],\n    x: tr[0],\n    y: tr[1]\n  };\n}\nexport function transformToMatrix(props, transform) {\n  if (!props && !transform) {\n    return null;\n  }\n  reset();\n  props && appendTransformProps(props);\n  if (transform) {\n    if (Array.isArray(transform)) {\n      if (typeof transform[0] === 'number') {\n        var columnMatrix = transform;\n        append(columnMatrix[0], columnMatrix[1], columnMatrix[2], columnMatrix[3], columnMatrix[4], columnMatrix[5]);\n      } else {\n        var transformProps = props2transform(transformsArrayToProps(transform));\n        transformProps && appendTransformProps(transformProps);\n      }\n    } else if (typeof transform === 'string') {\n      try {\n        var t = parse(transform);\n        append(t[0], t[3], t[1], t[4], t[2], t[5]);\n      } catch (e) {\n        console.error(e);\n      }\n    } else {\n      var _transformProps = props2transform(transform);\n      _transformProps && appendTransformProps(_transformProps);\n    }\n  }\n  return toArray();\n}\nexport default function extractTransform(props) {\n  if (Array.isArray(props) && typeof props[0] === 'number') {\n    return props;\n  }\n  if (typeof props === 'string') {\n    try {\n      var t = parse(props);\n      return [t[0], t[3], t[1], t[4], t[2], t[5]];\n    } catch (e) {\n      console.error(e);\n      return identity;\n    }\n  }\n  var transformProps = props;\n  return transformToMatrix(props2transform(transformProps), transformProps === null || transformProps === void 0 ? void 0 : transformProps.transform);\n}", "map": {"version": 3, "names": ["append", "appendTransform", "identity", "reset", "toArray", "parse", "appendTransformProps", "props", "x", "y", "originX", "originY", "scaleX", "scaleY", "rotation", "skewX", "skewY", "universal2axis", "universal", "axisX", "axisY", "defaultValue", "coords", "split", "length", "Array", "isArray", "isNaN", "transformsArrayToProps", "transformObjectsArray", "for<PERSON>ach", "transformObject", "keys", "Object", "console", "error", "key", "value", "props2transform", "extractedProps", "translate", "translateX", "translateY", "origin", "scale", "skew", "warn", "tr", "or", "sc", "sk", "transformToMatrix", "transform", "columnMatrix", "transformProps", "t", "e", "extractTransform"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-svg/src/lib/extract/extractTransform.ts"], "sourcesContent": ["import type { TransformsStyle } from 'react-native';\nimport { append, appendTransform, identity, reset, toArray } from '../Matrix2D';\nimport { parse } from './transform';\nimport type {\n  ColumnMajorTransformMatrix,\n  NumberProp,\n  TransformedProps,\n  TransformProps,\n} from './types';\n\nfunction appendTransformProps(props: TransformedProps) {\n  const { x, y, originX, originY, scaleX, scaleY, rotation, skewX, skewY } =\n    props;\n  appendTransform(\n    x + originX,\n    y + originY,\n    scaleX,\n    scaleY,\n    rotation,\n    skewX,\n    skewY,\n    originX,\n    originY,\n  );\n}\n\nfunction universal2axis(\n  universal: NumberProp | NumberProp[] | undefined,\n  axisX: NumberProp | void,\n  axisY: NumberProp | void,\n  defaultValue?: number,\n): [number, number] {\n  let x;\n  let y;\n  if (typeof universal === 'number') {\n    x = y = universal;\n  } else if (typeof universal === 'string') {\n    const coords = universal.split(/\\s*,\\s*/);\n    if (coords.length === 2) {\n      x = +coords[0];\n      y = +coords[1];\n    } else if (coords.length === 1) {\n      x = y = +coords[0];\n    }\n  } else if (Array.isArray(universal)) {\n    if (universal.length === 2) {\n      x = +universal[0];\n      y = +universal[1];\n    } else if (universal.length === 1) {\n      x = y = +universal[0];\n    }\n  }\n\n  axisX = +axisX;\n  if (!isNaN(axisX)) {\n    x = axisX;\n  }\n\n  axisY = +axisY;\n  if (!isNaN(axisY)) {\n    y = axisY;\n  }\n\n  return [x || defaultValue || 0, y || defaultValue || 0];\n}\n\nexport function transformsArrayToProps(\n  transformObjectsArray: TransformsStyle['transform'],\n) {\n  const props: TransformProps = {};\n  transformObjectsArray?.forEach((transformObject) => {\n    const keys = Object.keys(transformObject);\n    if (keys.length !== 1) {\n      console.error(\n        'You must specify exactly one property per transform object.',\n      );\n    }\n    const key = keys[0] as keyof TransformProps;\n    const value = transformObject[key as keyof typeof transformObject];\n    props[key] = value;\n  });\n  return props;\n}\n\nexport function props2transform(\n  props: TransformProps | undefined,\n): TransformedProps | null {\n  if (!props) {\n    return null;\n  }\n  const extractedProps = Array.isArray(props)\n    ? transformsArrayToProps(props)\n    : props;\n  const {\n    rotation,\n    translate,\n    translateX,\n    translateY,\n    origin,\n    originX,\n    originY,\n    scale,\n    scaleX,\n    scaleY,\n    skew,\n    skewX,\n    skewY,\n    x,\n    y,\n  } = extractedProps;\n  if (\n    rotation == null &&\n    translate == null &&\n    translateX == null &&\n    translateY == null &&\n    origin == null &&\n    originX == null &&\n    originY == null &&\n    scale == null &&\n    scaleX == null &&\n    scaleY == null &&\n    skew == null &&\n    skewX == null &&\n    skewY == null &&\n    x == null &&\n    y == null\n  ) {\n    return null;\n  }\n\n  if (Array.isArray(x) || Array.isArray(y)) {\n    console.warn(\n      'Passing SvgLengthList to x or y attribute where SvgLength expected',\n    );\n  }\n  const tr = universal2axis(\n    translate,\n    translateX || (Array.isArray(x) ? x[0] : x),\n    translateY || (Array.isArray(y) ? y[0] : y),\n  );\n  const or = universal2axis(origin, originX, originY);\n  const sc = universal2axis(scale, scaleX, scaleY, 1);\n  const sk = universal2axis(skew, skewX, skewY);\n\n  return {\n    rotation: rotation == null ? 0 : +rotation || 0,\n    originX: or[0],\n    originY: or[1],\n    scaleX: sc[0],\n    scaleY: sc[1],\n    skewX: sk[0],\n    skewY: sk[1],\n    x: tr[0],\n    y: tr[1],\n  };\n}\n\nexport function transformToMatrix(\n  props: TransformedProps | null,\n  transform: TransformProps['transform'],\n): ColumnMajorTransformMatrix | null {\n  if (!props && !transform) {\n    return null;\n  }\n  reset();\n  props && appendTransformProps(props);\n\n  if (transform) {\n    if (Array.isArray(transform)) {\n      if (typeof transform[0] === 'number') {\n        const columnMatrix = transform as ColumnMajorTransformMatrix;\n        append(\n          columnMatrix[0],\n          columnMatrix[1],\n          columnMatrix[2],\n          columnMatrix[3],\n          columnMatrix[4],\n          columnMatrix[5],\n        );\n      } else {\n        const transformProps = props2transform(\n          transformsArrayToProps(transform as TransformsStyle['transform']),\n        );\n        transformProps && appendTransformProps(transformProps);\n      }\n    } else if (typeof transform === 'string') {\n      try {\n        const t = parse(transform);\n        append(t[0], t[3], t[1], t[4], t[2], t[5]);\n      } catch (e) {\n        console.error(e);\n      }\n    } else {\n      const transformProps = props2transform(transform);\n      transformProps && appendTransformProps(transformProps);\n    }\n  }\n\n  return toArray();\n}\n\nexport default function extractTransform(\n  props: TransformProps | TransformProps['transform'],\n): ColumnMajorTransformMatrix | null {\n  if (Array.isArray(props) && typeof props[0] === 'number') {\n    return props as ColumnMajorTransformMatrix;\n  }\n  if (typeof props === 'string') {\n    try {\n      const t = parse(props);\n      return [t[0], t[3], t[1], t[4], t[2], t[5]];\n    } catch (e) {\n      console.error(e);\n      return identity;\n    }\n  }\n  // this type is not correct since props can be of type TransformsStyle['transform'] too\n  // but it satisfies TS and should not produce any type errors\n  const transformProps = props as TransformProps;\n  return transformToMatrix(\n    props2transform(transformProps),\n    transformProps?.transform,\n  );\n}\n"], "mappings": "AACA,SAASA,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO;AAC1D,SAASC,KAAK;AAQd,SAASC,oBAAoBA,CAACC,KAAuB,EAAE;EACrD,IAAQC,CAAC,GACPD,KAAK,CADCC,CAAC;IAAEC,CAAC,GACVF,KAAK,CADIE,CAAC;IAAEC,OAAO,GACnBH,KAAK,CADOG,OAAO;IAAEC,OAAO,GAC5BJ,KAAK,CADgBI,OAAO;IAAEC,MAAM,GACpCL,KAAK,CADyBK,MAAM;IAAEC,MAAM,GAC5CN,KAAK,CADiCM,MAAM;IAAEC,QAAQ,GACtDP,KAAK,CADyCO,QAAQ;IAAEC,KAAK,GAC7DR,KAAK,CADmDQ,KAAK;IAAEC,KAAA,GAC/DT,KAAK,CAD0DS,KAAA;EAEjEf,eAAe,CACbO,CAAC,GAAGE,OAAO,EACXD,CAAC,GAAGE,OAAO,EACXC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLN,OAAO,EACPC,OAAO,CACR;AACH;AAEA,SAASM,cAAcA,CACrBC,SAAgD,EAChDC,KAAwB,EACxBC,KAAwB,EACxBC,YAAqB,EACH;EAClB,IAAIb,CAAC;EACL,IAAIC,CAAC;EACL,IAAI,OAAOS,SAAS,KAAK,QAAQ,EAAE;IACjCV,CAAC,GAAGC,CAAC,GAAGS,SAAS;EACnB,CAAC,MAAM,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACxC,IAAMI,MAAM,GAAGJ,SAAS,CAACK,KAAK,CAAC,SAAS,CAAC;IACzC,IAAID,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MACvBhB,CAAC,GAAG,CAACc,MAAM,CAAC,CAAC,CAAC;MACdb,CAAC,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC;IAChB,CAAC,MAAM,IAAIA,MAAM,CAACE,MAAM,KAAK,CAAC,EAAE;MAC9BhB,CAAC,GAAGC,CAAC,GAAG,CAACa,MAAM,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACR,SAAS,CAAC,EAAE;IACnC,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MAC1BhB,CAAC,GAAG,CAACU,SAAS,CAAC,CAAC,CAAC;MACjBT,CAAC,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC;IACnB,CAAC,MAAM,IAAIA,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;MACjChB,CAAC,GAAGC,CAAC,GAAG,CAACS,SAAS,CAAC,CAAC,CAAC;IACvB;EACF;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACQ,KAAK,CAACR,KAAK,CAAC,EAAE;IACjBX,CAAC,GAAGW,KAAK;EACX;EAEAC,KAAK,GAAG,CAACA,KAAK;EACd,IAAI,CAACO,KAAK,CAACP,KAAK,CAAC,EAAE;IACjBX,CAAC,GAAGW,KAAK;EACX;EAEA,OAAO,CAACZ,CAAC,IAAIa,YAAY,IAAI,CAAC,EAAEZ,CAAC,IAAIY,YAAY,IAAI,CAAC,CAAC;AACzD;AAEA,OAAO,SAASO,sBAAsBA,CACpCC,qBAAmD,EACnD;EACA,IAAMtB,KAAqB,GAAG,CAAC,CAAC;EAChCsB,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEC,OAAO,CAAE,UAAAC,eAAe,EAAK;IAClD,IAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,eAAe,CAAC;IACzC,IAAIC,IAAI,CAACR,MAAM,KAAK,CAAC,EAAE;MACrBU,OAAO,CAACC,KAAK,CACX,6DAA6D,CAC9D;IACH;IACA,IAAMC,GAAG,GAAGJ,IAAI,CAAC,CAAC,CAAyB;IAC3C,IAAMK,KAAK,GAAGN,eAAe,CAACK,GAAG,CAAiC;IAClE7B,KAAK,CAAC6B,GAAG,CAAC,GAAGC,KAAK;EACpB,CAAC,CAAC;EACF,OAAO9B,KAAK;AACd;AAEA,OAAO,SAAS+B,eAAeA,CAC7B/B,KAAiC,EACR;EACzB,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EACA,IAAMgC,cAAc,GAAGd,KAAK,CAACC,OAAO,CAACnB,KAAK,CAAC,GACvCqB,sBAAsB,CAACrB,KAAK,CAAC,GAC7BA,KAAK;EACT,IACEO,QAAQ,GAeNyB,cAAc,CAfhBzB,QAAQ;IACR0B,SAAS,GAcPD,cAAc,CAdhBC,SAAS;IACTC,UAAU,GAaRF,cAAc,CAbhBE,UAAU;IACVC,UAAU,GAYRH,cAAc,CAZhBG,UAAU;IACVC,MAAM,GAWJJ,cAAc,CAXhBI,MAAM;IACNjC,OAAO,GAUL6B,cAAc,CAVhB7B,OAAO;IACPC,OAAO,GASL4B,cAAc,CAThB5B,OAAO;IACPiC,KAAK,GAQHL,cAAc,CARhBK,KAAK;IACLhC,MAAM,GAOJ2B,cAAc,CAPhB3B,MAAM;IACNC,MAAM,GAMJ0B,cAAc,CANhB1B,MAAM;IACNgC,IAAI,GAKFN,cAAc,CALhBM,IAAI;IACJ9B,KAAK,GAIHwB,cAAc,CAJhBxB,KAAK;IACLC,KAAK,GAGHuB,cAAc,CAHhBvB,KAAK;IACLR,CAAC,GAEC+B,cAAc,CAFhB/B,CAAC;IACDC,CAAA,GACE8B,cAAc,CADhB9B,CAAA;EAEF,IACEK,QAAQ,IAAI,IAAI,IAChB0B,SAAS,IAAI,IAAI,IACjBC,UAAU,IAAI,IAAI,IAClBC,UAAU,IAAI,IAAI,IAClBC,MAAM,IAAI,IAAI,IACdjC,OAAO,IAAI,IAAI,IACfC,OAAO,IAAI,IAAI,IACfiC,KAAK,IAAI,IAAI,IACbhC,MAAM,IAAI,IAAI,IACdC,MAAM,IAAI,IAAI,IACdgC,IAAI,IAAI,IAAI,IACZ9B,KAAK,IAAI,IAAI,IACbC,KAAK,IAAI,IAAI,IACbR,CAAC,IAAI,IAAI,IACTC,CAAC,IAAI,IAAI,EACT;IACA,OAAO,IAAI;EACb;EAEA,IAAIgB,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,IAAIiB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,EAAE;IACxCyB,OAAO,CAACY,IAAI,CACV,oEAAoE,CACrE;EACH;EACA,IAAMC,EAAE,GAAG9B,cAAc,CACvBuB,SAAS,EACTC,UAAU,KAAKhB,KAAK,CAACC,OAAO,CAAClB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,EAC3CkC,UAAU,KAAKjB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAC5C;EACD,IAAMuC,EAAE,GAAG/B,cAAc,CAAC0B,MAAM,EAAEjC,OAAO,EAAEC,OAAO,CAAC;EACnD,IAAMsC,EAAE,GAAGhC,cAAc,CAAC2B,KAAK,EAAEhC,MAAM,EAAEC,MAAM,EAAE,CAAC,CAAC;EACnD,IAAMqC,EAAE,GAAGjC,cAAc,CAAC4B,IAAI,EAAE9B,KAAK,EAAEC,KAAK,CAAC;EAE7C,OAAO;IACLF,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,QAAQ,IAAI,CAAC;IAC/CJ,OAAO,EAAEsC,EAAE,CAAC,CAAC,CAAC;IACdrC,OAAO,EAAEqC,EAAE,CAAC,CAAC,CAAC;IACdpC,MAAM,EAAEqC,EAAE,CAAC,CAAC,CAAC;IACbpC,MAAM,EAAEoC,EAAE,CAAC,CAAC,CAAC;IACblC,KAAK,EAAEmC,EAAE,CAAC,CAAC,CAAC;IACZlC,KAAK,EAAEkC,EAAE,CAAC,CAAC,CAAC;IACZ1C,CAAC,EAAEuC,EAAE,CAAC,CAAC,CAAC;IACRtC,CAAC,EAAEsC,EAAE,CAAC,CAAC;EACT,CAAC;AACH;AAEA,OAAO,SAASI,iBAAiBA,CAC/B5C,KAA8B,EAC9B6C,SAAsC,EACH;EACnC,IAAI,CAAC7C,KAAK,IAAI,CAAC6C,SAAS,EAAE;IACxB,OAAO,IAAI;EACb;EACAjD,KAAK,EAAE;EACPI,KAAK,IAAID,oBAAoB,CAACC,KAAK,CAAC;EAEpC,IAAI6C,SAAS,EAAE;IACb,IAAI3B,KAAK,CAACC,OAAO,CAAC0B,SAAS,CAAC,EAAE;MAC5B,IAAI,OAAOA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpC,IAAMC,YAAY,GAAGD,SAAuC;QAC5DpD,MAAM,CACJqD,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,EACfA,YAAY,CAAC,CAAC,CAAC,CAChB;MACH,CAAC,MAAM;QACL,IAAMC,cAAc,GAAGhB,eAAe,CACpCV,sBAAsB,CAACwB,SAAS,CAAiC,CAClE;QACDE,cAAc,IAAIhD,oBAAoB,CAACgD,cAAc,CAAC;MACxD;IACF,CAAC,MAAM,IAAI,OAAOF,SAAS,KAAK,QAAQ,EAAE;MACxC,IAAI;QACF,IAAMG,CAAC,GAAGlD,KAAK,CAAC+C,SAAS,CAAC;QAC1BpD,MAAM,CAACuD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVtB,OAAO,CAACC,KAAK,CAACqB,CAAC,CAAC;MAClB;IACF,CAAC,MAAM;MACL,IAAMF,eAAc,GAAGhB,eAAe,CAACc,SAAS,CAAC;MACjDE,eAAc,IAAIhD,oBAAoB,CAACgD,eAAc,CAAC;IACxD;EACF;EAEA,OAAOlD,OAAO,EAAE;AAClB;AAEA,eAAe,SAASqD,gBAAgBA,CACtClD,KAAmD,EAChB;EACnC,IAAIkB,KAAK,CAACC,OAAO,CAACnB,KAAK,CAAC,IAAI,OAAOA,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACxD,OAAOA,KAAK;EACd;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI;MACF,IAAMgD,CAAC,GAAGlD,KAAK,CAACE,KAAK,CAAC;MACtB,OAAO,CAACgD,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVtB,OAAO,CAACC,KAAK,CAACqB,CAAC,CAAC;MAChB,OAAOtD,QAAQ;IACjB;EACF;EAGA,IAAMoD,cAAc,GAAG/C,KAAuB;EAC9C,OAAO4C,iBAAiB,CACtBb,eAAe,CAACgB,cAAc,CAAC,EAC/BA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEF,SAAS,CAC1B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}