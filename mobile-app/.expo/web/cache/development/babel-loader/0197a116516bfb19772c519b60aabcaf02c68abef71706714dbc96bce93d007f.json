{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nvar supportsFullscreenAPI = function supportsFullscreenAPI(element) {\n  return 'requestFullscreen' in element;\n};\nvar supportsWebkitFullscreenAPI = function supportsWebkitFullscreenAPI(element) {\n  return 'webkitEnterFullScreen' in element;\n};\nvar supportsMsFullscreenAPI = function supportsMsFullscreenAPI(element) {\n  return 'msRequestFullscreen' in element;\n};\nvar supportsWebkitFullscreenChangeEvent = function supportsWebkitFullscreenChangeEvent() {\n  return supportsEvent('video', 'webkitfullscreenchange');\n};\nfunction addEventListener(element, eventName, listener) {\n  element.addEventListener(eventName, listener);\n  return function () {\n    return element.removeEventListener(eventName, listener);\n  };\n}\nvar supportsEvent = function supportsEvent(elementName, eventName) {\n  var element = document.createElement(elementName);\n  element.setAttribute('on' + eventName, 'return;');\n  return typeof element['on' + eventName] === 'function';\n};\nexport function requestFullscreen(_x) {\n  return _requestFullscreen.apply(this, arguments);\n}\nfunction _requestFullscreen() {\n  _requestFullscreen = _asyncToGenerator(function* (element) {\n    if (supportsFullscreenAPI(element)) {\n      return element.requestFullscreen();\n    } else if (supportsWebkitFullscreenAPI(element)) {\n      var _element$webkitEnterF;\n      (_element$webkitEnterF = element['webkitEnterFullScreen']) == null ? void 0 : _element$webkitEnterF.call(element);\n    } else if (supportsMsFullscreenAPI(element)) {\n      var _element$msRequestFul;\n      (_element$msRequestFul = element['msRequestFullscreen']) == null ? void 0 : _element$msRequestFul.call(element);\n    } else {\n      throw new Error('Fullscreen not supported');\n    }\n  });\n  return _requestFullscreen.apply(this, arguments);\n}\nexport function exitFullscreen(_x2) {\n  return _exitFullscreen.apply(this, arguments);\n}\nfunction _exitFullscreen() {\n  _exitFullscreen = _asyncToGenerator(function* (element) {\n    if (supportsFullscreenAPI(element)) {\n      return document.exitFullscreen();\n    } else if (supportsWebkitFullscreenAPI(element)) {\n      var _element$webkitExitFu;\n      (_element$webkitExitFu = element['webkitExitFullScreen']) == null ? void 0 : _element$webkitExitFu.call(element);\n    } else if (supportsMsFullscreenAPI(element)) {\n      var _document$msExitFulls, _document;\n      (_document$msExitFulls = (_document = document)['msExitFullscreen']) == null ? void 0 : _document$msExitFulls.call(_document);\n    } else {\n      throw new Error('Fullscreen not supported');\n    }\n  });\n  return _exitFullscreen.apply(this, arguments);\n}\nexport function addFullscreenListener(element, callback) {\n  if (supportsFullscreenAPI(element)) {\n    return addEventListener(element, 'fullscreenchange', function (event) {\n      return callback(document.fullscreenElement === event.target);\n    });\n  } else if (supportsWebkitFullscreenAPI(element) && supportsWebkitFullscreenChangeEvent()) {\n    return addEventListener(element, 'webkitfullscreenchange', function (event) {\n      return callback(document['webkitFullscreenElement'] === event.target);\n    });\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    var removeBeginListener = addEventListener(element, 'webkitbeginfullscreen', function () {\n      return callback(true);\n    });\n    var removeEndListener = addEventListener(element, 'webkitendfullscreen', function () {\n      return callback(false);\n    });\n    return function () {\n      removeBeginListener();\n      removeEndListener();\n    };\n  } else if (supportsMsFullscreenAPI(element)) {\n    return addEventListener(document, 'MSFullscreenChange', function (event) {\n      return callback(document['msFullscreenElement'] === event.target);\n    });\n  } else {\n    return function () {};\n  }\n}", "map": {"version": 3, "names": ["supportsFullscreenAPI", "element", "supportsWebkitFullscreenAPI", "supportsMsFullscreenAPI", "supportsWebkitFullscreenChangeEvent", "supportsEvent", "addEventListener", "eventName", "listener", "removeEventListener", "elementName", "document", "createElement", "setAttribute", "requestFullscreen", "_x", "_requestFullscreen", "apply", "arguments", "_asyncToGenerator", "_element$webkitEnterF", "call", "_element$msRequestFul", "Error", "exitFullscreen", "_x2", "_exitFullscreen", "_element$webkitExitFu", "_document$msExitFulls", "_document", "addFullscreenListener", "callback", "event", "fullscreenElement", "target", "removeBeginListener", "removeEndListener"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/FullscreenUtils.web.ts"], "sourcesContent": ["/**\n * Detect if the browser supports the standard fullscreen API on the given\n * element:\n * https://developer.mozilla.org/en-US/docs/Web/API/Fullscreen_API\n */\nconst supportsFullscreenAPI = (element: HTMLMediaElement): boolean =>\n  'requestFullscreen' in element;\n\n/**\n * Detect if the browser supports the non-standard webkit fullscreen API on the\n * given element (looking at you, Safari).\n */\nconst supportsWebkitFullscreenAPI = (element: HTMLMediaElement): boolean =>\n  'webkitEnterFullScreen' in element;\n\n/**\n * Detect if the browser supports the non-standard ms fullscreen API on the\n * given element (looking at you, IE11).\n */\nconst supportsMsFullscreenAPI = (element: HTMLMediaElement): boolean =>\n  'msRequestFullscreen' in element;\n\n/**\n * Detect if the browser supports the `webkitFullscreenChange` event. This is\n * a non-standard event added to Safari on macOS by Apple:\n * https://developer.apple.com/documentation/webkitjs/document/1631998-onwebkitfullscreenchange\n */\nconst supportsWebkitFullscreenChangeEvent = (): boolean =>\n  supportsEvent('video', 'webkitfullscreenchange');\n\n/**\n * A helper that adds an event listener to an element. The key value-add over\n * the native addEventListener is that it returns a function that will remove\n * the event listener. This allows the setup and teardown logic for a listener\n * to be easily colocated.\n */\nfunction addEventListener(\n  element: Document | HTMLElement,\n  eventName: string,\n  listener: EventListenerOrEventListenerObject\n): () => any {\n  element.addEventListener(eventName, listener);\n  return () => element.removeEventListener(eventName, listener);\n}\n\n/**\n * Detect if the browser supports an event on a particular element type.\n */\nconst supportsEvent = (elementName: string, eventName: string): boolean => {\n  // Detect if the browser supports the event by attempting to add a handler\n  // attribute for that event to the provided element. If the event is supported\n  // then the browser will accept the attribute and report the type of the\n  // attribute as \"function\". See: https://stackoverflow.com/a/4562426/2747759\n  const element = document.createElement(elementName);\n  element.setAttribute('on' + eventName, 'return;');\n  return typeof element['on' + eventName] === 'function';\n};\n\n/**\n * Switches a video element into fullscreen.\n */\nexport async function requestFullscreen(element: HTMLMediaElement): Promise<void> {\n  if (supportsFullscreenAPI(element)) {\n    return element.requestFullscreen();\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['webkitEnterFullScreen']?.();\n  } else if (supportsMsFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['msRequestFullscreen']?.();\n  } else {\n    throw new Error('Fullscreen not supported');\n  }\n}\n\n/**\n * Switches a video element out of fullscreen.\n */\n\nexport async function exitFullscreen(element: HTMLMediaElement): Promise<void> {\n  if (supportsFullscreenAPI(element)) {\n    return document.exitFullscreen();\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    element['webkitExitFullScreen']?.();\n  } else if (supportsMsFullscreenAPI(element)) {\n    // This API is synchronous so no need to return the result\n    document['msExitFullscreen']?.();\n  } else {\n    throw new Error('Fullscreen not supported');\n  }\n}\n\n/**\n * Listens for fullscreen change events on a video element. The provided\n * callback will be called with `true` when the video is switched into\n * fullscreen and `false` when the video is switched out of fullscreen.\n */\nexport function addFullscreenListener(\n  element: HTMLVideoElement,\n  callback: (isFullscreen: boolean) => void\n): () => any {\n  if (supportsFullscreenAPI(element)) {\n    // Used by browsers that support the official spec\n    return addEventListener(element, 'fullscreenchange', (event) =>\n      callback(document.fullscreenElement === event.target)\n    );\n  } else if (supportsWebkitFullscreenAPI(element) && supportsWebkitFullscreenChangeEvent()) {\n    // Used by Safari on macOS\n    return addEventListener(element, 'webkitfullscreenchange', (event) =>\n      callback(document['webkitFullscreenElement'] === event.target)\n    );\n  } else if (supportsWebkitFullscreenAPI(element)) {\n    // Used by Safari on iOS\n    const removeBeginListener = addEventListener(element, 'webkitbeginfullscreen', () =>\n      callback(true)\n    );\n    const removeEndListener = addEventListener(element, 'webkitendfullscreen', () =>\n      callback(false)\n    );\n    return () => {\n      removeBeginListener();\n      removeEndListener();\n    };\n  } else if (supportsMsFullscreenAPI(element)) {\n    // Used by IE11\n    return addEventListener(document, 'MSFullscreenChange', (event) =>\n      callback(document['msFullscreenElement'] === event.target)\n    );\n  } else {\n    return () => {};\n  }\n}\n"], "mappings": ";AAKA,IAAMA,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,OAAyB;EAAA,OACtD,mBAAmB,IAAIA,OAAO;AAAA;AAMhC,IAAMC,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAID,OAAyB;EAAA,OAC5D,uBAAuB,IAAIA,OAAO;AAAA;AAMpC,IAAME,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAIF,OAAyB;EAAA,OACxD,qBAAqB,IAAIA,OAAO;AAAA;AAOlC,IAAMG,mCAAmC,GAAG,SAAtCA,mCAAmCA,CAAA;EAAA,OACvCC,aAAa,CAAC,OAAO,EAAE,wBAAwB,CAAC;AAAA;AAQlD,SAASC,gBAAgBA,CACvBL,OAA+B,EAC/BM,SAAiB,EACjBC,QAA4C;EAE5CP,OAAO,CAACK,gBAAgB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7C,OAAO;IAAA,OAAMP,OAAO,CAACQ,mBAAmB,CAACF,SAAS,EAAEC,QAAQ,CAAC;EAAA;AAC/D;AAKA,IAAMH,aAAa,GAAG,SAAhBA,aAAaA,CAAIK,WAAmB,EAAEH,SAAiB,EAAa;EAKxE,IAAMN,OAAO,GAAGU,QAAQ,CAACC,aAAa,CAACF,WAAW,CAAC;EACnDT,OAAO,CAACY,YAAY,CAAC,IAAI,GAAGN,SAAS,EAAE,SAAS,CAAC;EACjD,OAAO,OAAON,OAAO,CAAC,IAAI,GAAGM,SAAS,CAAC,KAAK,UAAU;AACxD,CAAC;AAKD,gBAAsBO,iBAAiBA,CAAAC,EAAA;EAAA,OAAAC,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAYtC,SAAAF,mBAAA;EAAAA,kBAAA,GAAAG,iBAAA,CAZM,WAAiClB,OAAyB;IAC/D,IAAID,qBAAqB,CAACC,OAAO,CAAC,EAAE;MAClC,OAAOA,OAAO,CAACa,iBAAiB,EAAE;KACnC,MAAM,IAAIZ,2BAA2B,CAACD,OAAO,CAAC,EAAE;MAAA,IAAAmB,qBAAA;MAE/C,CAAAA,qBAAA,GAAAnB,OAAO,CAAC,uBAAuB,CAAC,qBAAhCmB,qBAAA,CAAAC,IAAA,CAAApB,OAAkC,CAAE;KACrC,MAAM,IAAIE,uBAAuB,CAACF,OAAO,CAAC,EAAE;MAAA,IAAAqB,qBAAA;MAE3C,CAAAA,qBAAA,GAAArB,OAAO,CAAC,qBAAqB,CAAC,qBAA9BqB,qBAAA,CAAAD,IAAA,CAAApB,OAAgC,CAAE;KACnC,MAAM;MACL,MAAM,IAAIsB,KAAK,CAAC,0BAA0B,CAAC;;EAE/C,CAAC;EAAA,OAAAP,kBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAMD,gBAAsBM,cAAcA,CAAAC,GAAA;EAAA,OAAAC,eAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAYnC,SAAAQ,gBAAA;EAAAA,eAAA,GAAAP,iBAAA,CAZM,WAA8BlB,OAAyB;IAC5D,IAAID,qBAAqB,CAACC,OAAO,CAAC,EAAE;MAClC,OAAOU,QAAQ,CAACa,cAAc,EAAE;KACjC,MAAM,IAAItB,2BAA2B,CAACD,OAAO,CAAC,EAAE;MAAA,IAAA0B,qBAAA;MAE/C,CAAAA,qBAAA,GAAA1B,OAAO,CAAC,sBAAsB,CAAC,qBAA/B0B,qBAAA,CAAAN,IAAA,CAAApB,OAAiC,CAAE;KACpC,MAAM,IAAIE,uBAAuB,CAACF,OAAO,CAAC,EAAE;MAAA,IAAA2B,qBAAA,EAAAC,SAAA;MAE3C,CAAAD,qBAAA,IAAAC,SAAA,GAAAlB,QAAQ,EAAC,kBAAkB,CAAC,qBAA5BiB,qBAAA,CAAAP,IAAA,CAAAQ,SAA8B,CAAE;KACjC,MAAM;MACL,MAAM,IAAIN,KAAK,CAAC,0BAA0B,CAAC;;EAE/C,CAAC;EAAA,OAAAG,eAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAOD,OAAM,SAAUY,qBAAqBA,CACnC7B,OAAyB,EACzB8B,QAAyC;EAEzC,IAAI/B,qBAAqB,CAACC,OAAO,CAAC,EAAE;IAElC,OAAOK,gBAAgB,CAACL,OAAO,EAAE,kBAAkB,EAAE,UAAC+B,KAAK;MAAA,OACzDD,QAAQ,CAACpB,QAAQ,CAACsB,iBAAiB,KAAKD,KAAK,CAACE,MAAM,CAAC;IAAA,EACtD;GACF,MAAM,IAAIhC,2BAA2B,CAACD,OAAO,CAAC,IAAIG,mCAAmC,EAAE,EAAE;IAExF,OAAOE,gBAAgB,CAACL,OAAO,EAAE,wBAAwB,EAAE,UAAC+B,KAAK;MAAA,OAC/DD,QAAQ,CAACpB,QAAQ,CAAC,yBAAyB,CAAC,KAAKqB,KAAK,CAACE,MAAM,CAAC;IAAA,EAC/D;GACF,MAAM,IAAIhC,2BAA2B,CAACD,OAAO,CAAC,EAAE;IAE/C,IAAMkC,mBAAmB,GAAG7B,gBAAgB,CAACL,OAAO,EAAE,uBAAuB,EAAE;MAAA,OAC7E8B,QAAQ,CAAC,IAAI,CAAC;IAAA,EACf;IACD,IAAMK,iBAAiB,GAAG9B,gBAAgB,CAACL,OAAO,EAAE,qBAAqB,EAAE;MAAA,OACzE8B,QAAQ,CAAC,KAAK,CAAC;IAAA,EAChB;IACD,OAAO,YAAK;MACVI,mBAAmB,EAAE;MACrBC,iBAAiB,EAAE;IACrB,CAAC;GACF,MAAM,IAAIjC,uBAAuB,CAACF,OAAO,CAAC,EAAE;IAE3C,OAAOK,gBAAgB,CAACK,QAAQ,EAAE,oBAAoB,EAAE,UAACqB,KAAK;MAAA,OAC5DD,QAAQ,CAACpB,QAAQ,CAAC,qBAAqB,CAAC,KAAKqB,KAAK,CAACE,MAAM,CAAC;IAAA,EAC3D;GACF,MAAM;IACL,OAAO,YAAK,CAAE,CAAC;;AAEnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}