{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nvar _Sound;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { EventEmitter, Platform, UnavailabilityError } from 'expo-modules-core';\nimport { PlaybackMixin, assertStatusValuesInBounds, getNativeSourceAndFullInitialStatusForLoadAsync, getUnloadedStatus } from \"../AV\";\nimport ExponentAV from \"../ExponentAV\";\nimport { throwIfAudioIsDisabled } from \"./AudioAvailability\";\nexport var Sound = function () {\n  function Sound() {\n    var _this = this;\n    _classCallCheck(this, Sound);\n    this._loaded = false;\n    this._loading = false;\n    this._key = null;\n    this._lastStatusUpdate = null;\n    this._lastStatusUpdateTime = null;\n    this._subscriptions = [];\n    this._eventEmitter = new EventEmitter(ExponentAV);\n    this._coalesceStatusUpdatesInMillis = 100;\n    this._onPlaybackStatusUpdate = null;\n    this._onMetadataUpdate = null;\n    this._onAudioSampleReceived = null;\n    this._internalStatusUpdateCallback = function (_ref) {\n      var key = _ref.key,\n        status = _ref.status;\n      if (_this._key === key) {\n        _this._callOnPlaybackStatusUpdateForNewStatus(status);\n      }\n    };\n    this._internalMetadataUpdateCallback = function (_ref2) {\n      var key = _ref2.key,\n        metadata = _ref2.metadata;\n      if (_this._key === key) {\n        _this._onMetadataUpdate == null ? void 0 : _this._onMetadataUpdate(metadata);\n      }\n    };\n    this._internalErrorCallback = function (_ref3) {\n      var key = _ref3.key,\n        error = _ref3.error;\n      if (_this._key === key) {\n        _this._errorCallback(error);\n      }\n    };\n    this._errorCallback = function (error) {\n      _this._clearSubscriptions();\n      _this._loaded = false;\n      _this._key = null;\n      _this._callOnPlaybackStatusUpdateForNewStatus(getUnloadedStatus(error));\n    };\n    this.getStatusAsync = _asyncToGenerator(function* () {\n      if (_this._loaded) {\n        return _this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.getStatusForSound(_this._key);\n        });\n      }\n      var status = getUnloadedStatus();\n      _this._callOnPlaybackStatusUpdateForNewStatus(status);\n      return status;\n    });\n  }\n  return _createClass(Sound, [{\n    key: \"_callOnPlaybackStatusUpdateForNewStatus\",\n    value: function _callOnPlaybackStatusUpdateForNewStatus(status) {\n      var shouldDismissBasedOnCoalescing = this._lastStatusUpdateTime && JSON.stringify(status) === this._lastStatusUpdate && Date.now() - this._lastStatusUpdateTime.getTime() < this._coalesceStatusUpdatesInMillis;\n      if (this._onPlaybackStatusUpdate != null && !shouldDismissBasedOnCoalescing) {\n        this._onPlaybackStatusUpdate(status);\n        this._lastStatusUpdateTime = new Date();\n        this._lastStatusUpdate = JSON.stringify(status);\n      }\n    }\n  }, {\n    key: \"_performOperationAndHandleStatusAsync\",\n    value: function () {\n      var _performOperationAndHandleStatusAsync2 = _asyncToGenerator(function* (operation) {\n        throwIfAudioIsDisabled();\n        if (this._loaded) {\n          var status = yield operation();\n          this._callOnPlaybackStatusUpdateForNewStatus(status);\n          return status;\n        } else {\n          throw new Error('Cannot complete operation because sound is not loaded.');\n        }\n      });\n      function _performOperationAndHandleStatusAsync(_x) {\n        return _performOperationAndHandleStatusAsync2.apply(this, arguments);\n      }\n      return _performOperationAndHandleStatusAsync;\n    }()\n  }, {\n    key: \"_updateAudioSampleReceivedCallback\",\n    value: function _updateAudioSampleReceivedCallback() {\n      if (globalThis.__EXAV_setOnAudioSampleReceivedCallback == null) {\n        if (Platform.OS === 'ios' || Platform.OS === 'android') {\n          console.warn('expo-av: Failed to set up Audio Sample Buffer callback. ' + \"Do you have 'Remote Debugging' enabled in your app's Developer Menu (https://docs.expo.dev/workflow/debugging)? \" + 'Audio Sample Buffer callbacks are not supported while using Remote Debugging, you will need to disable it to use them.');\n          return;\n        } else {\n          throw new UnavailabilityError('expo-av', 'setOnAudioSampleReceived');\n        }\n      }\n      if (this._key == null) {\n        throw new Error('Cannot set Audio Sample Buffer callback when the Sound instance has not been successfully loaded/initialized!');\n      }\n      if (typeof this._key !== 'number') {\n        throw new Error(`Cannot set Audio Sample Buffer callback when Sound instance key is of type ${typeof this._key}! (expected: number)`);\n      }\n      globalThis.__EXAV_setOnAudioSampleReceivedCallback(this._key, this._onAudioSampleReceived);\n    }\n  }, {\n    key: \"_subscribeToNativeEvents\",\n    value: function _subscribeToNativeEvents() {\n      if (this._loaded) {\n        this._subscriptions.push(this._eventEmitter.addListener('didUpdatePlaybackStatus', this._internalStatusUpdateCallback), this._eventEmitter.addListener('didUpdateMetadata', this._internalMetadataUpdateCallback));\n        this._subscriptions.push(this._eventEmitter.addListener('ExponentAV.onError', this._internalErrorCallback));\n      }\n    }\n  }, {\n    key: \"_clearSubscriptions\",\n    value: function _clearSubscriptions() {\n      this._subscriptions.forEach(function (e) {\n        return e.remove();\n      });\n      this._subscriptions = [];\n    }\n  }, {\n    key: \"setOnPlaybackStatusUpdate\",\n    value: function setOnPlaybackStatusUpdate(onPlaybackStatusUpdate) {\n      this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n      this.getStatusAsync();\n    }\n  }, {\n    key: \"setOnMetadataUpdate\",\n    value: function setOnMetadataUpdate(onMetadataUpdate) {\n      this._onMetadataUpdate = onMetadataUpdate;\n    }\n  }, {\n    key: \"setOnAudioSampleReceived\",\n    value: function setOnAudioSampleReceived(callback) {\n      this._onAudioSampleReceived = callback;\n      if (this._key != null) {\n        this._updateAudioSampleReceivedCallback();\n      }\n    }\n  }, {\n    key: \"loadAsync\",\n    value: function () {\n      var _loadAsync = _asyncToGenerator(function* (source) {\n        var _this2 = this;\n        var initialStatus = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        var downloadFirst = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n        throwIfAudioIsDisabled();\n        if (this._loading) {\n          throw new Error('The Sound is already loading.');\n        }\n        if (!this._loaded) {\n          this._loading = true;\n          var _yield$getNativeSourc = yield getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst),\n            nativeSource = _yield$getNativeSourc.nativeSource,\n            fullInitialStatus = _yield$getNativeSourc.fullInitialStatus;\n          return new Promise(function (resolve, reject) {\n            var loadSuccess = function loadSuccess(result) {\n              var _result = _slicedToArray(result, 2),\n                key = _result[0],\n                status = _result[1];\n              _this2._key = key;\n              _this2._loaded = true;\n              _this2._loading = false;\n              _this2._subscribeToNativeEvents();\n              _this2._callOnPlaybackStatusUpdateForNewStatus(status);\n              resolve(status);\n            };\n            var loadError = function loadError(error) {\n              _this2._loading = false;\n              reject(error);\n            };\n            ExponentAV.loadForSound(nativeSource, fullInitialStatus).then(loadSuccess).catch(loadError);\n          });\n        } else {\n          throw new Error('The Sound is already loaded.');\n        }\n      });\n      function loadAsync(_x2) {\n        return _loadAsync.apply(this, arguments);\n      }\n      return loadAsync;\n    }()\n  }, {\n    key: \"unloadAsync\",\n    value: function () {\n      var _unloadAsync = _asyncToGenerator(function* () {\n        if (this._loaded) {\n          this._loaded = false;\n          var key = this._key;\n          this._key = null;\n          var status = yield ExponentAV.unloadForSound(key);\n          this._callOnPlaybackStatusUpdateForNewStatus(status);\n          this._clearSubscriptions();\n          return status;\n        } else {\n          return this.getStatusAsync();\n        }\n      });\n      function unloadAsync() {\n        return _unloadAsync.apply(this, arguments);\n      }\n      return unloadAsync;\n    }()\n  }, {\n    key: \"setStatusAsync\",\n    value: function () {\n      var _setStatusAsync = _asyncToGenerator(function* (status) {\n        var _this3 = this;\n        assertStatusValuesInBounds(status);\n        return this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.setStatusForSound(_this3._key, status);\n        });\n      });\n      function setStatusAsync(_x3) {\n        return _setStatusAsync.apply(this, arguments);\n      }\n      return setStatusAsync;\n    }()\n  }, {\n    key: \"replayAsync\",\n    value: function () {\n      var _replayAsync = _asyncToGenerator(function* () {\n        var _this4 = this;\n        var status = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        if (status.positionMillis && status.positionMillis !== 0) {\n          throw new Error('Requested position after replay has to be 0.');\n        }\n        return this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.replaySound(_this4._key, _objectSpread(_objectSpread({}, status), {}, {\n            positionMillis: 0,\n            shouldPlay: true\n          }));\n        });\n      });\n      function replayAsync() {\n        return _replayAsync.apply(this, arguments);\n      }\n      return replayAsync;\n    }()\n  }]);\n}();\n_Sound = Sound;\nSound.create = function () {\n  var _ref5 = _asyncToGenerator(function* (source) {\n    var initialStatus = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var onPlaybackStatusUpdate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var downloadFirst = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n    console.warn(`Sound.create is deprecated in favor of Sound.createAsync with the same API except for the new method name`);\n    return _Sound.createAsync(source, initialStatus, onPlaybackStatusUpdate, downloadFirst);\n  });\n  return function (_x4) {\n    return _ref5.apply(this, arguments);\n  };\n}();\nSound.createAsync = function () {\n  var _ref6 = _asyncToGenerator(function* (source) {\n    var initialStatus = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var onPlaybackStatusUpdate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var downloadFirst = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n    var sound = new _Sound();\n    sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);\n    var status = yield sound.loadAsync(source, initialStatus, downloadFirst);\n    return {\n      sound: sound,\n      status: status\n    };\n  });\n  return function (_x5) {\n    return _ref6.apply(this, arguments);\n  };\n}();\nObject.assign(Sound.prototype, PlaybackMixin);", "map": {"version": 3, "names": ["EventEmitter", "Platform", "UnavailabilityError", "PlaybackMixin", "assertStatusValuesInBounds", "getNativeSourceAndFullInitialStatusForLoadAsync", "getUnloadedStatus", "ExponentAV", "throwIfAudioIsDisabled", "Sound", "_this", "_classCallCheck", "_loaded", "_loading", "_key", "_lastStatusUpdate", "_lastStatusUpdateTime", "_subscriptions", "_eventEmitter", "_coalesceStatusUpdatesInMillis", "_onPlaybackStatusUpdate", "_onMetadataUpdate", "_onAudioSampleReceived", "_internalStatusUpdateCallback", "_ref", "key", "status", "_callOnPlaybackStatusUpdateForNewStatus", "_internalMetadataUpdateCallback", "_ref2", "metadata", "_internalErrorCallback", "_ref3", "error", "_errorCallback", "_clearSubscriptions", "getStatusAsync", "_asyncToGenerator", "_performOperationAndHandleStatusAsync", "getStatusForSound", "_createClass", "value", "shouldDismissBasedOnCoalescing", "JSON", "stringify", "Date", "now", "getTime", "_performOperationAndHandleStatusAsync2", "operation", "Error", "_x", "apply", "arguments", "_updateAudioSampleReceivedCallback", "globalThis", "__EXAV_setOnAudioSampleReceivedCallback", "OS", "console", "warn", "_subscribeToNativeEvents", "push", "addListener", "for<PERSON>ach", "e", "remove", "setOnPlaybackStatusUpdate", "onPlaybackStatusUpdate", "setOnMetadataUpdate", "onMetadataUpdate", "setOnAudioSampleReceived", "callback", "_loadAsync", "source", "_this2", "initialStatus", "length", "undefined", "downloadFirst", "_yield$getNativeSourc", "nativeSource", "fullInitialStatus", "Promise", "resolve", "reject", "loadSuccess", "result", "_result", "_slicedToArray", "loadError", "loadForSound", "then", "catch", "loadAsync", "_x2", "_unloadAsync", "unloadForSound", "unloadAsync", "_setStatusAsync", "_this3", "setStatusForSound", "setStatusAsync", "_x3", "_replayAsync", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "replaySound", "_objectSpread", "shouldPlay", "replayAsync", "create", "_ref5", "createAsync", "_x4", "_ref6", "sound", "_x5", "Object", "assign", "prototype"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio/Sound.ts"], "sourcesContent": ["import { EventEmitter, Platform, UnavailabilityError } from 'expo-modules-core';\n\nimport {\n  Playback,\n  PlaybackMixin,\n  AVPlaybackSource,\n  AVMetadata,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n  assertStatusValuesInBounds,\n  getNativeSourceAndFullInitialStatusForLoadAsync,\n  getUnloadedStatus,\n  AVPlaybackTolerance,\n} from '../AV';\nimport { PitchCorrectionQuality } from '../Audio';\nimport ExponentAV from '../ExponentAV';\nimport { throwIfAudioIsDisabled } from './AudioAvailability';\n\n// @needsAudit\nexport type AudioChannel = {\n  /**\n   * All samples for this specific Audio Channel in PCM Buffer format (-1 to 1).\n   */\n  frames: number[];\n};\n\n// @needsAudit\n/**\n * Object passed to the `onAudioSampleReceived` function. Represents a single sample from an audio source.\n * The sample contains all frames (PCM Buffer values) for each channel of the audio, so if the audio is _stereo_ (interleaved),\n * there will be two channels, one for left and one for right audio.\n */\nexport type AudioSample = {\n  /**\n   * An array representing the data from each channel in PCM Buffer format. Array elements are objects in the following format: `{ frames: number[] }`,\n   * where each frame is a number in PCM Buffer format (`-1` to `1` range).\n   */\n  channels: AudioChannel[];\n  /**\n   * A number representing the timestamp of the current sample in seconds, relative to the audio track's timeline.\n   * > **Known issue:** When using the `ExoPlayer` Android implementation, the timestamp is always `-1`.\n   */\n  timestamp: number;\n};\n\n// @needsAudit\nexport type SoundObject = {\n  /**\n   * The newly created and loaded `Sound` object.\n   */\n  sound: Sound;\n  /**\n   * The `PlaybackStatus` of the `Sound` object. See the [AV documentation](/versions/latest/sdk/av) for further information.\n   */\n  status: AVPlaybackStatus;\n};\n\ntype AudioInstance = number | HTMLMediaElement | null;\ntype AudioSampleCallback = ((sample: AudioSample) => void) | null;\n\ndeclare global {\n  interface Global {\n    __EXAV_setOnAudioSampleReceivedCallback:\n      | ((key: number, callback: AudioSampleCallback) => void)\n      | undefined;\n  }\n}\n\n// @needsAudit\n/**\n * This class represents a sound corresponding to an Asset or URL.\n * @return A newly constructed instance of `Audio.Sound`.\n *\n * @example\n * ```ts\n * const sound = new Audio.Sound();\n * try {\n *   await sound.loadAsync(require('./assets/sounds/hello.mp3'));\n *   await sound.playAsync();\n *   // Your sound is playing!\n *\n *   // Don't forget to unload the sound from memory\n *   // when you are done using the Sound object\n *   await sound.unloadAsync();\n * } catch (error) {\n *   // An error occurred!\n * }\n * ```\n *\n * > Method not described below and the rest of the API for `Audio.Sound` is the same as the imperative playback API for `Video`.\n * > See the [AV documentation](/versions/latest/sdk/av) for further information.\n */\nexport class Sound implements Playback {\n  _loaded: boolean = false;\n  _loading: boolean = false;\n  _key: AudioInstance = null;\n  _lastStatusUpdate: string | null = null;\n  _lastStatusUpdateTime: Date | null = null;\n  _subscriptions: { remove: () => void }[] = [];\n  _eventEmitter: EventEmitter = new EventEmitter(ExponentAV);\n  _coalesceStatusUpdatesInMillis: number = 100;\n  _onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null;\n  _onMetadataUpdate: ((metadata: AVMetadata) => void) | null = null;\n  _onAudioSampleReceived: AudioSampleCallback = null;\n\n  /** @deprecated Use `Sound.createAsync()` instead */\n  static create = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null,\n    downloadFirst: boolean = true\n  ): Promise<SoundObject> => {\n    console.warn(\n      `Sound.create is deprecated in favor of Sound.createAsync with the same API except for the new method name`\n    );\n    return Sound.createAsync(source, initialStatus, onPlaybackStatusUpdate, downloadFirst);\n  };\n\n  /**\n   * Creates and loads a sound from source.\n   *\n   * ```ts\n   * const { sound } = await Audio.Sound.createAsync(\n   *   source,\n   *   initialStatus,\n   *   onPlaybackStatusUpdate,\n   *   downloadFirst\n   * );\n   *\n   * // Which is equivalent to the following:\n   * const sound = new Audio.Sound();\n   * sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);\n   * await sound.loadAsync(source, initialStatus, downloadFirst);\n   * ```\n   *\n   * @param source The source of the sound. See the [AV documentation](/versions/latest/sdk/av/#playback-api) for details on the possible `source` values.\n   *\n   * @param initialStatus The initial intended `PlaybackStatusToSet` of the sound, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. See the [AV documentation](/versions/latest/sdk/av) for details on `PlaybackStatusToSet` and the default\n   * initial playback status.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `PlaybackStatus`. This value defaults to `null` if no parameter is passed.\n   * See the [AV documentation](/versions/latest/sdk/av) for details on the functionality provided by `onPlaybackStatusUpdate`\n   *\n   * @param downloadFirst If set to true, the system will attempt to download the resource to the device before loading. This value defaults to `true`.\n   * Note that at the moment, this will only work for `source`s of the form `require('path/to/file')` or `Asset` objects.\n   *\n   * @example\n   * ```ts\n   * try {\n   *   const { sound: soundObject, status } = await Audio.Sound.createAsync(\n   *     require('./assets/sounds/hello.mp3'),\n   *     { shouldPlay: true }\n   *   );\n   *   // Your sound is playing!\n   * } catch (error) {\n   *   // An error occurred!\n   * }\n   * ```\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the `SoundObject` if creation succeeded.\n   */\n  static createAsync = async (\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null,\n    downloadFirst: boolean = true\n  ): Promise<SoundObject> => {\n    const sound: Sound = new Sound();\n    sound.setOnPlaybackStatusUpdate(onPlaybackStatusUpdate);\n    const status: AVPlaybackStatus = await sound.loadAsync(source, initialStatus, downloadFirst);\n    return { sound, status };\n  };\n\n  // Internal methods\n\n  _callOnPlaybackStatusUpdateForNewStatus(status: AVPlaybackStatus) {\n    const shouldDismissBasedOnCoalescing =\n      this._lastStatusUpdateTime &&\n      JSON.stringify(status) === this._lastStatusUpdate &&\n      Date.now() - this._lastStatusUpdateTime.getTime() < this._coalesceStatusUpdatesInMillis;\n\n    if (this._onPlaybackStatusUpdate != null && !shouldDismissBasedOnCoalescing) {\n      this._onPlaybackStatusUpdate(status);\n      this._lastStatusUpdateTime = new Date();\n      this._lastStatusUpdate = JSON.stringify(status);\n    }\n  }\n\n  async _performOperationAndHandleStatusAsync(\n    operation: () => Promise<AVPlaybackStatus>\n  ): Promise<AVPlaybackStatus> {\n    throwIfAudioIsDisabled();\n    if (this._loaded) {\n      const status = await operation();\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n      return status;\n    } else {\n      throw new Error('Cannot complete operation because sound is not loaded.');\n    }\n  }\n\n  private _updateAudioSampleReceivedCallback() {\n    if (globalThis.__EXAV_setOnAudioSampleReceivedCallback == null) {\n      if (Platform.OS === 'ios' || Platform.OS === 'android') {\n        console.warn(\n          'expo-av: Failed to set up Audio Sample Buffer callback. ' +\n            \"Do you have 'Remote Debugging' enabled in your app's Developer Menu (https://docs.expo.dev/workflow/debugging)? \" +\n            'Audio Sample Buffer callbacks are not supported while using Remote Debugging, you will need to disable it to use them.'\n        );\n        return;\n      } else {\n        throw new UnavailabilityError('expo-av', 'setOnAudioSampleReceived');\n      }\n    }\n    if (this._key == null) {\n      throw new Error(\n        'Cannot set Audio Sample Buffer callback when the Sound instance has not been successfully loaded/initialized!'\n      );\n    }\n    if (typeof this._key !== 'number') {\n      throw new Error(\n        `Cannot set Audio Sample Buffer callback when Sound instance key is of type ${typeof this\n          ._key}! (expected: number)`\n      );\n    }\n\n    globalThis.__EXAV_setOnAudioSampleReceivedCallback(this._key, this._onAudioSampleReceived);\n  }\n\n  _internalStatusUpdateCallback = ({\n    key,\n    status,\n  }: {\n    key: AudioInstance;\n    status: AVPlaybackStatus;\n  }) => {\n    if (this._key === key) {\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n    }\n  };\n\n  _internalMetadataUpdateCallback = ({\n    key,\n    metadata,\n  }: {\n    key: AudioInstance;\n    metadata: AVMetadata;\n  }) => {\n    if (this._key === key) {\n      this._onMetadataUpdate?.(metadata);\n    }\n  };\n\n  _internalErrorCallback = ({ key, error }: { key: AudioInstance; error: string }) => {\n    if (this._key === key) {\n      this._errorCallback(error);\n    }\n  };\n\n  // TODO: We can optimize by only using time observer on native if (this._onPlaybackStatusUpdate).\n  _subscribeToNativeEvents() {\n    if (this._loaded) {\n      this._subscriptions.push(\n        this._eventEmitter.addListener(\n          'didUpdatePlaybackStatus',\n          this._internalStatusUpdateCallback\n        ),\n        this._eventEmitter.addListener('didUpdateMetadata', this._internalMetadataUpdateCallback)\n      );\n\n      this._subscriptions.push(\n        this._eventEmitter.addListener('ExponentAV.onError', this._internalErrorCallback)\n      );\n    }\n  }\n\n  _clearSubscriptions() {\n    this._subscriptions.forEach((e) => e.remove());\n    this._subscriptions = [];\n  }\n\n  _errorCallback = (error: string) => {\n    this._clearSubscriptions();\n    this._loaded = false;\n    this._key = null;\n    this._callOnPlaybackStatusUpdateForNewStatus(getUnloadedStatus(error));\n  };\n\n  // ### Unified playback API ### (consistent with Video.js)\n  // All calls automatically call onPlaybackStatusUpdate as a side effect.\n\n  // Get status API\n\n  getStatusAsync = async (): Promise<AVPlaybackStatus> => {\n    if (this._loaded) {\n      return this._performOperationAndHandleStatusAsync(() =>\n        ExponentAV.getStatusForSound(this._key)\n      );\n    }\n    const status: AVPlaybackStatus = getUnloadedStatus();\n    this._callOnPlaybackStatusUpdateForNewStatus(status);\n    return status;\n  };\n\n  /**\n   * Sets a function to be called regularly with the `AVPlaybackStatus` of the playback object.\n   *\n   * `onPlaybackStatusUpdate` will be called whenever a call to the API for this playback object completes\n   * (such as `setStatusAsync()`, `getStatusAsync()`, or `unloadAsync()`), nd will also be called at regular intervals\n   * while the media is in the loaded state.\n   *\n   * Set `progressUpdateIntervalMillis` via `setStatusAsync()` or `setProgressUpdateIntervalAsync()` to modify\n   * the interval with which `onPlaybackStatusUpdate` is called while loaded.\n   *\n   * @param onPlaybackStatusUpdate A function taking a single parameter `AVPlaybackStatus`.\n   */\n  setOnPlaybackStatusUpdate(onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null) {\n    this._onPlaybackStatusUpdate = onPlaybackStatusUpdate;\n    this.getStatusAsync();\n  }\n\n  /**\n   * Sets a function to be called whenever the metadata of the sound object changes, if one is set.\n   * @param onMetadataUpdate A function taking a single object of type `AVMetadata` as a parameter.\n   * @platform ios\n   */\n  setOnMetadataUpdate(onMetadataUpdate: (metadata: AVMetadata) => void) {\n    this._onMetadataUpdate = onMetadataUpdate;\n  }\n\n  /**\n   * Sets a function to be called during playback, receiving the audio sample as parameter.\n   * @param callback A function taking the `AudioSampleCallback` as parameter.\n   */\n  setOnAudioSampleReceived(callback: AudioSampleCallback) {\n    this._onAudioSampleReceived = callback;\n    if (this._key != null) {\n      this._updateAudioSampleReceivedCallback();\n    }\n  }\n\n  // Loading / unloading API\n\n  async loadAsync(\n    source: AVPlaybackSource,\n    initialStatus: AVPlaybackStatusToSet = {},\n    downloadFirst: boolean = true\n  ): Promise<AVPlaybackStatus> {\n    throwIfAudioIsDisabled();\n    if (this._loading) {\n      throw new Error('The Sound is already loading.');\n    }\n    if (!this._loaded) {\n      this._loading = true;\n\n      const { nativeSource, fullInitialStatus } =\n        await getNativeSourceAndFullInitialStatusForLoadAsync(source, initialStatus, downloadFirst);\n\n      // This is a workaround, since using load with resolve / reject seems to not work.\n      return new Promise<AVPlaybackStatus>((resolve, reject) => {\n        const loadSuccess = (result: [AudioInstance, AVPlaybackStatus]) => {\n          const [key, status] = result;\n          this._key = key;\n          this._loaded = true;\n          this._loading = false;\n          this._subscribeToNativeEvents();\n          this._callOnPlaybackStatusUpdateForNewStatus(status);\n          resolve(status);\n        };\n\n        const loadError = (error: Error) => {\n          this._loading = false;\n          reject(error);\n        };\n\n        ExponentAV.loadForSound(nativeSource, fullInitialStatus).then(loadSuccess).catch(loadError);\n      });\n    } else {\n      throw new Error('The Sound is already loaded.');\n    }\n  }\n\n  async unloadAsync(): Promise<AVPlaybackStatus> {\n    if (this._loaded) {\n      this._loaded = false;\n      const key = this._key;\n      this._key = null;\n      const status = await ExponentAV.unloadForSound(key);\n      this._callOnPlaybackStatusUpdateForNewStatus(status);\n      this._clearSubscriptions();\n      return status;\n    } else {\n      return this.getStatusAsync(); // Automatically calls onPlaybackStatusUpdate.\n    }\n  }\n\n  // Set status API (only available while isLoaded = true)\n\n  async setStatusAsync(status: AVPlaybackStatusToSet): Promise<AVPlaybackStatus> {\n    assertStatusValuesInBounds(status);\n    return this._performOperationAndHandleStatusAsync(() =>\n      ExponentAV.setStatusForSound(this._key, status)\n    );\n  }\n\n  async replayAsync(status: AVPlaybackStatusToSet = {}): Promise<AVPlaybackStatus> {\n    if (status.positionMillis && status.positionMillis !== 0) {\n      throw new Error('Requested position after replay has to be 0.');\n    }\n\n    return this._performOperationAndHandleStatusAsync(() =>\n      ExponentAV.replaySound(this._key, {\n        ...status,\n        positionMillis: 0,\n        shouldPlay: true,\n      })\n    );\n  }\n\n  // Methods of the Playback interface that are set via PlaybackMixin\n  playAsync!: () => Promise<AVPlaybackStatus>;\n  playFromPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  pauseAsync!: () => Promise<AVPlaybackStatus>;\n  stopAsync!: () => Promise<AVPlaybackStatus>;\n  setPositionAsync!: (\n    positionMillis: number,\n    tolerances?: AVPlaybackTolerance\n  ) => Promise<AVPlaybackStatus>;\n  setRateAsync!: (\n    rate: number,\n    shouldCorrectPitch: boolean,\n    pitchCorrectionQuality?: PitchCorrectionQuality\n  ) => Promise<AVPlaybackStatus>;\n  setVolumeAsync!: (volume: number, audioPan?: number) => Promise<AVPlaybackStatus>;\n  setIsMutedAsync!: (isMuted: boolean) => Promise<AVPlaybackStatus>;\n  setIsLoopingAsync!: (isLooping: boolean) => Promise<AVPlaybackStatus>;\n  setProgressUpdateIntervalAsync!: (\n    progressUpdateIntervalMillis: number\n  ) => Promise<AVPlaybackStatus>;\n}\n\nObject.assign(Sound.prototype, PlaybackMixin);\n"], "mappings": ";;;;;;;;AAAA,SAASA,YAAY,EAAEC,QAAQ,EAAEC,mBAAmB,QAAQ,mBAAmB;AAE/E,SAEEC,aAAa,EAKbC,0BAA0B,EAC1BC,+CAA+C,EAC/CC,iBAAiB;AAInB,OAAOC,UAAU;AACjB,SAASC,sBAAsB;AA4E/B,WAAaC,KAAK;EAAA,SAAAA,MAAA;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAF,KAAA;IAAA,KAChBG,OAAO,GAAY,KAAK;IAAA,KACxBC,QAAQ,GAAY,KAAK;IAAA,KACzBC,IAAI,GAAkB,IAAI;IAAA,KAC1BC,iBAAiB,GAAkB,IAAI;IAAA,KACvCC,qBAAqB,GAAgB,IAAI;IAAA,KACzCC,cAAc,GAA6B,EAAE;IAAA,KAC7CC,aAAa,GAAiB,IAAIlB,YAAY,CAACO,UAAU,CAAC;IAAA,KAC1DY,8BAA8B,GAAW,GAAG;IAAA,KAC5CC,uBAAuB,GAAgD,IAAI;IAAA,KAC3EC,iBAAiB,GAA4C,IAAI;IAAA,KACjEC,sBAAsB,GAAwB,IAAI;IAAA,KA+HlDC,6BAA6B,GAAG,UAAAC,IAAA,EAM3B;MAAA,IALHC,GAAG,GAAAD,IAAA,CAAHC,GAAG;QACHC,MAAM,GAAAF,IAAA,CAANE,MAAM;MAKN,IAAIhB,KAAI,CAACI,IAAI,KAAKW,GAAG,EAAE;QACrBf,KAAI,CAACiB,uCAAuC,CAACD,MAAM,CAAC;;IAExD,CAAC;IAAA,KAEDE,+BAA+B,GAAG,UAAAC,KAAA,EAM7B;MAAA,IALHJ,GAAG,GAAAI,KAAA,CAAHJ,GAAG;QACHK,QAAQ,GAAAD,KAAA,CAARC,QAAQ;MAKR,IAAIpB,KAAI,CAACI,IAAI,KAAKW,GAAG,EAAE;QACrBf,KAAI,CAACW,iBAAiB,oBAAtBX,KAAI,CAACW,iBAAiB,CAAGS,QAAQ,CAAC;;IAEtC,CAAC;IAAA,KAEDC,sBAAsB,GAAG,UAAAC,KAAA,EAA0D;MAAA,IAAvDP,GAAG,GAAAO,KAAA,CAAHP,GAAG;QAAEQ,KAAK,GAAAD,KAAA,CAALC,KAAK;MACpC,IAAIvB,KAAI,CAACI,IAAI,KAAKW,GAAG,EAAE;QACrBf,KAAI,CAACwB,cAAc,CAACD,KAAK,CAAC;;IAE9B,CAAC;IAAA,KAwBDC,cAAc,GAAG,UAACD,KAAa,EAAI;MACjCvB,KAAI,CAACyB,mBAAmB,EAAE;MAC1BzB,KAAI,CAACE,OAAO,GAAG,KAAK;MACpBF,KAAI,CAACI,IAAI,GAAG,IAAI;MAChBJ,KAAI,CAACiB,uCAAuC,CAACrB,iBAAiB,CAAC2B,KAAK,CAAC,CAAC;IACxE,CAAC;IAAA,KAODG,cAAc,GAAAC,iBAAA,CAAG,aAAsC;MACrD,IAAI3B,KAAI,CAACE,OAAO,EAAE;QAChB,OAAOF,KAAI,CAAC4B,qCAAqC,CAAC;UAAA,OAChD/B,UAAU,CAACgC,iBAAiB,CAAC7B,KAAI,CAACI,IAAI,CAAC;QAAA,EACxC;;MAEH,IAAMY,MAAM,GAAqBpB,iBAAiB,EAAE;MACpDI,KAAI,CAACiB,uCAAuC,CAACD,MAAM,CAAC;MACpD,OAAOA,MAAM;IACf,CAAC;EAAA;EAAA,OAAAc,YAAA,CAAA/B,KAAA;IAAAgB,GAAA;IAAAgB,KAAA,EA/HD,SAAAd,uCAAuCA,CAACD,MAAwB;MAC9D,IAAMgB,8BAA8B,GAClC,IAAI,CAAC1B,qBAAqB,IAC1B2B,IAAI,CAACC,SAAS,CAAClB,MAAM,CAAC,KAAK,IAAI,CAACX,iBAAiB,IACjD8B,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC9B,qBAAqB,CAAC+B,OAAO,EAAE,GAAG,IAAI,CAAC5B,8BAA8B;MAEzF,IAAI,IAAI,CAACC,uBAAuB,IAAI,IAAI,IAAI,CAACsB,8BAA8B,EAAE;QAC3E,IAAI,CAACtB,uBAAuB,CAACM,MAAM,CAAC;QACpC,IAAI,CAACV,qBAAqB,GAAG,IAAI6B,IAAI,EAAE;QACvC,IAAI,CAAC9B,iBAAiB,GAAG4B,IAAI,CAACC,SAAS,CAAClB,MAAM,CAAC;;IAEnD;EAAC;IAAAD,GAAA;IAAAgB,KAAA;MAAA,IAAAO,sCAAA,GAAAX,iBAAA,CAED,WACEY,SAA0C;QAE1CzC,sBAAsB,EAAE;QACxB,IAAI,IAAI,CAACI,OAAO,EAAE;UAChB,IAAMc,MAAM,SAASuB,SAAS,EAAE;UAChC,IAAI,CAACtB,uCAAuC,CAACD,MAAM,CAAC;UACpD,OAAOA,MAAM;SACd,MAAM;UACL,MAAM,IAAIwB,KAAK,CAAC,wDAAwD,CAAC;;MAE7E,CAAC;MAAA,SAXKZ,qCAAqCA,CAAAa,EAAA;QAAA,OAAAH,sCAAA,CAAAI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArCf,qCAAqC;IAAA;EAAA;IAAAb,GAAA;IAAAgB,KAAA,EAanC,SAAAa,kCAAkCA,CAAA;MACxC,IAAIC,UAAU,CAACC,uCAAuC,IAAI,IAAI,EAAE;QAC9D,IAAIvD,QAAQ,CAACwD,EAAE,KAAK,KAAK,IAAIxD,QAAQ,CAACwD,EAAE,KAAK,SAAS,EAAE;UACtDC,OAAO,CAACC,IAAI,CACV,0DAA0D,GACxD,kHAAkH,GAClH,wHAAwH,CAC3H;UACD;SACD,MAAM;UACL,MAAM,IAAIzD,mBAAmB,CAAC,SAAS,EAAE,0BAA0B,CAAC;;;MAGxE,IAAI,IAAI,CAACY,IAAI,IAAI,IAAI,EAAE;QACrB,MAAM,IAAIoC,KAAK,CACb,+GAA+G,CAChH;;MAEH,IAAI,OAAO,IAAI,CAACpC,IAAI,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAIoC,KAAK,CACb,8EAA8E,OAAO,IAAI,CACtFpC,IAAI,sBAAsB,CAC9B;;MAGHyC,UAAU,CAACC,uCAAuC,CAAC,IAAI,CAAC1C,IAAI,EAAE,IAAI,CAACQ,sBAAsB,CAAC;IAC5F;EAAC;IAAAG,GAAA;IAAAgB,KAAA,EAiCD,SAAAmB,wBAAwBA,CAAA;MACtB,IAAI,IAAI,CAAChD,OAAO,EAAE;QAChB,IAAI,CAACK,cAAc,CAAC4C,IAAI,CACtB,IAAI,CAAC3C,aAAa,CAAC4C,WAAW,CAC5B,yBAAyB,EACzB,IAAI,CAACvC,6BAA6B,CACnC,EACD,IAAI,CAACL,aAAa,CAAC4C,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAAClC,+BAA+B,CAAC,CAC1F;QAED,IAAI,CAACX,cAAc,CAAC4C,IAAI,CACtB,IAAI,CAAC3C,aAAa,CAAC4C,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC/B,sBAAsB,CAAC,CAClF;;IAEL;EAAC;IAAAN,GAAA;IAAAgB,KAAA,EAED,SAAAN,mBAAmBA,CAAA;MACjB,IAAI,CAAClB,cAAc,CAAC8C,OAAO,CAAC,UAACC,CAAC;QAAA,OAAKA,CAAC,CAACC,MAAM,EAAE;MAAA,EAAC;MAC9C,IAAI,CAAChD,cAAc,GAAG,EAAE;IAC1B;EAAC;IAAAQ,GAAA;IAAAgB,KAAA,EAqCD,SAAAyB,yBAAyBA,CAACC,sBAAmE;MAC3F,IAAI,CAAC/C,uBAAuB,GAAG+C,sBAAsB;MACrD,IAAI,CAAC/B,cAAc,EAAE;IACvB;EAAC;IAAAX,GAAA;IAAAgB,KAAA,EAOD,SAAA2B,mBAAmBA,CAACC,gBAAgD;MAClE,IAAI,CAAChD,iBAAiB,GAAGgD,gBAAgB;IAC3C;EAAC;IAAA5C,GAAA;IAAAgB,KAAA,EAMD,SAAA6B,wBAAwBA,CAACC,QAA6B;MACpD,IAAI,CAACjD,sBAAsB,GAAGiD,QAAQ;MACtC,IAAI,IAAI,CAACzD,IAAI,IAAI,IAAI,EAAE;QACrB,IAAI,CAACwC,kCAAkC,EAAE;;IAE7C;EAAC;IAAA7B,GAAA;IAAAgB,KAAA;MAAA,IAAA+B,UAAA,GAAAnC,iBAAA,CAID,WACEoC,MAAwB,EAEK;QAAA,IAAAC,MAAA;QAAA,IAD7BC,aAAA,GAAAtB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAuC,EAAE;QAAA,IACzCyB,aAAA,GAAAzB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAyB,IAAI;QAE7B7C,sBAAsB,EAAE;QACxB,IAAI,IAAI,CAACK,QAAQ,EAAE;UACjB,MAAM,IAAIqC,KAAK,CAAC,+BAA+B,CAAC;;QAElD,IAAI,CAAC,IAAI,CAACtC,OAAO,EAAE;UACjB,IAAI,CAACC,QAAQ,GAAG,IAAI;UAEpB,IAAAkE,qBAAA,SACQ1E,+CAA+C,CAACoE,MAAM,EAAEE,aAAa,EAAEG,aAAa,CAAC;YADrFE,YAAY,GAAAD,qBAAA,CAAZC,YAAY;YAAEC,iBAAiB,GAAAF,qBAAA,CAAjBE,iBAAiB;UAIvC,OAAO,IAAIC,OAAO,CAAmB,UAACC,OAAO,EAAEC,MAAM,EAAI;YACvD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,MAAyC,EAAI;cAChE,IAAAC,OAAA,GAAAC,cAAA,CAAsBF,MAAM;gBAArB7D,GAAG,GAAA8D,OAAA;gBAAE7D,MAAM,GAAA6D,OAAA;cAClBb,MAAI,CAAC5D,IAAI,GAAGW,GAAG;cACfiD,MAAI,CAAC9D,OAAO,GAAG,IAAI;cACnB8D,MAAI,CAAC7D,QAAQ,GAAG,KAAK;cACrB6D,MAAI,CAACd,wBAAwB,EAAE;cAC/Bc,MAAI,CAAC/C,uCAAuC,CAACD,MAAM,CAAC;cACpDyD,OAAO,CAACzD,MAAM,CAAC;YACjB,CAAC;YAED,IAAM+D,SAAS,GAAG,SAAZA,SAASA,CAAIxD,KAAY,EAAI;cACjCyC,MAAI,CAAC7D,QAAQ,GAAG,KAAK;cACrBuE,MAAM,CAACnD,KAAK,CAAC;YACf,CAAC;YAED1B,UAAU,CAACmF,YAAY,CAACV,YAAY,EAAEC,iBAAiB,CAAC,CAACU,IAAI,CAACN,WAAW,CAAC,CAACO,KAAK,CAACH,SAAS,CAAC;UAC7F,CAAC,CAAC;SACH,MAAM;UACL,MAAM,IAAIvC,KAAK,CAAC,8BAA8B,CAAC;;MAEnD,CAAC;MAAA,SArCK2C,SAASA,CAAAC,GAAA;QAAA,OAAAtB,UAAA,CAAApB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAATwC,SAAS;IAAA;EAAA;IAAApE,GAAA;IAAAgB,KAAA;MAAA,IAAAsD,YAAA,GAAA1D,iBAAA,CAuCf,aAAiB;QACf,IAAI,IAAI,CAACzB,OAAO,EAAE;UAChB,IAAI,CAACA,OAAO,GAAG,KAAK;UACpB,IAAMa,GAAG,GAAG,IAAI,CAACX,IAAI;UACrB,IAAI,CAACA,IAAI,GAAG,IAAI;UAChB,IAAMY,MAAM,SAASnB,UAAU,CAACyF,cAAc,CAACvE,GAAG,CAAC;UACnD,IAAI,CAACE,uCAAuC,CAACD,MAAM,CAAC;UACpD,IAAI,CAACS,mBAAmB,EAAE;UAC1B,OAAOT,MAAM;SACd,MAAM;UACL,OAAO,IAAI,CAACU,cAAc,EAAE;;MAEhC,CAAC;MAAA,SAZK6D,WAAWA,CAAA;QAAA,OAAAF,YAAA,CAAA3C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX4C,WAAW;IAAA;EAAA;IAAAxE,GAAA;IAAAgB,KAAA;MAAA,IAAAyD,eAAA,GAAA7D,iBAAA,CAgBjB,WAAqBX,MAA6B;QAAA,IAAAyE,MAAA;QAChD/F,0BAA0B,CAACsB,MAAM,CAAC;QAClC,OAAO,IAAI,CAACY,qCAAqC,CAAC;UAAA,OAChD/B,UAAU,CAAC6F,iBAAiB,CAACD,MAAI,CAACrF,IAAI,EAAEY,MAAM,CAAC;QAAA,EAChD;MACH,CAAC;MAAA,SALK2E,cAAcA,CAAAC,GAAA;QAAA,OAAAJ,eAAA,CAAA9C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdgD,cAAc;IAAA;EAAA;IAAA5E,GAAA;IAAAgB,KAAA;MAAA,IAAA8D,YAAA,GAAAlE,iBAAA,CAOpB,aAAoD;QAAA,IAAAmE,MAAA;QAAA,IAAlC9E,MAAA,GAAA2B,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAgC,EAAE;QAClD,IAAI3B,MAAM,CAAC+E,cAAc,IAAI/E,MAAM,CAAC+E,cAAc,KAAK,CAAC,EAAE;UACxD,MAAM,IAAIvD,KAAK,CAAC,8CAA8C,CAAC;;QAGjE,OAAO,IAAI,CAACZ,qCAAqC,CAAC;UAAA,OAChD/B,UAAU,CAACmG,WAAW,CAACF,MAAI,CAAC1F,IAAI,EAAA6F,aAAA,CAAAA,aAAA,KAC3BjF,MAAM;YACT+E,cAAc,EAAE,CAAC;YACjBG,UAAU,EAAE;UAAI,EACjB,CAAC;QAAA,EACH;MACH,CAAC;MAAA,SAZKC,WAAWA,CAAA;QAAA,OAAAN,YAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXwD,WAAW;IAAA;EAAA;AAAA;SA1TNpG,KAAK;AAALA,KAAK,CAcTqG,MAAM;EAAA,IAAAC,KAAA,GAAA1E,iBAAA,CAAG,WACdoC,MAAwB,EAIA;IAAA,IAHxBE,aAAA,GAAAtB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAuC,EAAE;IAAA,IACzCc,sBAAA,GAAAd,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAsE,IAAI;IAAA,IAC1EyB,aAAA,GAAAzB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAyB,IAAI;IAE7BK,OAAO,CAACC,IAAI,CACV,2GAA2G,CAC5G;IACD,OAAOlD,MAAK,CAACuG,WAAW,CAACvC,MAAM,EAAEE,aAAa,EAAER,sBAAsB,EAAEW,aAAa,CAAC;EACxF,CAAC;EAAA,iBAAAmC,GAAA;IAAA,OAAAF,KAAA,CAAA3D,KAAA,OAAAC,SAAA;EAAA;AAAA;AAxBU5C,KAAK,CAsETuG,WAAW;EAAA,IAAAE,KAAA,GAAA7E,iBAAA,CAAG,WACnBoC,MAAwB,EAIA;IAAA,IAHxBE,aAAA,GAAAtB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAuC,EAAE;IAAA,IACzCc,sBAAA,GAAAd,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAsE,IAAI;IAAA,IAC1EyB,aAAA,GAAAzB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,MAAyB,IAAI;IAE7B,IAAM8D,KAAK,GAAU,IAAI1G,MAAK,EAAE;IAChC0G,KAAK,CAACjD,yBAAyB,CAACC,sBAAsB,CAAC;IACvD,IAAMzC,MAAM,SAA2ByF,KAAK,CAACtB,SAAS,CAACpB,MAAM,EAAEE,aAAa,EAAEG,aAAa,CAAC;IAC5F,OAAO;MAAEqC,KAAK,EAALA,KAAK;MAAEzF,MAAM,EAANA;IAAM,CAAE;EAC1B,CAAC;EAAA,iBAAA0F,GAAA;IAAA,OAAAF,KAAA,CAAA9D,KAAA,OAAAC,SAAA;EAAA;AAAA;AAiRHgE,MAAM,CAACC,MAAM,CAAC7G,KAAK,CAAC8G,SAAS,EAAEpH,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}