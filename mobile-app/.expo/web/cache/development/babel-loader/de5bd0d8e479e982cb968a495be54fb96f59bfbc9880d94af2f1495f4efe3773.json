{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState, useEffect } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport KeyboardAvoidingView from \"react-native-web/dist/exports/KeyboardAvoidingView\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport { Ionicons } from '@expo/vector-icons';\nimport { Audio } from 'expo-av';\nimport * as ImagePicker from 'expo-image-picker';\nimport io from 'socket.io-client';\nimport ReactiveBlob from \"../components/ReactiveBlob\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SERVER_URL = 'http://localhost:3000';\nvar Agent = function Agent() {\n  var _useState = useState('idle'),\n    _useState2 = _slicedToArray(_useState, 2),\n    blobState = _useState2[0],\n    setBlobState = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    recording = _useState4[0],\n    setRecording = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    textInput = _useState6[0],\n    setTextInput = _useState6[1];\n  var _useState7 = useState(null),\n    _useState8 = _slicedToArray(_useState7, 2),\n    socket = _useState8[0],\n    setSocket = _useState8[1];\n  useEffect(function () {\n    var newSocket = io(SERVER_URL);\n    setSocket(newSocket);\n    newSocket.on('connect', function () {\n      console.log('Connected to server');\n    });\n    newSocket.on('newMessage', function (message) {\n      console.log('New message received:', message);\n      if (blobState === 'thinking') {\n        setBlobState('idle');\n      }\n    });\n    return function () {\n      return newSocket.close();\n    };\n  }, []);\n  var startRecording = function () {\n    var _ref = _asyncToGenerator(function* () {\n      try {\n        var permission = yield Audio.requestPermissionsAsync();\n        if (permission.status !== 'granted') {\n          Alert.alert('Permission required', 'Please grant microphone permission');\n          return;\n        }\n        yield Audio.setAudioModeAsync({\n          allowsRecordingIOS: true,\n          playsInSilentModeIOS: true\n        });\n        setBlobState('recording');\n        var _yield$Audio$Recordin = yield Audio.Recording.createAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY),\n          _recording = _yield$Audio$Recordin.recording;\n        setRecording(_recording);\n        setTimeout(function () {\n          if (_recording) {\n            stopRecording();\n          }\n        }, 5000);\n      } catch (err) {\n        console.error('Failed to start recording', err);\n        setBlobState('idle');\n      }\n    });\n    return function startRecording() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  var stopRecording = function () {\n    var _ref2 = _asyncToGenerator(function* () {\n      if (!recording) return;\n      setBlobState('thinking');\n      setRecording(null);\n      try {\n        yield recording.stopAndUnloadAsync();\n        var uri = recording.getURI();\n        if (uri) {\n          yield uploadAudio(uri);\n        }\n      } catch (error) {\n        console.error('Failed to stop recording', error);\n        setBlobState('idle');\n      }\n    });\n    return function stopRecording() {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  var uploadAudio = function () {\n    var _ref3 = _asyncToGenerator(function* (uri) {\n      try {\n        var formData = new FormData();\n        formData.append('file', {\n          uri: uri,\n          type: 'audio/wav',\n          name: 'recording.wav'\n        });\n        var response = yield fetch(`${SERVER_URL}/upload`, {\n          method: 'POST',\n          body: formData,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (response.ok) {\n          var result = yield response.json();\n          console.log('Audio uploaded successfully:', result);\n        } else {\n          throw new Error('Upload failed');\n        }\n      } catch (error) {\n        console.error('Audio upload error:', error);\n        Alert.alert('Error', 'Failed to upload audio');\n      } finally {\n        setBlobState('idle');\n      }\n    });\n    return function uploadAudio(_x) {\n      return _ref3.apply(this, arguments);\n    };\n  }();\n  var pickImage = function () {\n    var _ref4 = _asyncToGenerator(function* () {\n      var result = yield ImagePicker.launchImageLibraryAsync({\n        mediaTypes: ImagePicker.MediaTypeOptions.Images,\n        allowsEditing: true,\n        aspect: [4, 3],\n        quality: 1\n      });\n      if (!result.canceled && result.assets[0]) {\n        setBlobState('thinking');\n        yield uploadImage(result.assets[0].uri);\n      }\n    });\n    return function pickImage() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  var uploadImage = function () {\n    var _ref5 = _asyncToGenerator(function* (uri) {\n      try {\n        var formData = new FormData();\n        formData.append('file', {\n          uri: uri,\n          type: 'image/jpeg',\n          name: 'image.jpg'\n        });\n        var response = yield fetch(`${SERVER_URL}/upload`, {\n          method: 'POST',\n          body: formData,\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        if (response.ok) {\n          var result = yield response.json();\n          console.log('Image uploaded successfully:', result);\n        } else {\n          throw new Error('Upload failed');\n        }\n      } catch (error) {\n        console.error('Image upload error:', error);\n        Alert.alert('Error', 'Failed to upload image');\n      } finally {\n        setBlobState('idle');\n      }\n    });\n    return function uploadImage(_x2) {\n      return _ref5.apply(this, arguments);\n    };\n  }();\n  var sendTextMessage = function () {\n    var _ref6 = _asyncToGenerator(function* () {\n      if (!textInput.trim()) return;\n      try {\n        var response = yield fetch(`${SERVER_URL}/message`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            from: 'patient',\n            subtype: 'text',\n            payload: {\n              text: textInput.trim()\n            }\n          })\n        });\n        if (response.ok) {\n          setTextInput('');\n        } else {\n          throw new Error('Send failed');\n        }\n      } catch (error) {\n        console.error('Text message error:', error);\n        Alert.alert('Error', 'Failed to send message');\n      }\n    });\n    return function sendTextMessage() {\n      return _ref6.apply(this, arguments);\n    };\n  }();\n  return _jsxs(KeyboardAvoidingView, {\n    style: styles.container,\n    behavior: Platform.OS === 'ios' ? 'padding' : 'height',\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: \"Symptom-OS\"\n      }), _jsx(Text, {\n        style: styles.subtitle,\n        children: \"How are you feeling today?\"\n      })]\n    }), _jsxs(View, {\n      style: styles.blobContainer,\n      children: [_jsx(ReactiveBlob, {\n        state: blobState\n      }), _jsxs(Text, {\n        style: styles.stateText,\n        children: [blobState === 'idle' && 'Tap to record your symptoms', blobState === 'recording' && 'Recording... speak now', blobState === 'thinking' && 'Processing your input...']\n      })]\n    }), _jsxs(View, {\n      style: styles.controls,\n      children: [_jsx(TouchableOpacity, {\n        style: [styles.micButton, blobState === 'recording' && styles.recordingButton],\n        onPress: blobState === 'recording' ? stopRecording : startRecording,\n        disabled: blobState === 'thinking',\n        children: _jsx(Ionicons, {\n          name: blobState === 'recording' ? 'stop' : 'mic',\n          size: 32,\n          color: \"white\"\n        })\n      }), _jsx(TouchableOpacity, {\n        style: styles.imageButton,\n        onPress: pickImage,\n        disabled: blobState !== 'idle',\n        children: _jsx(Ionicons, {\n          name: \"camera\",\n          size: 24,\n          color: \"#007AFF\"\n        })\n      })]\n    }), _jsxs(View, {\n      style: styles.textInputContainer,\n      children: [_jsx(TextInput, {\n        style: styles.textInput,\n        placeholder: \"Or type your message here...\",\n        value: textInput,\n        onChangeText: setTextInput,\n        multiline: true\n      }), _jsx(TouchableOpacity, {\n        style: [styles.sendButton, !textInput.trim() && styles.sendButtonDisabled],\n        onPress: sendTextMessage,\n        disabled: !textInput.trim(),\n        children: _jsx(Ionicons, {\n          name: \"send\",\n          size: 20,\n          color: textInput.trim() ? '#007AFF' : '#C7C7CC'\n        })\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#F8F9FA',\n    paddingTop: 60\n  },\n  header: {\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 40\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#1A1A1A',\n    marginBottom: 8\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center'\n  },\n  blobContainer: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingHorizontal: 20\n  },\n  stateText: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n    marginTop: 20\n  },\n  controls: {\n    flexDirection: 'row',\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 20,\n    gap: 20\n  },\n  micButton: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: '#007AFF',\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2\n    },\n    shadowOpacity: 0.25,\n    shadowRadius: 4,\n    elevation: 5\n  },\n  recordingButton: {\n    backgroundColor: '#FF3B30'\n  },\n  imageButton: {\n    width: 50,\n    height: 50,\n    borderRadius: 25,\n    backgroundColor: '#F0F0F0',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 1,\n    borderColor: '#E0E0E0'\n  },\n  textInputContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    paddingHorizontal: 20,\n    paddingBottom: 20,\n    gap: 10\n  },\n  textInput: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: '#E0E0E0',\n    borderRadius: 20,\n    paddingHorizontal: 16,\n    paddingVertical: 12,\n    backgroundColor: 'white',\n    maxHeight: 100,\n    fontSize: 16\n  },\n  sendButton: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    alignItems: 'center',\n    justifyContent: 'center'\n  },\n  sendButtonDisabled: {\n    opacity: 0.5\n  }\n});\nexport default Agent;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "View", "Text", "TouchableOpacity", "StyleSheet", "<PERSON><PERSON>", "TextInput", "KeyboardAvoidingView", "Platform", "Ionicons", "Audio", "ImagePicker", "io", "ReactiveBlob", "jsx", "_jsx", "jsxs", "_jsxs", "SERVER_URL", "Agent", "_useState", "_useState2", "_slicedToArray", "blobState", "setBlobState", "_useState3", "_useState4", "recording", "setRecording", "_useState5", "_useState6", "textInput", "setTextInput", "_useState7", "_useState8", "socket", "setSocket", "newSocket", "on", "console", "log", "message", "close", "startRecording", "_ref", "_asyncToGenerator", "permission", "requestPermissionsAsync", "status", "alert", "setAudioModeAsync", "allowsRecordingIOS", "playsInSilentModeIOS", "_yield$Audio$Recordin", "Recording", "createAsync", "RecordingOptionsPresets", "HIGH_QUALITY", "setTimeout", "stopRecording", "err", "error", "apply", "arguments", "_ref2", "stopAndUnloadAsync", "uri", "getURI", "uploadAudio", "_ref3", "formData", "FormData", "append", "type", "name", "response", "fetch", "method", "body", "headers", "ok", "result", "json", "Error", "_x", "pickImage", "_ref4", "launchImageLibraryAsync", "mediaTypes", "MediaTypeOptions", "Images", "allowsEditing", "aspect", "quality", "canceled", "assets", "uploadImage", "_ref5", "_x2", "sendTextMessage", "_ref6", "trim", "JSON", "stringify", "from", "subtype", "payload", "text", "style", "styles", "container", "behavior", "OS", "children", "header", "title", "subtitle", "blobC<PERSON>r", "state", "stateText", "controls", "mi<PERSON><PERSON><PERSON><PERSON>", "recordingButton", "onPress", "disabled", "size", "color", "imageButton", "textInputContainer", "placeholder", "value", "onChangeText", "multiline", "sendButton", "sendButtonDisabled", "create", "flex", "backgroundColor", "paddingTop", "alignItems", "paddingHorizontal", "marginBottom", "fontSize", "fontWeight", "textAlign", "justifyContent", "marginTop", "flexDirection", "gap", "width", "height", "borderRadius", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "borderWidth", "borderColor", "paddingBottom", "paddingVertical", "maxHeight", "opacity"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/screens/Agent.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  View,\n  Text,\n  TouchableOpacity,\n  StyleSheet,\n  Alert,\n  TextInput,\n  KeyboardAvoidingView,\n  Platform,\n} from 'react-native';\nimport { Ionicons } from '@expo/vector-icons';\nimport { Audio } from 'expo-av';\nimport * as ImagePicker from 'expo-image-picker';\nimport io from 'socket.io-client';\nimport ReactiveBlob from '../components/ReactiveBlob';\n\nconst SERVER_URL = 'http://localhost:3000';\n\ntype BlobState = 'idle' | 'recording' | 'thinking';\n\nconst Agent: React.FC = () => {\n  const [blobState, setBlobState] = useState<BlobState>('idle');\n  const [recording, setRecording] = useState<Audio.Recording | null>(null);\n  const [textInput, setTextInput] = useState('');\n  const [socket, setSocket] = useState<any>(null);\n\n  useEffect(() => {\n    // Initialize socket connection\n    const newSocket = io(SERVER_URL);\n    setSocket(newSocket);\n\n    newSocket.on('connect', () => {\n      console.log('Connected to server');\n    });\n\n    newSocket.on('newMessage', (message: any) => {\n      console.log('New message received:', message);\n      if (blobState === 'thinking') {\n        setBlobState('idle');\n      }\n    });\n\n    return () => newSocket.close();\n  }, []);\n\n  const startRecording = async () => {\n    try {\n      const permission = await Audio.requestPermissionsAsync();\n      if (permission.status !== 'granted') {\n        Alert.alert('Permission required', 'Please grant microphone permission');\n        return;\n      }\n\n      await Audio.setAudioModeAsync({\n        allowsRecordingIOS: true,\n        playsInSilentModeIOS: true,\n      });\n\n      setBlobState('recording');\n      const { recording } = await Audio.Recording.createAsync(\n        Audio.RecordingOptionsPresets.HIGH_QUALITY\n      );\n      setRecording(recording);\n\n      // Auto-stop after 5 seconds for demo\n      setTimeout(() => {\n        if (recording) {\n          stopRecording();\n        }\n      }, 5000);\n    } catch (err) {\n      console.error('Failed to start recording', err);\n      setBlobState('idle');\n    }\n  };\n\n  const stopRecording = async () => {\n    if (!recording) return;\n\n    setBlobState('thinking');\n    setRecording(null);\n\n    try {\n      await recording.stopAndUnloadAsync();\n      const uri = recording.getURI();\n      \n      if (uri) {\n        await uploadAudio(uri);\n      }\n    } catch (error) {\n      console.error('Failed to stop recording', error);\n      setBlobState('idle');\n    }\n  };\n\n  const uploadAudio = async (uri: string) => {\n    try {\n      const formData = new FormData();\n      formData.append('file', {\n        uri,\n        type: 'audio/wav',\n        name: 'recording.wav',\n      } as any);\n\n      const response = await fetch(`${SERVER_URL}/upload`, {\n        method: 'POST',\n        body: formData,\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('Audio uploaded successfully:', result);\n      } else {\n        throw new Error('Upload failed');\n      }\n    } catch (error) {\n      console.error('Audio upload error:', error);\n      Alert.alert('Error', 'Failed to upload audio');\n    } finally {\n      setBlobState('idle');\n    }\n  };\n\n  const pickImage = async () => {\n    const result = await ImagePicker.launchImageLibraryAsync({\n      mediaTypes: ImagePicker.MediaTypeOptions.Images,\n      allowsEditing: true,\n      aspect: [4, 3],\n      quality: 1,\n    });\n\n    if (!result.canceled && result.assets[0]) {\n      setBlobState('thinking');\n      await uploadImage(result.assets[0].uri);\n    }\n  };\n\n  const uploadImage = async (uri: string) => {\n    try {\n      const formData = new FormData();\n      formData.append('file', {\n        uri,\n        type: 'image/jpeg',\n        name: 'image.jpg',\n      } as any);\n\n      const response = await fetch(`${SERVER_URL}/upload`, {\n        method: 'POST',\n        body: formData,\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('Image uploaded successfully:', result);\n      } else {\n        throw new Error('Upload failed');\n      }\n    } catch (error) {\n      console.error('Image upload error:', error);\n      Alert.alert('Error', 'Failed to upload image');\n    } finally {\n      setBlobState('idle');\n    }\n  };\n\n  const sendTextMessage = async () => {\n    if (!textInput.trim()) return;\n\n    try {\n      const response = await fetch(`${SERVER_URL}/message`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          from: 'patient',\n          subtype: 'text',\n          payload: { text: textInput.trim() },\n        }),\n      });\n\n      if (response.ok) {\n        setTextInput('');\n      } else {\n        throw new Error('Send failed');\n      }\n    } catch (error) {\n      console.error('Text message error:', error);\n      Alert.alert('Error', 'Failed to send message');\n    }\n  };\n\n  return (\n    <KeyboardAvoidingView \n      style={styles.container} \n      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n    >\n      <View style={styles.header}>\n        <Text style={styles.title}>Symptom-OS</Text>\n        <Text style={styles.subtitle}>How are you feeling today?</Text>\n      </View>\n\n      <View style={styles.blobContainer}>\n        <ReactiveBlob state={blobState} />\n        \n        <Text style={styles.stateText}>\n          {blobState === 'idle' && 'Tap to record your symptoms'}\n          {blobState === 'recording' && 'Recording... speak now'}\n          {blobState === 'thinking' && 'Processing your input...'}\n        </Text>\n      </View>\n\n      <View style={styles.controls}>\n        <TouchableOpacity\n          style={[styles.micButton, blobState === 'recording' && styles.recordingButton]}\n          onPress={blobState === 'recording' ? stopRecording : startRecording}\n          disabled={blobState === 'thinking'}\n        >\n          <Ionicons \n            name={blobState === 'recording' ? 'stop' : 'mic'} \n            size={32} \n            color=\"white\" \n          />\n        </TouchableOpacity>\n\n        <TouchableOpacity\n          style={styles.imageButton}\n          onPress={pickImage}\n          disabled={blobState !== 'idle'}\n        >\n          <Ionicons name=\"camera\" size={24} color=\"#007AFF\" />\n        </TouchableOpacity>\n      </View>\n\n      <View style={styles.textInputContainer}>\n        <TextInput\n          style={styles.textInput}\n          placeholder=\"Or type your message here...\"\n          value={textInput}\n          onChangeText={setTextInput}\n          multiline\n        />\n        <TouchableOpacity\n          style={[styles.sendButton, !textInput.trim() && styles.sendButtonDisabled]}\n          onPress={sendTextMessage}\n          disabled={!textInput.trim()}\n        >\n          <Ionicons name=\"send\" size={20} color={textInput.trim() ? '#007AFF' : '#C7C7CC'} />\n        </TouchableOpacity>\n      </View>\n    </KeyboardAvoidingView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#F8F9FA',\n    paddingTop: 60,\n  },\n  header: {\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 40,\n  },\n  title: {\n    fontSize: 28,\n    fontWeight: 'bold',\n    color: '#1A1A1A',\n    marginBottom: 8,\n  },\n  subtitle: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n  },\n  blobContainer: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    paddingHorizontal: 20,\n  },\n  stateText: {\n    fontSize: 16,\n    color: '#666',\n    textAlign: 'center',\n    marginTop: 20,\n  },\n  controls: {\n    flexDirection: 'row',\n    justifyContent: 'center',\n    alignItems: 'center',\n    paddingHorizontal: 20,\n    marginBottom: 20,\n    gap: 20,\n  },\n  micButton: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: '#007AFF',\n    alignItems: 'center',\n    justifyContent: 'center',\n    shadowColor: '#000',\n    shadowOffset: { width: 0, height: 2 },\n    shadowOpacity: 0.25,\n    shadowRadius: 4,\n    elevation: 5,\n  },\n  recordingButton: {\n    backgroundColor: '#FF3B30',\n  },\n  imageButton: {\n    width: 50,\n    height: 50,\n    borderRadius: 25,\n    backgroundColor: '#F0F0F0',\n    alignItems: 'center',\n    justifyContent: 'center',\n    borderWidth: 1,\n    borderColor: '#E0E0E0',\n  },\n  textInputContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    paddingHorizontal: 20,\n    paddingBottom: 20,\n    gap: 10,\n  },\n  textInput: {\n    flex: 1,\n    borderWidth: 1,\n    borderColor: '#E0E0E0',\n    borderRadius: 20,\n    paddingHorizontal: 16,\n    paddingVertical: 12,\n    backgroundColor: 'white',\n    maxHeight: 100,\n    fontSize: 16,\n  },\n  sendButton: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  sendButtonDisabled: {\n    opacity: 0.5,\n  },\n});\n\nexport default Agent;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,oBAAA;AAAA,OAAAC,QAAA;AAWnD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,OAAOC,EAAE,MAAM,kBAAkB;AACjC,OAAOC,YAAY;AAAmC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtD,IAAMC,UAAU,GAAG,uBAAuB;AAI1C,IAAMC,KAAe,GAAG,SAAlBA,KAAeA,CAAA,EAAS;EAC5B,IAAAC,SAAA,GAAkCrB,QAAQ,CAAY,MAAM,CAAC;IAAAsB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAtDG,SAAS,GAAAF,UAAA;IAAEG,YAAY,GAAAH,UAAA;EAC9B,IAAAI,UAAA,GAAkC1B,QAAQ,CAAyB,IAAI,CAAC;IAAA2B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAjEE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAAkC9B,QAAQ,CAAC,EAAE,CAAC;IAAA+B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAvCE,SAAS,GAAAD,UAAA;IAAEE,YAAY,GAAAF,UAAA;EAC9B,IAAAG,UAAA,GAA4BlC,QAAQ,CAAM,IAAI,CAAC;IAAAmC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAxCE,MAAM,GAAAD,UAAA;IAAEE,SAAS,GAAAF,UAAA;EAExBlC,SAAS,CAAC,YAAM;IAEd,IAAMqC,SAAS,GAAGzB,EAAE,CAACM,UAAU,CAAC;IAChCkB,SAAS,CAACC,SAAS,CAAC;IAEpBA,SAAS,CAACC,EAAE,CAAC,SAAS,EAAE,YAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IACpC,CAAC,CAAC;IAEFH,SAAS,CAACC,EAAE,CAAC,YAAY,EAAE,UAACG,OAAY,EAAK;MAC3CF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,OAAO,CAAC;MAC7C,IAAIlB,SAAS,KAAK,UAAU,EAAE;QAC5BC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMa,SAAS,CAACK,KAAK,CAAC,CAAC;IAAA;EAChC,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMC,cAAc;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;MACjC,IAAI;QACF,IAAMC,UAAU,SAASpC,KAAK,CAACqC,uBAAuB,CAAC,CAAC;QACxD,IAAID,UAAU,CAACE,MAAM,KAAK,SAAS,EAAE;UACnC3C,KAAK,CAAC4C,KAAK,CAAC,qBAAqB,EAAE,oCAAoC,CAAC;UACxE;QACF;QAEA,MAAMvC,KAAK,CAACwC,iBAAiB,CAAC;UAC5BC,kBAAkB,EAAE,IAAI;UACxBC,oBAAoB,EAAE;QACxB,CAAC,CAAC;QAEF5B,YAAY,CAAC,WAAW,CAAC;QACzB,IAAA6B,qBAAA,SAA4B3C,KAAK,CAAC4C,SAAS,CAACC,WAAW,CACrD7C,KAAK,CAAC8C,uBAAuB,CAACC,YAChC,CAAC;UAFO9B,UAAS,GAAA0B,qBAAA,CAAT1B,SAAS;QAGjBC,YAAY,CAACD,UAAS,CAAC;QAGvB+B,UAAU,CAAC,YAAM;UACf,IAAI/B,UAAS,EAAE;YACbgC,aAAa,CAAC,CAAC;UACjB;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZrB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAED,GAAG,CAAC;QAC/CpC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBA7BKmB,cAAcA,CAAA;MAAA,OAAAC,IAAA,CAAAkB,KAAA,OAAAC,SAAA;IAAA;EAAA,GA6BnB;EAED,IAAMJ,aAAa;IAAA,IAAAK,KAAA,GAAAnB,iBAAA,CAAG,aAAY;MAChC,IAAI,CAAClB,SAAS,EAAE;MAEhBH,YAAY,CAAC,UAAU,CAAC;MACxBI,YAAY,CAAC,IAAI,CAAC;MAElB,IAAI;QACF,MAAMD,SAAS,CAACsC,kBAAkB,CAAC,CAAC;QACpC,IAAMC,GAAG,GAAGvC,SAAS,CAACwC,MAAM,CAAC,CAAC;QAE9B,IAAID,GAAG,EAAE;UACP,MAAME,WAAW,CAACF,GAAG,CAAC;QACxB;MACF,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDrC,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBAjBKmC,aAAaA,CAAA;MAAA,OAAAK,KAAA,CAAAF,KAAA,OAAAC,SAAA;IAAA;EAAA,GAiBlB;EAED,IAAMK,WAAW;IAAA,IAAAC,KAAA,GAAAxB,iBAAA,CAAG,WAAOqB,GAAW,EAAK;MACzC,IAAI;QACF,IAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE;UACtBN,GAAG,EAAHA,GAAG;UACHO,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;QACR,CAAQ,CAAC;QAET,IAAMC,QAAQ,SAASC,KAAK,CAAC,GAAG1D,UAAU,SAAS,EAAE;UACnD2D,MAAM,EAAE,MAAM;UACdC,IAAI,EAAER,QAAQ;UACdS,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;UACf,IAAMC,MAAM,SAASN,QAAQ,CAACO,IAAI,CAAC,CAAC;UACpC3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyC,MAAM,CAAC;QACrD,CAAC,MAAM;UACL,MAAM,IAAIE,KAAK,CAAC,eAAe,CAAC;QAClC;MACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxD,KAAK,CAAC4C,KAAK,CAAC,OAAO,EAAE,wBAAwB,CAAC;MAChD,CAAC,SAAS;QACRzB,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBA7BK4C,WAAWA,CAAAgB,EAAA;MAAA,OAAAf,KAAA,CAAAP,KAAA,OAAAC,SAAA;IAAA;EAAA,GA6BhB;EAED,IAAMsB,SAAS;IAAA,IAAAC,KAAA,GAAAzC,iBAAA,CAAG,aAAY;MAC5B,IAAMoC,MAAM,SAAStE,WAAW,CAAC4E,uBAAuB,CAAC;QACvDC,UAAU,EAAE7E,WAAW,CAAC8E,gBAAgB,CAACC,MAAM;QAC/CC,aAAa,EAAE,IAAI;QACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,IAAI,CAACZ,MAAM,CAACa,QAAQ,IAAIb,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,EAAE;QACxCvE,YAAY,CAAC,UAAU,CAAC;QACxB,MAAMwE,WAAW,CAACf,MAAM,CAACc,MAAM,CAAC,CAAC,CAAC,CAAC7B,GAAG,CAAC;MACzC;IACF,CAAC;IAAA,gBAZKmB,SAASA,CAAA;MAAA,OAAAC,KAAA,CAAAxB,KAAA,OAAAC,SAAA;IAAA;EAAA,GAYd;EAED,IAAMiC,WAAW;IAAA,IAAAC,KAAA,GAAApD,iBAAA,CAAG,WAAOqB,GAAW,EAAK;MACzC,IAAI;QACF,IAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE;UACtBN,GAAG,EAAHA,GAAG;UACHO,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE;QACR,CAAQ,CAAC;QAET,IAAMC,QAAQ,SAASC,KAAK,CAAC,GAAG1D,UAAU,SAAS,EAAE;UACnD2D,MAAM,EAAE,MAAM;UACdC,IAAI,EAAER,QAAQ;UACdS,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;UACf,IAAMC,MAAM,SAASN,QAAQ,CAACO,IAAI,CAAC,CAAC;UACpC3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyC,MAAM,CAAC;QACrD,CAAC,MAAM;UACL,MAAM,IAAIE,KAAK,CAAC,eAAe,CAAC;QAClC;MACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxD,KAAK,CAAC4C,KAAK,CAAC,OAAO,EAAE,wBAAwB,CAAC;MAChD,CAAC,SAAS;QACRzB,YAAY,CAAC,MAAM,CAAC;MACtB;IACF,CAAC;IAAA,gBA7BKwE,WAAWA,CAAAE,GAAA;MAAA,OAAAD,KAAA,CAAAnC,KAAA,OAAAC,SAAA;IAAA;EAAA,GA6BhB;EAED,IAAMoC,eAAe;IAAA,IAAAC,KAAA,GAAAvD,iBAAA,CAAG,aAAY;MAClC,IAAI,CAACd,SAAS,CAACsE,IAAI,CAAC,CAAC,EAAE;MAEvB,IAAI;QACF,IAAM1B,QAAQ,SAASC,KAAK,CAAC,GAAG1D,UAAU,UAAU,EAAE;UACpD2D,MAAM,EAAE,MAAM;UACdE,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDD,IAAI,EAAEwB,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,MAAM;YACfC,OAAO,EAAE;cAAEC,IAAI,EAAE5E,SAAS,CAACsE,IAAI,CAAC;YAAE;UACpC,CAAC;QACH,CAAC,CAAC;QAEF,IAAI1B,QAAQ,CAACK,EAAE,EAAE;UACfhD,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACL,MAAM,IAAImD,KAAK,CAAC,aAAa,CAAC;QAChC;MACF,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CxD,KAAK,CAAC4C,KAAK,CAAC,OAAO,EAAE,wBAAwB,CAAC;MAChD;IACF,CAAC;IAAA,gBAzBKkD,eAAeA,CAAA;MAAA,OAAAC,KAAA,CAAAtC,KAAA,OAAAC,SAAA;IAAA;EAAA,GAyBpB;EAED,OACE9C,KAAA,CAACV,oBAAoB;IACnBqG,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBC,QAAQ,EAAEvG,QAAQ,CAACwG,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,QAAS;IAAAC,QAAA,GAEvDhG,KAAA,CAAChB,IAAI;MAAC2G,KAAK,EAAEC,MAAM,CAACK,MAAO;MAAAD,QAAA,GACzBlG,IAAA,CAACb,IAAI;QAAC0G,KAAK,EAAEC,MAAM,CAACM,KAAM;QAAAF,QAAA,EAAC;MAAU,CAAM,CAAC,EAC5ClG,IAAA,CAACb,IAAI;QAAC0G,KAAK,EAAEC,MAAM,CAACO,QAAS;QAAAH,QAAA,EAAC;MAA0B,CAAM,CAAC;IAAA,CAC3D,CAAC,EAEPhG,KAAA,CAAChB,IAAI;MAAC2G,KAAK,EAAEC,MAAM,CAACQ,aAAc;MAAAJ,QAAA,GAChClG,IAAA,CAACF,YAAY;QAACyG,KAAK,EAAE/F;MAAU,CAAE,CAAC,EAElCN,KAAA,CAACf,IAAI;QAAC0G,KAAK,EAAEC,MAAM,CAACU,SAAU;QAAAN,QAAA,GAC3B1F,SAAS,KAAK,MAAM,IAAI,6BAA6B,EACrDA,SAAS,KAAK,WAAW,IAAI,wBAAwB,EACrDA,SAAS,KAAK,UAAU,IAAI,0BAA0B;MAAA,CACnD,CAAC;IAAA,CACH,CAAC,EAEPN,KAAA,CAAChB,IAAI;MAAC2G,KAAK,EAAEC,MAAM,CAACW,QAAS;MAAAP,QAAA,GAC3BlG,IAAA,CAACZ,gBAAgB;QACfyG,KAAK,EAAE,CAACC,MAAM,CAACY,SAAS,EAAElG,SAAS,KAAK,WAAW,IAAIsF,MAAM,CAACa,eAAe,CAAE;QAC/EC,OAAO,EAAEpG,SAAS,KAAK,WAAW,GAAGoC,aAAa,GAAGhB,cAAe;QACpEiF,QAAQ,EAAErG,SAAS,KAAK,UAAW;QAAA0F,QAAA,EAEnClG,IAAA,CAACN,QAAQ;UACPiE,IAAI,EAAEnD,SAAS,KAAK,WAAW,GAAG,MAAM,GAAG,KAAM;UACjDsG,IAAI,EAAE,EAAG;UACTC,KAAK,EAAC;QAAO,CACd;MAAC,CACc,CAAC,EAEnB/G,IAAA,CAACZ,gBAAgB;QACfyG,KAAK,EAAEC,MAAM,CAACkB,WAAY;QAC1BJ,OAAO,EAAEtC,SAAU;QACnBuC,QAAQ,EAAErG,SAAS,KAAK,MAAO;QAAA0F,QAAA,EAE/BlG,IAAA,CAACN,QAAQ;UAACiE,IAAI,EAAC,QAAQ;UAACmD,IAAI,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS,CAAE;MAAC,CACpC,CAAC;IAAA,CACf,CAAC,EAEP7G,KAAA,CAAChB,IAAI;MAAC2G,KAAK,EAAEC,MAAM,CAACmB,kBAAmB;MAAAf,QAAA,GACrClG,IAAA,CAACT,SAAS;QACRsG,KAAK,EAAEC,MAAM,CAAC9E,SAAU;QACxBkG,WAAW,EAAC,8BAA8B;QAC1CC,KAAK,EAAEnG,SAAU;QACjBoG,YAAY,EAAEnG,YAAa;QAC3BoG,SAAS;MAAA,CACV,CAAC,EACFrH,IAAA,CAACZ,gBAAgB;QACfyG,KAAK,EAAE,CAACC,MAAM,CAACwB,UAAU,EAAE,CAACtG,SAAS,CAACsE,IAAI,CAAC,CAAC,IAAIQ,MAAM,CAACyB,kBAAkB,CAAE;QAC3EX,OAAO,EAAExB,eAAgB;QACzByB,QAAQ,EAAE,CAAC7F,SAAS,CAACsE,IAAI,CAAC,CAAE;QAAAY,QAAA,EAE5BlG,IAAA,CAACN,QAAQ;UAACiE,IAAI,EAAC,MAAM;UAACmD,IAAI,EAAE,EAAG;UAACC,KAAK,EAAE/F,SAAS,CAACsE,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG;QAAU,CAAE;MAAC,CACnE,CAAC;IAAA,CACf,CAAC;EAAA,CACa,CAAC;AAE3B,CAAC;AAED,IAAMQ,MAAM,GAAGzG,UAAU,CAACmI,MAAM,CAAC;EAC/BzB,SAAS,EAAE;IACT0B,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE,SAAS;IAC1BC,UAAU,EAAE;EACd,CAAC;EACDxB,MAAM,EAAE;IACNyB,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE;EAChB,CAAC;EACD1B,KAAK,EAAE;IACL2B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBjB,KAAK,EAAE,SAAS;IAChBe,YAAY,EAAE;EAChB,CAAC;EACDzB,QAAQ,EAAE;IACR0B,QAAQ,EAAE,EAAE;IACZhB,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE;EACb,CAAC;EACD3B,aAAa,EAAE;IACbmB,IAAI,EAAE,CAAC;IACPG,UAAU,EAAE,QAAQ;IACpBM,cAAc,EAAE,QAAQ;IACxBL,iBAAiB,EAAE;EACrB,CAAC;EACDrB,SAAS,EAAE;IACTuB,QAAQ,EAAE,EAAE;IACZhB,KAAK,EAAE,MAAM;IACbkB,SAAS,EAAE,QAAQ;IACnBE,SAAS,EAAE;EACb,CAAC;EACD1B,QAAQ,EAAE;IACR2B,aAAa,EAAE,KAAK;IACpBF,cAAc,EAAE,QAAQ;IACxBN,UAAU,EAAE,QAAQ;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,YAAY,EAAE,EAAE;IAChBO,GAAG,EAAE;EACP,CAAC;EACD3B,SAAS,EAAE;IACT4B,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBd,eAAe,EAAE,SAAS;IAC1BE,UAAU,EAAE,QAAQ;IACpBM,cAAc,EAAE,QAAQ;IACxBO,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MAAEJ,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACrCI,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlC,eAAe,EAAE;IACfe,eAAe,EAAE;EACnB,CAAC;EACDV,WAAW,EAAE;IACXsB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBd,eAAe,EAAE,SAAS;IAC1BE,UAAU,EAAE,QAAQ;IACpBM,cAAc,EAAE,QAAQ;IACxBY,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACD9B,kBAAkB,EAAE;IAClBmB,aAAa,EAAE,KAAK;IACpBR,UAAU,EAAE,UAAU;IACtBC,iBAAiB,EAAE,EAAE;IACrBmB,aAAa,EAAE,EAAE;IACjBX,GAAG,EAAE;EACP,CAAC;EACDrH,SAAS,EAAE;IACTyG,IAAI,EAAE,CAAC;IACPqB,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,SAAS;IACtBP,YAAY,EAAE,EAAE;IAChBX,iBAAiB,EAAE,EAAE;IACrBoB,eAAe,EAAE,EAAE;IACnBvB,eAAe,EAAE,OAAO;IACxBwB,SAAS,EAAE,GAAG;IACdnB,QAAQ,EAAE;EACZ,CAAC;EACDT,UAAU,EAAE;IACVgB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBZ,UAAU,EAAE,QAAQ;IACpBM,cAAc,EAAE;EAClB,CAAC;EACDX,kBAAkB,EAAE;IAClB4B,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAe/I,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}