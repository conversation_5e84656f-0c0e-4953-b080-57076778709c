{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { PermissionStatus, Platform } from 'expo-modules-core';\nimport { MediaTypeOptions } from \"./ImagePicker.types\";\nvar MediaTypeInput = _defineProperty(_defineProperty(_defineProperty({}, MediaTypeOptions.All, 'video/mp4,video/quicktime,video/x-m4v,video/*,image/*'), MediaTypeOptions.Images, 'image/*'), MediaTypeOptions.Videos, 'video/mp4,video/quicktime,video/x-m4v,video/*');\nexport default {\n  get name() {\n    return 'ExponentImagePicker';\n  },\n  launchImageLibraryAsync: function () {\n    var _launchImageLibraryAsync = _asyncToGenerator(function* (_ref) {\n      var _ref$mediaTypes = _ref.mediaTypes,\n        mediaTypes = _ref$mediaTypes === void 0 ? MediaTypeOptions.Images : _ref$mediaTypes,\n        _ref$allowsMultipleSe = _ref.allowsMultipleSelection,\n        allowsMultipleSelection = _ref$allowsMultipleSe === void 0 ? false : _ref$allowsMultipleSe,\n        _ref$base = _ref.base64,\n        base64 = _ref$base === void 0 ? false : _ref$base;\n      if (!Platform.isDOMAvailable) {\n        return {\n          canceled: true,\n          assets: null\n        };\n      }\n      return yield openFileBrowserAsync({\n        mediaTypes: mediaTypes,\n        allowsMultipleSelection: allowsMultipleSelection,\n        base64: base64\n      });\n    });\n    function launchImageLibraryAsync(_x) {\n      return _launchImageLibraryAsync.apply(this, arguments);\n    }\n    return launchImageLibraryAsync;\n  }(),\n  launchCameraAsync: function () {\n    var _launchCameraAsync = _asyncToGenerator(function* (_ref2) {\n      var _ref2$mediaTypes = _ref2.mediaTypes,\n        mediaTypes = _ref2$mediaTypes === void 0 ? MediaTypeOptions.Images : _ref2$mediaTypes,\n        _ref2$allowsMultipleS = _ref2.allowsMultipleSelection,\n        allowsMultipleSelection = _ref2$allowsMultipleS === void 0 ? false : _ref2$allowsMultipleS,\n        _ref2$base = _ref2.base64,\n        base64 = _ref2$base === void 0 ? false : _ref2$base;\n      if (!Platform.isDOMAvailable) {\n        return {\n          canceled: true,\n          assets: null\n        };\n      }\n      return yield openFileBrowserAsync({\n        mediaTypes: mediaTypes,\n        allowsMultipleSelection: allowsMultipleSelection,\n        capture: true,\n        base64: base64\n      });\n    });\n    function launchCameraAsync(_x2) {\n      return _launchCameraAsync.apply(this, arguments);\n    }\n    return launchCameraAsync;\n  }(),\n  getCameraPermissionsAsync: function () {\n    var _getCameraPermissionsAsync = _asyncToGenerator(function* () {\n      return permissionGrantedResponse();\n    });\n    function getCameraPermissionsAsync() {\n      return _getCameraPermissionsAsync.apply(this, arguments);\n    }\n    return getCameraPermissionsAsync;\n  }(),\n  requestCameraPermissionsAsync: function () {\n    var _requestCameraPermissionsAsync = _asyncToGenerator(function* () {\n      return permissionGrantedResponse();\n    });\n    function requestCameraPermissionsAsync() {\n      return _requestCameraPermissionsAsync.apply(this, arguments);\n    }\n    return requestCameraPermissionsAsync;\n  }(),\n  getMediaLibraryPermissionsAsync: function () {\n    var _getMediaLibraryPermissionsAsync = _asyncToGenerator(function* (_writeOnly) {\n      return permissionGrantedResponse();\n    });\n    function getMediaLibraryPermissionsAsync(_x3) {\n      return _getMediaLibraryPermissionsAsync.apply(this, arguments);\n    }\n    return getMediaLibraryPermissionsAsync;\n  }(),\n  requestMediaLibraryPermissionsAsync: function () {\n    var _requestMediaLibraryPermissionsAsync = _asyncToGenerator(function* (_writeOnly) {\n      return permissionGrantedResponse();\n    });\n    function requestMediaLibraryPermissionsAsync(_x4) {\n      return _requestMediaLibraryPermissionsAsync.apply(this, arguments);\n    }\n    return requestMediaLibraryPermissionsAsync;\n  }()\n};\nfunction permissionGrantedResponse() {\n  return {\n    status: PermissionStatus.GRANTED,\n    expires: 'never',\n    granted: true,\n    canAskAgain: true\n  };\n}\nfunction openFileBrowserAsync(_ref3) {\n  var mediaTypes = _ref3.mediaTypes,\n    _ref3$capture = _ref3.capture,\n    capture = _ref3$capture === void 0 ? false : _ref3$capture,\n    _ref3$allowsMultipleS = _ref3.allowsMultipleSelection,\n    allowsMultipleSelection = _ref3$allowsMultipleS === void 0 ? false : _ref3$allowsMultipleS,\n    base64 = _ref3.base64;\n  var mediaTypeFormat = MediaTypeInput[mediaTypes];\n  var input = document.createElement('input');\n  input.style.display = 'none';\n  input.setAttribute('type', 'file');\n  input.setAttribute('accept', mediaTypeFormat);\n  input.setAttribute('id', String(Math.random()));\n  if (allowsMultipleSelection) {\n    input.setAttribute('multiple', 'multiple');\n  }\n  if (capture) {\n    input.setAttribute('capture', 'camera');\n  }\n  document.body.appendChild(input);\n  return new Promise(function (resolve) {\n    input.addEventListener('change', _asyncToGenerator(function* () {\n      if (input.files) {\n        var files = allowsMultipleSelection ? input.files : [input.files[0]];\n        var assets = yield Promise.all(Array.from(files).map(function (file) {\n          return readFile(file, {\n            base64: base64\n          });\n        }));\n        resolve({\n          canceled: false,\n          assets: assets\n        });\n      } else {\n        resolve({\n          canceled: true,\n          assets: null\n        });\n      }\n      document.body.removeChild(input);\n    }));\n    var event = new MouseEvent('click');\n    input.dispatchEvent(event);\n  });\n}\nfunction readFile(targetFile, options) {\n  return new Promise(function (resolve, reject) {\n    var reader = new FileReader();\n    reader.onerror = function () {\n      reject(new Error(`Failed to read the selected media because the operation failed.`));\n    };\n    reader.onload = function (_ref5) {\n      var target = _ref5.target;\n      var uri = target.result;\n      var returnRaw = function returnRaw() {\n        return resolve({\n          uri: uri,\n          width: 0,\n          height: 0\n        });\n      };\n      if (typeof uri === 'string') {\n        var image = new Image();\n        image.src = uri;\n        image.onload = function () {\n          var _image$naturalWidth, _image$naturalHeight;\n          resolve(_objectSpread({\n            uri: uri,\n            width: (_image$naturalWidth = image.naturalWidth) != null ? _image$naturalWidth : image.width,\n            height: (_image$naturalHeight = image.naturalHeight) != null ? _image$naturalHeight : image.height\n          }, options.base64 && {\n            base64: uri.substr(uri.indexOf(',') + 1)\n          }));\n        };\n        image.onerror = function () {\n          return returnRaw();\n        };\n      } else {\n        returnRaw();\n      }\n    };\n    reader.readAsDataURL(targetFile);\n  });\n}", "map": {"version": 3, "names": ["PermissionStatus", "Platform", "MediaTypeOptions", "MediaTypeInput", "_defineProperty", "All", "Images", "Videos", "name", "launchImageLibraryAsync", "_launchImageLibraryAsync", "_asyncToGenerator", "_ref", "_ref$mediaTypes", "mediaTypes", "_ref$allowsMultipleSe", "allowsMultipleSelection", "_ref$base", "base64", "isDOMAvailable", "canceled", "assets", "openFileBrowserAsync", "_x", "apply", "arguments", "launchCameraAsync", "_launchCameraAsync", "_ref2", "_ref2$mediaTypes", "_ref2$allowsMultipleS", "_ref2$base", "capture", "_x2", "getCameraPermissionsAsync", "_getCameraPermissionsAsync", "permissionGrantedResponse", "requestCameraPermissionsAsync", "_requestCameraPermissionsAsync", "getMediaLibraryPermissionsAsync", "_getMediaLibraryPermissionsAsync", "_writeOnly", "_x3", "requestMediaLibraryPermissionsAsync", "_requestMediaLibraryPermissionsAsync", "_x4", "status", "GRANTED", "expires", "granted", "canAskAgain", "_ref3", "_ref3$capture", "_ref3$allowsMultipleS", "mediaTypeFormat", "input", "document", "createElement", "style", "display", "setAttribute", "String", "Math", "random", "body", "append<PERSON><PERSON><PERSON>", "Promise", "resolve", "addEventListener", "files", "all", "Array", "from", "map", "file", "readFile", "<PERSON><PERSON><PERSON><PERSON>", "event", "MouseEvent", "dispatchEvent", "targetFile", "options", "reject", "reader", "FileReader", "onerror", "Error", "onload", "_ref5", "target", "uri", "result", "returnRaw", "width", "height", "image", "Image", "src", "_image$naturalWidth", "_image$naturalHeight", "_objectSpread", "naturalWidth", "naturalHeight", "substr", "indexOf", "readAsDataURL"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-image-picker/src/ExponentImagePicker.web.ts"], "sourcesContent": ["import { PermissionResponse, PermissionStatus, Platform } from 'expo-modules-core';\n\nimport {\n  ImagePickerAsset,\n  ImagePickerResult,\n  MediaTypeOptions,\n  OpenFileBrowserOptions,\n} from './ImagePicker.types';\n\nconst MediaTypeInput = {\n  [MediaTypeOptions.All]: 'video/mp4,video/quicktime,video/x-m4v,video/*,image/*',\n  [MediaTypeOptions.Images]: 'image/*',\n  [MediaTypeOptions.Videos]: 'video/mp4,video/quicktime,video/x-m4v,video/*',\n};\n\nexport default {\n  get name(): string {\n    return 'ExponentImagePicker';\n  },\n\n  async launchImageLibraryAsync({\n    mediaTypes = MediaTypeOptions.Images,\n    allowsMultipleSelection = false,\n    base64 = false,\n  }): Promise<ImagePickerResult> {\n    // SSR guard\n    if (!Platform.isDOMAvailable) {\n      return { canceled: true, assets: null };\n    }\n    return await openFileBrowserAsync({\n      mediaTypes,\n      allowsMultipleSelection,\n      base64,\n    });\n  },\n\n  async launchCameraAsync({\n    mediaTypes = MediaTypeOptions.Images,\n    allowsMultipleSelection = false,\n    base64 = false,\n  }): Promise<ImagePickerResult> {\n    // SSR guard\n    if (!Platform.isDOMAvailable) {\n      return { canceled: true, assets: null };\n    }\n    return await openFileBrowserAsync({\n      mediaTypes,\n      allowsMultipleSelection,\n      capture: true,\n      base64,\n    });\n  },\n\n  /*\n   * Delegate to expo-permissions to request camera permissions\n   */\n  async getCameraPermissionsAsync() {\n    return permissionGrantedResponse();\n  },\n  async requestCameraPermissionsAsync() {\n    return permissionGrantedResponse();\n  },\n\n  /*\n   * Camera roll permissions don't need to be requested on web, so we always\n   * respond with granted.\n   */\n  async getMediaLibraryPermissionsAsync(_writeOnly: boolean) {\n    return permissionGrantedResponse();\n  },\n  async requestMediaLibraryPermissionsAsync(_writeOnly: boolean): Promise<PermissionResponse> {\n    return permissionGrantedResponse();\n  },\n};\n\nfunction permissionGrantedResponse(): PermissionResponse {\n  return {\n    status: PermissionStatus.GRANTED,\n    expires: 'never',\n    granted: true,\n    canAskAgain: true,\n  };\n}\n\nfunction openFileBrowserAsync({\n  mediaTypes,\n  capture = false,\n  allowsMultipleSelection = false,\n  base64,\n}: OpenFileBrowserOptions): Promise<ImagePickerResult> {\n  const mediaTypeFormat = MediaTypeInput[mediaTypes];\n\n  const input = document.createElement('input');\n  input.style.display = 'none';\n  input.setAttribute('type', 'file');\n  input.setAttribute('accept', mediaTypeFormat);\n  input.setAttribute('id', String(Math.random()));\n  if (allowsMultipleSelection) {\n    input.setAttribute('multiple', 'multiple');\n  }\n  if (capture) {\n    input.setAttribute('capture', 'camera');\n  }\n  document.body.appendChild(input);\n\n  return new Promise((resolve) => {\n    input.addEventListener('change', async () => {\n      if (input.files) {\n        const files = allowsMultipleSelection ? input.files : [input.files[0]];\n        const assets: ImagePickerAsset[] = await Promise.all(\n          Array.from(files).map((file) => readFile(file, { base64 }))\n        );\n\n        resolve({ canceled: false, assets });\n      } else {\n        resolve({ canceled: true, assets: null });\n      }\n      document.body.removeChild(input);\n    });\n\n    const event = new MouseEvent('click');\n    input.dispatchEvent(event);\n  });\n}\n\nfunction readFile(targetFile: Blob, options: { base64: boolean }): Promise<ImagePickerAsset> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onerror = () => {\n      reject(new Error(`Failed to read the selected media because the operation failed.`));\n    };\n    reader.onload = ({ target }) => {\n      const uri = (target as any).result;\n      const returnRaw = () => resolve({ uri, width: 0, height: 0 });\n\n      if (typeof uri === 'string') {\n        const image = new Image();\n        image.src = uri;\n        image.onload = () => {\n          resolve({\n            uri,\n            width: image.naturalWidth ?? image.width,\n            height: image.naturalHeight ?? image.height,\n            // The blob's result cannot be directly decoded as Base64 without\n            // first removing the Data-URL declaration preceding the\n            // Base64-encoded data. To retrieve only the Base64 encoded string,\n            // first remove data:*/*;base64, from the result.\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileReader/readAsDataURL\n            ...(options.base64 && { base64: uri.substr(uri.indexOf(',') + 1) }),\n          });\n        };\n        image.onerror = () => returnRaw();\n      } else {\n        returnRaw();\n      }\n    };\n\n    reader.readAsDataURL(targetFile);\n  });\n}\n"], "mappings": ";;;;AAAA,SAA6BA,gBAAgB,EAAEC,QAAQ,QAAQ,mBAAmB;AAElF,SAGEC,gBAAgB;AAIlB,IAAMC,cAAc,GAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,KACjBF,gBAAgB,CAACG,GAAG,EAAG,uDAAuD,GAC9EH,gBAAgB,CAACI,MAAM,EAAG,SAAS,GACnCJ,gBAAgB,CAACK,MAAM,EAAG,+CAA+C,CAC3E;AAED,eAAe;EACb,IAAIC,IAAIA,CAAA;IACN,OAAO,qBAAqB;EAC9B,CAAC;EAEKC,uBAAuB;IAAA,IAAAC,wBAAA,GAAAC,iBAAA,YAAAC,IAAA,EAI5B;MAAA,IAAAC,eAAA,GAAAD,IAAA,CAHCE,UAAU;QAAVA,UAAU,GAAAD,eAAA,cAAGX,gBAAgB,CAACI,MAAM,GAAAO,eAAA;QAAAE,qBAAA,GAAAH,IAAA,CACpCI,uBAAuB;QAAvBA,uBAAuB,GAAAD,qBAAA,cAAG,KAAK,GAAAA,qBAAA;QAAAE,SAAA,GAAAL,IAAA,CAC/BM,MAAM;QAANA,MAAM,GAAAD,SAAA,cAAG,KAAK,GAAAA,SAAA;MAGd,IAAI,CAAChB,QAAQ,CAACkB,cAAc,EAAE;QAC5B,OAAO;UAAEC,QAAQ,EAAE,IAAI;UAAEC,MAAM,EAAE;QAAI,CAAE;;MAEzC,aAAaC,oBAAoB,CAAC;QAChCR,UAAU,EAAVA,UAAU;QACVE,uBAAuB,EAAvBA,uBAAuB;QACvBE,MAAM,EAANA;OACD,CAAC;IACJ,CAAC;IAAA,SAdKT,uBAAuBA,CAAAc,EAAA;MAAA,OAAAb,wBAAA,CAAAc,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAvBhB,uBAAuB;EAAA;EAgBvBiB,iBAAiB;IAAA,IAAAC,kBAAA,GAAAhB,iBAAA,YAAAiB,KAAA,EAItB;MAAA,IAAAC,gBAAA,GAAAD,KAAA,CAHCd,UAAU;QAAVA,UAAU,GAAAe,gBAAA,cAAG3B,gBAAgB,CAACI,MAAM,GAAAuB,gBAAA;QAAAC,qBAAA,GAAAF,KAAA,CACpCZ,uBAAuB;QAAvBA,uBAAuB,GAAAc,qBAAA,cAAG,KAAK,GAAAA,qBAAA;QAAAC,UAAA,GAAAH,KAAA,CAC/BV,MAAM;QAANA,MAAM,GAAAa,UAAA,cAAG,KAAK,GAAAA,UAAA;MAGd,IAAI,CAAC9B,QAAQ,CAACkB,cAAc,EAAE;QAC5B,OAAO;UAAEC,QAAQ,EAAE,IAAI;UAAEC,MAAM,EAAE;QAAI,CAAE;;MAEzC,aAAaC,oBAAoB,CAAC;QAChCR,UAAU,EAAVA,UAAU;QACVE,uBAAuB,EAAvBA,uBAAuB;QACvBgB,OAAO,EAAE,IAAI;QACbd,MAAM,EAANA;OACD,CAAC;IACJ,CAAC;IAAA,SAfKQ,iBAAiBA,CAAAO,GAAA;MAAA,OAAAN,kBAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAjBC,iBAAiB;EAAA;EAoBjBQ,yBAAyB;IAAA,IAAAC,0BAAA,GAAAxB,iBAAA;MAC7B,OAAOyB,yBAAyB,EAAE;IACpC,CAAC;IAAA,SAFKF,yBAAyBA,CAAA;MAAA,OAAAC,0BAAA,CAAAX,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAzBS,yBAAyB;EAAA;EAGzBG,6BAA6B;IAAA,IAAAC,8BAAA,GAAA3B,iBAAA;MACjC,OAAOyB,yBAAyB,EAAE;IACpC,CAAC;IAAA,SAFKC,6BAA6BA,CAAA;MAAA,OAAAC,8BAAA,CAAAd,KAAA,OAAAC,SAAA;IAAA;IAAA,OAA7BY,6BAA6B;EAAA;EAQ7BE,+BAA+B;IAAA,IAAAC,gCAAA,GAAA7B,iBAAA,YAAC8B,UAAmB;MACvD,OAAOL,yBAAyB,EAAE;IACpC,CAAC;IAAA,SAFKG,+BAA+BA,CAAAG,GAAA;MAAA,OAAAF,gCAAA,CAAAhB,KAAA,OAAAC,SAAA;IAAA;IAAA,OAA/Bc,+BAA+B;EAAA;EAG/BI,mCAAmC;IAAA,IAAAC,oCAAA,GAAAjC,iBAAA,YAAC8B,UAAmB;MAC3D,OAAOL,yBAAyB,EAAE;IACpC,CAAC;IAAA,SAFKO,mCAAmCA,CAAAE,GAAA;MAAA,OAAAD,oCAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAnCkB,mCAAmC;EAAA;CAG1C;AAED,SAASP,yBAAyBA,CAAA;EAChC,OAAO;IACLU,MAAM,EAAE9C,gBAAgB,CAAC+C,OAAO;IAChCC,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE;GACd;AACH;AAEA,SAAS5B,oBAAoBA,CAAA6B,KAAA,EAKJ;EAAA,IAJvBrC,UAAU,GAAAqC,KAAA,CAAVrC,UAAU;IAAAsC,aAAA,GAAAD,KAAA,CACVnB,OAAO;IAAPA,OAAO,GAAAoB,aAAA,cAAG,KAAK,GAAAA,aAAA;IAAAC,qBAAA,GAAAF,KAAA,CACfnC,uBAAuB;IAAvBA,uBAAuB,GAAAqC,qBAAA,cAAG,KAAK,GAAAA,qBAAA;IAC/BnC,MAAM,GAAAiC,KAAA,CAANjC,MAAM;EAEN,IAAMoC,eAAe,GAAGnD,cAAc,CAACW,UAAU,CAAC;EAElD,IAAMyC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAC7CF,KAAK,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;EAC5BJ,KAAK,CAACK,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;EAClCL,KAAK,CAACK,YAAY,CAAC,QAAQ,EAAEN,eAAe,CAAC;EAC7CC,KAAK,CAACK,YAAY,CAAC,IAAI,EAAEC,MAAM,CAACC,IAAI,CAACC,MAAM,EAAE,CAAC,CAAC;EAC/C,IAAI/C,uBAAuB,EAAE;IAC3BuC,KAAK,CAACK,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;;EAE5C,IAAI5B,OAAO,EAAE;IACXuB,KAAK,CAACK,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC;;EAEzCJ,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,KAAK,CAAC;EAEhC,OAAO,IAAIW,OAAO,CAAC,UAACC,OAAO,EAAI;IAC7BZ,KAAK,CAACa,gBAAgB,CAAC,QAAQ,EAAAzD,iBAAA,CAAE,aAAW;MAC1C,IAAI4C,KAAK,CAACc,KAAK,EAAE;QACf,IAAMA,KAAK,GAAGrD,uBAAuB,GAAGuC,KAAK,CAACc,KAAK,GAAG,CAACd,KAAK,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;QACtE,IAAMhD,MAAM,SAA6B6C,OAAO,CAACI,GAAG,CAClDC,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,GAAG,CAAC,UAACC,IAAI;UAAA,OAAKC,QAAQ,CAACD,IAAI,EAAE;YAAExD,MAAM,EAANA;UAAM,CAAE,CAAC;QAAA,EAAC,CAC5D;QAEDiD,OAAO,CAAC;UAAE/C,QAAQ,EAAE,KAAK;UAAEC,MAAM,EAANA;QAAM,CAAE,CAAC;OACrC,MAAM;QACL8C,OAAO,CAAC;UAAE/C,QAAQ,EAAE,IAAI;UAAEC,MAAM,EAAE;QAAI,CAAE,CAAC;;MAE3CmC,QAAQ,CAACQ,IAAI,CAACY,WAAW,CAACrB,KAAK,CAAC;IAClC,CAAC,EAAC;IAEF,IAAMsB,KAAK,GAAG,IAAIC,UAAU,CAAC,OAAO,CAAC;IACrCvB,KAAK,CAACwB,aAAa,CAACF,KAAK,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASF,QAAQA,CAACK,UAAgB,EAAEC,OAA4B;EAC9D,OAAO,IAAIf,OAAO,CAAC,UAACC,OAAO,EAAEe,MAAM,EAAI;IACrC,IAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,OAAO,GAAG,YAAK;MACpBH,MAAM,CAAC,IAAII,KAAK,CAAC,iEAAiE,CAAC,CAAC;IACtF,CAAC;IACDH,MAAM,CAACI,MAAM,GAAG,UAAAC,KAAA,EAAe;MAAA,IAAZC,MAAM,GAAAD,KAAA,CAANC,MAAM;MACvB,IAAMC,GAAG,GAAID,MAAc,CAACE,MAAM;MAClC,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA;QAAA,OAASzB,OAAO,CAAC;UAAEuB,GAAG,EAAHA,GAAG;UAAEG,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAC,CAAE,CAAC;MAAA;MAE7D,IAAI,OAAOJ,GAAG,KAAK,QAAQ,EAAE;QAC3B,IAAMK,KAAK,GAAG,IAAIC,KAAK,EAAE;QACzBD,KAAK,CAACE,GAAG,GAAGP,GAAG;QACfK,KAAK,CAACR,MAAM,GAAG,YAAK;UAAA,IAAAW,mBAAA,EAAAC,oBAAA;UAClBhC,OAAO,CAAAiC,aAAA;YACLV,GAAG,EAAHA,GAAG;YACHG,KAAK,GAAAK,mBAAA,GAAEH,KAAK,CAACM,YAAY,YAAAH,mBAAA,GAAIH,KAAK,CAACF,KAAK;YACxCC,MAAM,GAAAK,oBAAA,GAAEJ,KAAK,CAACO,aAAa,YAAAH,oBAAA,GAAIJ,KAAK,CAACD;UAAM,GAMvCb,OAAO,CAAC/D,MAAM,IAAI;YAAEA,MAAM,EAAEwE,GAAG,CAACa,MAAM,CAACb,GAAG,CAACc,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;UAAC,CAAE,CACnE,CAAC;QACJ,CAAC;QACDT,KAAK,CAACV,OAAO,GAAG;UAAA,OAAMO,SAAS,EAAE;QAAA;OAClC,MAAM;QACLA,SAAS,EAAE;;IAEf,CAAC;IAEDT,MAAM,CAACsB,aAAa,CAACzB,UAAU,CAAC;EAClC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}