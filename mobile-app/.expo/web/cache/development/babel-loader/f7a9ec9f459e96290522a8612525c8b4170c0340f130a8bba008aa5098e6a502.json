{"ast": null, "code": "export var InterruptionModeIOS;\n(function (InterruptionModeIOS) {\n  InterruptionModeIOS[InterruptionModeIOS[\"MixWithOthers\"] = 0] = \"MixWithOthers\";\n  InterruptionModeIOS[InterruptionModeIOS[\"DoNotMix\"] = 1] = \"DoNotMix\";\n  InterruptionModeIOS[InterruptionModeIOS[\"DuckOthers\"] = 2] = \"DuckOthers\";\n})(InterruptionModeIOS || (InterruptionModeIOS = {}));\nexport var InterruptionModeAndroid;\n(function (InterruptionModeAndroid) {\n  InterruptionModeAndroid[InterruptionModeAndroid[\"DoNotMix\"] = 1] = \"DoNotMix\";\n  InterruptionModeAndroid[InterruptionModeAndroid[\"DuckOthers\"] = 2] = \"DuckOthers\";\n})(InterruptionModeAndroid || (InterruptionModeAndroid = {}));", "map": {"version": 3, "names": ["InterruptionModeIOS", "InterruptionModeAndroid"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio.types.ts"], "sourcesContent": ["// @needsAudit\nexport type AudioMode = {\n  /**\n   * A boolean selecting if recording is enabled on iOS.\n   * > When this flag is set to `true`, playback may be routed to the phone earpiece instead of to the speaker. Set it back to `false` after stopping recording to reenable playback through the speaker.\n   * @default false\n   */\n  allowsRecordingIOS: boolean;\n  /**\n   * An enum selecting how your experience's audio should interact with the audio from other apps on iOS.\n   */\n  interruptionModeIOS: InterruptionModeIOS;\n  /**\n   * A boolean selecting if your experience's audio should play in silent mode on iOS.\n   * @default false\n   */\n  playsInSilentModeIOS: boolean;\n  /**\n   * A boolean selecting if the audio session (playback or recording) should stay active even when the app goes into background.\n   * > This is not available in Expo Go for iOS, it will only work in standalone apps.\n   * > To enable it for standalone apps, [follow the instructions below](#playing-or-recording-audio-in-background-ios)\n   * > to add `UIBackgroundModes` to your app configuration.\n   * @default false\n   */\n  staysActiveInBackground: boolean;\n  /**\n   * An enum selecting how your experience's audio should interact with the audio from other apps on Android.\n   */\n  interruptionModeAndroid: InterruptionModeAndroid;\n  /**\n   * A boolean selecting if your experience's audio should automatically be lowered in volume (\"duck\") if audio from another\n   * app interrupts your experience. If `false`, audio from other apps will pause your audio.\n   * @default true\n   */\n  shouldDuckAndroid: boolean;\n  /**\n   * A boolean selecting if the audio is routed to earpiece on Android.\n   * @default false\n   */\n  playThroughEarpieceAndroid: boolean;\n};\n\n// @needsAudit\nexport enum InterruptionModeIOS {\n  /**\n   * **This is the default option.** If this option is set, your experience's audio is mixed with audio playing in background apps.\n   */\n  MixWithOthers = 0,\n  /**\n   * If this option is set, your experience's audio interrupts audio from other apps.\n   */\n  DoNotMix = 1,\n  /**\n   * If this option is set, your experience's audio lowers the volume (\"ducks\") of audio from other apps while your audio plays.\n   */\n  DuckOthers = 2,\n}\n\nexport enum InterruptionModeAndroid {\n  /**\n   * If this option is set, your experience's audio interrupts audio from other apps.\n   */\n  DoNotMix = 1,\n  /**\n   * **This is the default option.** If this option is set, your experience's audio lowers the volume (\"ducks\") of audio from other apps while your audio plays.\n   */\n  DuckOthers = 2,\n}\n"], "mappings": "AA2CA,WAAYA,mBAaX;AAbD,WAAYA,mBAAmB;EAI7BA,mBAAA,CAAAA,mBAAA,wCAAiB;EAIjBA,mBAAA,CAAAA,mBAAA,8BAAY;EAIZA,mBAAA,CAAAA,mBAAA,kCAAc;AAChB,CAAC,EAbWA,mBAAmB,KAAnBA,mBAAmB;AAe/B,WAAYC,uBASX;AATD,WAAYA,uBAAuB;EAIjCA,uBAAA,CAAAA,uBAAA,8BAAY;EAIZA,uBAAA,CAAAA,uBAAA,kCAAc;AAChB,CAAC,EATWA,uBAAuB,KAAvBA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}