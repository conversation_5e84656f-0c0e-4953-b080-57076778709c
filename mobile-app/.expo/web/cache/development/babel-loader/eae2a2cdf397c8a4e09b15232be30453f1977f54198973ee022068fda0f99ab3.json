{"ast": null, "code": "\"use strict\";\n\nfunction peg$subclass(child, parent) {\n  function ctor() {\n    this.constructor = child;\n  }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message = message;\n  this.expected = expected;\n  this.found = found;\n  this.location = location;\n  this.name = \"SyntaxError\";\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\npeg$subclass(peg$SyntaxError, Error);\npeg$SyntaxError.buildMessage = function (expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function literal(expectation) {\n      return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n    },\n    \"class\": function _class(expectation) {\n      var escapedParts = \"\",\n        i;\n      for (i = 0; i < expectation.parts.length; i++) {\n        escapedParts += expectation.parts[i] instanceof Array ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1]) : classEscape(expectation.parts[i]);\n      }\n      return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n    },\n    any: function any(expectation) {\n      return \"any character\";\n    },\n    end: function end(expectation) {\n      return \"end of input\";\n    },\n    other: function other(expectation) {\n      return expectation.description;\n    }\n  };\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n  function literalEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function classEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\\]/g, '\\\\]').replace(/\\^/g, '\\\\^').replace(/-/g, '\\\\-').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n      i,\n      j;\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n    descriptions.sort();\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n      default:\n        return descriptions.slice(0, -1).join(\", \") + \", or \" + descriptions[descriptions.length - 1];\n    }\n  }\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n  var peg$FAILED = {},\n    peg$startRuleFunctions = {\n      transformList: peg$parsetransformList\n    },\n    peg$startRuleFunction = peg$parsetransformList,\n    peg$c0 = function peg$c0(ts) {\n      return ts;\n    },\n    peg$c1 = function peg$c1(t, ts) {\n      return multiply_matrices(t, ts);\n    },\n    peg$c2 = \"matrix\",\n    peg$c3 = peg$literalExpectation(\"matrix\", false),\n    peg$c4 = \"(\",\n    peg$c5 = peg$literalExpectation(\"(\", false),\n    peg$c6 = \")\",\n    peg$c7 = peg$literalExpectation(\")\", false),\n    peg$c8 = function peg$c8(a, b, c, d, e, f) {\n      return [a, c, e, b, d, f];\n    },\n    peg$c9 = \"translate\",\n    peg$c10 = peg$literalExpectation(\"translate\", false),\n    peg$c11 = function peg$c11(tx, ty) {\n      return [1, 0, tx, 0, 1, ty || 0];\n    },\n    peg$c12 = \"scale\",\n    peg$c13 = peg$literalExpectation(\"scale\", false),\n    peg$c14 = function peg$c14(sx, sy) {\n      return [sx, 0, 0, 0, sy === null ? sx : sy, 0];\n    },\n    peg$c15 = \"rotate\",\n    peg$c16 = peg$literalExpectation(\"rotate\", false),\n    peg$c17 = function peg$c17(angle, c) {\n      var cos = Math.cos(deg2rad * angle);\n      var sin = Math.sin(deg2rad * angle);\n      if (c !== null) {\n        var x = c[0];\n        var y = c[1];\n        return [cos, -sin, cos * -x + -sin * -y + x, sin, cos, sin * -x + cos * -y + y];\n      }\n      return [cos, -sin, 0, sin, cos, 0];\n    },\n    peg$c18 = \"skewX\",\n    peg$c19 = peg$literalExpectation(\"skewX\", false),\n    peg$c20 = function peg$c20(angle) {\n      return [1, Math.tan(deg2rad * angle), 0, 0, 1, 0];\n    },\n    peg$c21 = \"skewY\",\n    peg$c22 = peg$literalExpectation(\"skewY\", false),\n    peg$c23 = function peg$c23(angle) {\n      return [1, 0, 0, Math.tan(deg2rad * angle), 1, 0];\n    },\n    peg$c24 = function peg$c24(f) {\n      return parseFloat(f.join(\"\"));\n    },\n    peg$c25 = function peg$c25(i) {\n      return parseInt(i.join(\"\"));\n    },\n    peg$c26 = function peg$c26(n) {\n      return n;\n    },\n    peg$c27 = function peg$c27(n1, n2) {\n      return [n1, n2];\n    },\n    peg$c28 = \",\",\n    peg$c29 = peg$literalExpectation(\",\", false),\n    peg$c30 = function peg$c30(ds) {\n      return ds.join(\"\");\n    },\n    peg$c31 = function peg$c31(f) {\n      return f.join(\"\");\n    },\n    peg$c32 = function peg$c32(d) {\n      return d.join(\"\");\n    },\n    peg$c33 = peg$otherExpectation(\"fractionalConstant\"),\n    peg$c34 = \".\",\n    peg$c35 = peg$literalExpectation(\".\", false),\n    peg$c36 = function peg$c36(d1, d2) {\n      return [d1 ? d1.join(\"\") : null, \".\", d2.join(\"\")].join(\"\");\n    },\n    peg$c37 = /^[eE]/,\n    peg$c38 = peg$classExpectation([\"e\", \"E\"], false, false),\n    peg$c39 = function peg$c39(e) {\n      return [e[0], e[1], e[2].join(\"\")].join(\"\");\n    },\n    peg$c40 = /^[+\\-]/,\n    peg$c41 = peg$classExpectation([\"+\", \"-\"], false, false),\n    peg$c42 = /^[0-9]/,\n    peg$c43 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n    peg$c44 = /^[ \\t\\r\\n]/,\n    peg$c45 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false),\n    peg$currPos = 0,\n    peg$savedPos = 0,\n    peg$posDetailsCache = [{\n      line: 1,\n      column: 1\n    }],\n    peg$maxFailPos = 0,\n    peg$maxFailExpected = [],\n    peg$silentFails = 0,\n    peg$result;\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildStructuredError([peg$otherExpectation(description)], input.substring(peg$savedPos, peg$currPos), location);\n  }\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildSimpleError(message, location);\n  }\n  function peg$literalExpectation(text, ignoreCase) {\n    return {\n      type: \"literal\",\n      text: text,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return {\n      type: \"class\",\n      parts: parts,\n      inverted: inverted,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$anyExpectation() {\n    return {\n      type: \"any\"\n    };\n  }\n  function peg$endExpectation() {\n    return {\n      type: \"end\"\n    };\n  }\n  function peg$otherExpectation(description) {\n    return {\n      type: \"other\",\n      description: description\n    };\n  }\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos],\n      p;\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n        p++;\n      }\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n      endPosDetails = peg$computePosDetails(endPos);\n    return {\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) {\n      return;\n    }\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n    peg$maxFailExpected.push(expected);\n  }\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(peg$SyntaxError.buildMessage(expected, found), expected, found, location);\n  }\n  function peg$parsetransformList() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsewsp();\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsetransforms();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c0(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsetransforms() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$parsetransform();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsecommaWsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsecommaWsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsetransforms();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetransform();\n    }\n    return s0;\n  }\n  function peg$parsetransform() {\n    var s0;\n    s0 = peg$parsematrix();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetranslate();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsescale();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parserotate();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseskewX();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parseskewY();\n            }\n          }\n        }\n      }\n    }\n    return s0;\n  }\n  function peg$parsematrix() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16, s17;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c2) {\n      s1 = peg$c2;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c3);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWsp();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parsenumber();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parsecommaWsp();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parsenumber();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsecommaWsp();\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parsenumber();\n                        if (s11 !== peg$FAILED) {\n                          s12 = peg$parsecommaWsp();\n                          if (s12 !== peg$FAILED) {\n                            s13 = peg$parsenumber();\n                            if (s13 !== peg$FAILED) {\n                              s14 = peg$parsecommaWsp();\n                              if (s14 !== peg$FAILED) {\n                                s15 = peg$parsenumber();\n                                if (s15 !== peg$FAILED) {\n                                  s16 = [];\n                                  s17 = peg$parsewsp();\n                                  while (s17 !== peg$FAILED) {\n                                    s16.push(s17);\n                                    s17 = peg$parsewsp();\n                                  }\n                                  if (s16 !== peg$FAILED) {\n                                    if (input.charCodeAt(peg$currPos) === 41) {\n                                      s17 = peg$c6;\n                                      peg$currPos++;\n                                    } else {\n                                      s17 = peg$FAILED;\n                                      if (peg$silentFails === 0) {\n                                        peg$fail(peg$c7);\n                                      }\n                                    }\n                                    if (s17 !== peg$FAILED) {\n                                      peg$savedPos = s0;\n                                      s1 = peg$c8(s5, s7, s9, s11, s13, s15);\n                                      s0 = s1;\n                                    } else {\n                                      peg$currPos = s0;\n                                      s0 = peg$FAILED;\n                                    }\n                                  } else {\n                                    peg$currPos = s0;\n                                    s0 = peg$FAILED;\n                                  }\n                                } else {\n                                  peg$currPos = s0;\n                                  s0 = peg$FAILED;\n                                }\n                              } else {\n                                peg$currPos = s0;\n                                s0 = peg$FAILED;\n                              }\n                            } else {\n                              peg$currPos = s0;\n                              s0 = peg$FAILED;\n                            }\n                          } else {\n                            peg$currPos = s0;\n                            s0 = peg$FAILED;\n                          }\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsetranslate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 9) === peg$c9) {\n      s1 = peg$c9;\n      peg$currPos += 9;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c10);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c11(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsescale() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c12) {\n      s1 = peg$c12;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c13);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c14(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parserotate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c15) {\n      s1 = peg$c15;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c16);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspTwoNumbers();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) {\n                      peg$fail(peg$c7);\n                    }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c17(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseskewX() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c18) {\n      s1 = peg$c18;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c19);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) {\n                    peg$fail(peg$c7);\n                  }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c20(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseskewY() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c21) {\n      s1 = peg$c21;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c22);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c5);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) {\n                    peg$fail(peg$c7);\n                  }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c23(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsenumber() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsesign();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsefloatingPointConstant();\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c24(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsesign();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseintegerConstant();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c25(s1);\n      }\n      s0 = s1;\n    }\n    return s0;\n  }\n  function peg$parsecommaWspNumber() {\n    var s0, s1, s2;\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecommaWspTwoNumbers() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecommaWsp();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsenumber();\n          if (s4 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c27(s2, s4);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecommaWsp() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsewsp();\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s1 = [s1, s2, s3];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsecomma();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parsewsp();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parsewsp();\n        }\n        if (s2 !== peg$FAILED) {\n          s1 = [s1, s2];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n    return s0;\n  }\n  function peg$parsecomma() {\n    var s0;\n    if (input.charCodeAt(peg$currPos) === 44) {\n      s0 = peg$c28;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c29);\n      }\n    }\n    return s0;\n  }\n  function peg$parseintegerConstant() {\n    var s0, s1;\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c30(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsefloatingPointConstant() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsefractionalConstant();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseexponent();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c31(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsedigitSequence();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseexponent();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c32(s1);\n      }\n      s0 = s1;\n    }\n    return s0;\n  }\n  function peg$parsefractionalConstant() {\n    var s0, s1, s2, s3;\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    if (s1 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s2 = peg$c34;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$c35);\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsedigitSequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c36(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsedigitSequence();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s2 = peg$c34;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c35);\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c32(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c33);\n      }\n    }\n    return s0;\n  }\n  function peg$parseexponent() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (peg$c37.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c38);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsesign();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigitSequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c39(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsesign() {\n    var s0;\n    if (peg$c40.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c41);\n      }\n    }\n    return s0;\n  }\n  function peg$parsedigitSequence() {\n    var s0, s1;\n    s0 = [];\n    s1 = peg$parsedigit();\n    if (s1 !== peg$FAILED) {\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        s1 = peg$parsedigit();\n      }\n    } else {\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsedigit() {\n    var s0;\n    if (peg$c42.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c43);\n      }\n    }\n    return s0;\n  }\n  function peg$parsewsp() {\n    var s0;\n    if (peg$c44.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c45);\n      }\n    }\n    return s0;\n  }\n  var deg2rad = Math.PI / 180;\n  function multiply_matrices(l, r) {\n    var al = l[0];\n    var cl = l[1];\n    var el = l[2];\n    var bl = l[3];\n    var dl = l[4];\n    var fl = l[5];\n    var ar = r[0];\n    var cr = r[1];\n    var er = r[2];\n    var br = r[3];\n    var dr = r[4];\n    var fr = r[5];\n    var a = al * ar + cl * br;\n    var c = al * cr + cl * dr;\n    var e = al * er + cl * fr + el;\n    var b = bl * ar + dl * br;\n    var d = bl * cr + dl * dr;\n    var f = bl * er + dl * fr + fl;\n    return [a, c, e, b, d, f];\n  }\n  peg$result = peg$startRuleFunction();\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n    throw peg$buildStructuredError(peg$maxFailExpected, peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null, peg$maxFailPos < input.length ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1) : peg$computeLocation(peg$maxFailPos, peg$maxFailPos));\n  }\n}\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse: peg$parse\n};", "map": {"version": 3, "names": ["peg$subclass", "child", "parent", "ctor", "constructor", "prototype", "peg$SyntaxError", "message", "expected", "found", "location", "name", "Error", "captureStackTrace", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "_class", "escapedParts", "i", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "replace", "describeExpectation", "type", "describeExpected", "descriptions", "j", "sort", "slice", "join", "describeFound", "peg$parse", "input", "options", "peg$FAILED", "peg$startRuleFunctions", "transformList", "peg$parsetransformList", "peg$startRuleFunction", "peg$c0", "ts", "peg$c1", "t", "multiply_matrices", "peg$c2", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$c6", "peg$c7", "peg$c8", "a", "b", "c", "d", "e", "f", "peg$c9", "peg$c10", "peg$c11", "tx", "ty", "peg$c12", "peg$c13", "peg$c14", "sx", "sy", "peg$c15", "peg$c16", "peg$c17", "angle", "cos", "Math", "deg2rad", "sin", "x", "y", "peg$c18", "peg$c19", "peg$c20", "tan", "peg$c21", "peg$c22", "peg$c23", "peg$c24", "parseFloat", "peg$c25", "parseInt", "peg$c26", "n", "peg$c27", "n1", "n2", "peg$c28", "peg$c29", "peg$c30", "ds", "peg$c31", "peg$c32", "peg$c33", "peg$otherExpectation", "peg$c34", "peg$c35", "peg$c36", "d1", "d2", "peg$c37", "peg$c38", "peg$classExpectation", "peg$c39", "peg$c40", "peg$c41", "peg$c42", "peg$c43", "peg$c44", "peg$c45", "peg$currPos", "peg$savedPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "peg$result", "startRule", "substring", "peg$computeLocation", "peg$buildStructuredError", "error", "peg$buildSimpleError", "ignoreCase", "peg$anyExpectation", "peg$endExpectation", "peg$computePosDetails", "pos", "details", "p", "startPos", "endPos", "startPosDetails", "endPosDetails", "start", "offset", "peg$fail", "push", "s0", "s1", "s2", "s3", "s4", "peg$parsewsp", "peg$parsetransforms", "peg$parsetransform", "peg$parsecommaWsp", "peg$parsematrix", "peg$parsetranslate", "peg$parsescale", "peg$parserotate", "peg$parseskewX", "peg$parseskewY", "s5", "s6", "s7", "s8", "s9", "s10", "s11", "s12", "s13", "s14", "s15", "s16", "s17", "substr", "peg$parsenumber", "peg$parsecommaWspNumber", "peg$parsecommaWspTwoNumbers", "peg$parsesign", "peg$parsefloatingPointConstant", "peg$parseintegerConstant", "peg$parsecomma", "peg$parsedigitSequence", "peg$parsefractionalConstant", "peg$parseexponent", "test", "char<PERSON>t", "peg$parsedigit", "PI", "l", "r", "al", "cl", "el", "bl", "dl", "fl", "ar", "cr", "er", "br", "dr", "fr", "module", "exports", "SyntaxError", "parse"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-svg/src/lib/extract/transform.js"], "sourcesContent": ["/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n\n\"use strict\";\n\nfunction peg$subclass(child, parent) {\n  function ctor() { this.constructor = child; }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\n\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message  = message;\n  this.expected = expected;\n  this.found    = found;\n  this.location = location;\n  this.name     = \"SyntaxError\";\n\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\n\npeg$subclass(peg$SyntaxError, Error);\n\npeg$SyntaxError.buildMessage = function(expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n        literal: function(expectation) {\n          return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n        },\n\n        \"class\": function(expectation) {\n          var escapedParts = \"\",\n              i;\n\n          for (i = 0; i < expectation.parts.length; i++) {\n            escapedParts += expectation.parts[i] instanceof Array\n              ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n              : classEscape(expectation.parts[i]);\n          }\n\n          return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n        },\n\n        any: function(expectation) {\n          return \"any character\";\n        },\n\n        end: function(expectation) {\n          return \"end of input\";\n        },\n\n        other: function(expectation) {\n          return expectation.description;\n        }\n      };\n\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n\n  function literalEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\"/g,  '\\\\\"')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function classEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\]/g, '\\\\]')\n      .replace(/\\^/g, '\\\\^')\n      .replace(/-/g,  '\\\\-')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n        i, j;\n\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n\n    descriptions.sort();\n\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n\n      default:\n        return descriptions.slice(0, -1).join(\", \")\n          + \", or \"\n          + descriptions[descriptions.length - 1];\n    }\n  }\n\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\n\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n\n  var peg$FAILED = {},\n\n      peg$startRuleFunctions = { transformList: peg$parsetransformList },\n      peg$startRuleFunction  = peg$parsetransformList,\n\n      peg$c0 = function(ts) { return ts; },\n      peg$c1 = function(t, ts) {\n              return multiply_matrices(t, ts);\n          },\n      peg$c2 = \"matrix\",\n      peg$c3 = peg$literalExpectation(\"matrix\", false),\n      peg$c4 = \"(\",\n      peg$c5 = peg$literalExpectation(\"(\", false),\n      peg$c6 = \")\",\n      peg$c7 = peg$literalExpectation(\")\", false),\n      peg$c8 = function(a, b, c, d, e, f) {\n              return [\n                  a, c, e,\n                  b, d, f\n              ];\n          },\n      peg$c9 = \"translate\",\n      peg$c10 = peg$literalExpectation(\"translate\", false),\n      peg$c11 = function(tx, ty) {\n              return [\n                  1, 0, tx,\n                  0, 1, ty || 0\n              ];\n          },\n      peg$c12 = \"scale\",\n      peg$c13 = peg$literalExpectation(\"scale\", false),\n      peg$c14 = function(sx, sy) {\n              return [\n                  sx, 0,                     0,\n                  0,  sy === null ? sx : sy, 0\n              ];\n          },\n      peg$c15 = \"rotate\",\n      peg$c16 = peg$literalExpectation(\"rotate\", false),\n      peg$c17 = function(angle, c) {\n              var cos = Math.cos(deg2rad * angle);\n              var sin = Math.sin(deg2rad * angle);\n              if (c !== null) {\n                  var x = c[0];\n                  var y = c[1];\n                  return [\n                      cos, -sin, cos * -x + -sin * -y + x,\n                      sin,  cos, sin * -x +  cos * -y + y\n                  ];\n              }\n              return [\n                  cos, -sin, 0,\n                  sin,  cos, 0\n              ];\n          },\n      peg$c18 = \"skewX\",\n      peg$c19 = peg$literalExpectation(\"skewX\", false),\n      peg$c20 = function(angle) {\n              return [\n                  1, Math.tan(deg2rad * angle), 0,\n                  0, 1,                         0\n              ];\n          },\n      peg$c21 = \"skewY\",\n      peg$c22 = peg$literalExpectation(\"skewY\", false),\n      peg$c23 = function(angle) {\n              return [\n                  1,                         0, 0,\n                  Math.tan(deg2rad * angle), 1, 0\n              ];\n          },\n      peg$c24 = function(f) { return parseFloat(f.join(\"\")); },\n      peg$c25 = function(i) { return parseInt(i.join(\"\")); },\n      peg$c26 = function(n) { return n; },\n      peg$c27 = function(n1, n2) { return [n1, n2]; },\n      peg$c28 = \",\",\n      peg$c29 = peg$literalExpectation(\",\", false),\n      peg$c30 = function(ds) { return ds.join(\"\"); },\n      peg$c31 = function(f) { return f.join(\"\"); },\n      peg$c32 = function(d) { return d.join(\"\"); },\n      peg$c33 = peg$otherExpectation(\"fractionalConstant\"),\n      peg$c34 = \".\",\n      peg$c35 = peg$literalExpectation(\".\", false),\n      peg$c36 = function(d1, d2) { return [d1 ? d1.join(\"\") : null, \".\", d2.join(\"\")].join(\"\"); },\n      peg$c37 = /^[eE]/,\n      peg$c38 = peg$classExpectation([\"e\", \"E\"], false, false),\n      peg$c39 = function(e) { return [e[0], e[1], e[2].join(\"\")].join(\"\"); },\n      peg$c40 = /^[+\\-]/,\n      peg$c41 = peg$classExpectation([\"+\", \"-\"], false, false),\n      peg$c42 = /^[0-9]/,\n      peg$c43 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n      peg$c44 = /^[ \\t\\r\\n]/,\n      peg$c45 = peg$classExpectation([\" \", \"\\t\", \"\\r\", \"\\n\"], false, false),\n\n      peg$currPos          = 0,\n      peg$savedPos         = 0,\n      peg$posDetailsCache  = [{ line: 1, column: 1 }],\n      peg$maxFailPos       = 0,\n      peg$maxFailExpected  = [],\n      peg$silentFails      = 0,\n\n      peg$result;\n\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildStructuredError(\n      [peg$otherExpectation(description)],\n      input.substring(peg$savedPos, peg$currPos),\n      location\n    );\n  }\n\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildSimpleError(message, location);\n  }\n\n  function peg$literalExpectation(text, ignoreCase) {\n    return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n  }\n\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n  }\n\n  function peg$anyExpectation() {\n    return { type: \"any\" };\n  }\n\n  function peg$endExpectation() {\n    return { type: \"end\" };\n  }\n\n  function peg$otherExpectation(description) {\n    return { type: \"other\", description: description };\n  }\n\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos], p;\n\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n\n      details = peg$posDetailsCache[p];\n      details = {\n        line:   details.line,\n        column: details.column\n      };\n\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n\n        p++;\n      }\n\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n        endPosDetails   = peg$computePosDetails(endPos);\n\n    return {\n      start: {\n        offset: startPos,\n        line:   startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line:   endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) { return; }\n\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n\n    peg$maxFailExpected.push(expected);\n  }\n\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(\n      peg$SyntaxError.buildMessage(expected, found),\n      expected,\n      found,\n      location\n    );\n  }\n\n  function peg$parsetransformList() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsewsp();\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsetransforms();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c0(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsetransforms() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsetransform();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsecommaWsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsecommaWsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsetransforms();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetransform();\n    }\n\n    return s0;\n  }\n\n  function peg$parsetransform() {\n    var s0;\n\n    s0 = peg$parsematrix();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parsetranslate();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsescale();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parserotate();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parseskewX();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parseskewY();\n            }\n          }\n        }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parsematrix() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11, s12, s13, s14, s15, s16, s17;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c2) {\n      s1 = peg$c2;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c3); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWsp();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parsenumber();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parsecommaWsp();\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parsenumber();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsecommaWsp();\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parsenumber();\n                        if (s11 !== peg$FAILED) {\n                          s12 = peg$parsecommaWsp();\n                          if (s12 !== peg$FAILED) {\n                            s13 = peg$parsenumber();\n                            if (s13 !== peg$FAILED) {\n                              s14 = peg$parsecommaWsp();\n                              if (s14 !== peg$FAILED) {\n                                s15 = peg$parsenumber();\n                                if (s15 !== peg$FAILED) {\n                                  s16 = [];\n                                  s17 = peg$parsewsp();\n                                  while (s17 !== peg$FAILED) {\n                                    s16.push(s17);\n                                    s17 = peg$parsewsp();\n                                  }\n                                  if (s16 !== peg$FAILED) {\n                                    if (input.charCodeAt(peg$currPos) === 41) {\n                                      s17 = peg$c6;\n                                      peg$currPos++;\n                                    } else {\n                                      s17 = peg$FAILED;\n                                      if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                                    }\n                                    if (s17 !== peg$FAILED) {\n                                      peg$savedPos = s0;\n                                      s1 = peg$c8(s5, s7, s9, s11, s13, s15);\n                                      s0 = s1;\n                                    } else {\n                                      peg$currPos = s0;\n                                      s0 = peg$FAILED;\n                                    }\n                                  } else {\n                                    peg$currPos = s0;\n                                    s0 = peg$FAILED;\n                                  }\n                                } else {\n                                  peg$currPos = s0;\n                                  s0 = peg$FAILED;\n                                }\n                              } else {\n                                peg$currPos = s0;\n                                s0 = peg$FAILED;\n                              }\n                            } else {\n                              peg$currPos = s0;\n                              s0 = peg$FAILED;\n                            }\n                          } else {\n                            peg$currPos = s0;\n                            s0 = peg$FAILED;\n                          }\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsetranslate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 9) === peg$c9) {\n      s1 = peg$c9;\n      peg$currPos += 9;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c10); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c11(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsescale() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c12) {\n      s1 = peg$c12;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c13); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspNumber();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c14(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parserotate() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 6) === peg$c15) {\n      s1 = peg$c15;\n      peg$currPos += 6;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c16); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecommaWspTwoNumbers();\n              if (s6 === peg$FAILED) {\n                s6 = null;\n              }\n              if (s6 !== peg$FAILED) {\n                s7 = [];\n                s8 = peg$parsewsp();\n                while (s8 !== peg$FAILED) {\n                  s7.push(s8);\n                  s8 = peg$parsewsp();\n                }\n                if (s7 !== peg$FAILED) {\n                  if (input.charCodeAt(peg$currPos) === 41) {\n                    s8 = peg$c6;\n                    peg$currPos++;\n                  } else {\n                    s8 = peg$FAILED;\n                    if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                  }\n                  if (s8 !== peg$FAILED) {\n                    peg$savedPos = s0;\n                    s1 = peg$c17(s5, s6);\n                    s0 = s1;\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseskewX() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c18) {\n      s1 = peg$c18;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c19); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c20(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseskewY() {\n    var s0, s1, s2, s3, s4, s5, s6, s7;\n\n    s0 = peg$currPos;\n    if (input.substr(peg$currPos, 5) === peg$c21) {\n      s1 = peg$c21;\n      peg$currPos += 5;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c22); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 40) {\n          s3 = peg$c4;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c5); }\n        }\n        if (s3 !== peg$FAILED) {\n          s4 = [];\n          s5 = peg$parsewsp();\n          while (s5 !== peg$FAILED) {\n            s4.push(s5);\n            s5 = peg$parsewsp();\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = [];\n              s7 = peg$parsewsp();\n              while (s7 !== peg$FAILED) {\n                s6.push(s7);\n                s7 = peg$parsewsp();\n              }\n              if (s6 !== peg$FAILED) {\n                if (input.charCodeAt(peg$currPos) === 41) {\n                  s7 = peg$c6;\n                  peg$currPos++;\n                } else {\n                  s7 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c7); }\n                }\n                if (s7 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c23(s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsenumber() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsesign();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsefloatingPointConstant();\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c24(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsesign();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseintegerConstant();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c25(s1);\n      }\n      s0 = s1;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecommaWspNumber() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecommaWspTwoNumbers() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecommaWsp();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsenumber();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecommaWsp();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsenumber();\n          if (s4 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c27(s2, s4);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecommaWsp() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsewsp();\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s1 = [s1, s2, s3];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsecomma();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parsewsp();\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$parsewsp();\n        }\n        if (s2 !== peg$FAILED) {\n          s1 = [s1, s2];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parsecomma() {\n    var s0;\n\n    if (input.charCodeAt(peg$currPos) === 44) {\n      s0 = peg$c28;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c29); }\n    }\n\n    return s0;\n  }\n\n  function peg$parseintegerConstant() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c30(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsefloatingPointConstant() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsefractionalConstant();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseexponent();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c31(s1);\n    }\n    s0 = s1;\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsedigitSequence();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseexponent();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c32(s1);\n      }\n      s0 = s1;\n    }\n\n    return s0;\n  }\n\n  function peg$parsefractionalConstant() {\n    var s0, s1, s2, s3;\n\n    peg$silentFails++;\n    s0 = peg$currPos;\n    s1 = peg$parsedigitSequence();\n    if (s1 === peg$FAILED) {\n      s1 = null;\n    }\n    if (s1 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s2 = peg$c34;\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c35); }\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsedigitSequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c36(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$parsedigitSequence();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s2 = peg$c34;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c35); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c32(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    }\n    peg$silentFails--;\n    if (s0 === peg$FAILED) {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c33); }\n    }\n\n    return s0;\n  }\n\n  function peg$parseexponent() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (peg$c37.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c38); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsesign();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigitSequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c39(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsesign() {\n    var s0;\n\n    if (peg$c40.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c41); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsedigitSequence() {\n    var s0, s1;\n\n    s0 = [];\n    s1 = peg$parsedigit();\n    if (s1 !== peg$FAILED) {\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        s1 = peg$parsedigit();\n      }\n    } else {\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsedigit() {\n    var s0;\n\n    if (peg$c42.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c43); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsewsp() {\n    var s0;\n\n    if (peg$c44.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c45); }\n    }\n\n    return s0;\n  }\n\n\n      var deg2rad = Math.PI / 180;\n\n      /*\n       ╔═        ═╗   ╔═        ═╗   ╔═     ═╗\n       ║ al cl el ║   ║ ar cr er ║   ║ a c e ║\n       ║ bl dl fl ║ * ║ br dr fr ║ = ║ b d f ║\n       ║ 0  0  1  ║   ║ 0  0  1  ║   ║ 0 0 1 ║\n       ╚═        ═╝   ╚═        ═╝   ╚═     ═╝\n      */\n      function multiply_matrices(l, r) {\n          var al = l[0];\n          var cl = l[1];\n          var el = l[2];\n          var bl = l[3];\n          var dl = l[4];\n          var fl = l[5];\n\n          var ar = r[0];\n          var cr = r[1];\n          var er = r[2];\n          var br = r[3];\n          var dr = r[4];\n          var fr = r[5];\n\n          var a = al * ar + cl * br;\n          var c = al * cr + cl * dr;\n          var e = al * er + cl * fr + el;\n          var b = bl * ar + dl * br;\n          var d = bl * cr + dl * dr;\n          var f = bl * er + dl * fr + fl;\n\n          return [a, c, e, b, d, f];\n      }\n\n\n  peg$result = peg$startRuleFunction();\n\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n\n    throw peg$buildStructuredError(\n      peg$maxFailExpected,\n      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n      peg$maxFailPos < input.length\n        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n    );\n  }\n}\n\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse:       peg$parse\n};\n"], "mappings": "AAMA,YAAY;;AAEZ,SAASA,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnC,SAASC,IAAIA,CAAA,EAAG;IAAE,IAAI,CAACC,WAAW,GAAGH,KAAK;EAAE;EAC5CE,IAAI,CAACE,SAAS,GAAGH,MAAM,CAACG,SAAS;EACjCJ,KAAK,CAACI,SAAS,GAAG,IAAIF,IAAI,EAAE;AAC9B;AAEA,SAASG,eAAeA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC3D,IAAI,CAACH,OAAO,GAAIA,OAAO;EACvB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,KAAK,GAAMA,KAAK;EACrB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,IAAI,GAAO,aAAa;EAE7B,IAAI,OAAOC,KAAK,CAACC,iBAAiB,KAAK,UAAU,EAAE;IACjDD,KAAK,CAACC,iBAAiB,CAAC,IAAI,EAAEP,eAAe,CAAC;EAChD;AACF;AAEAN,YAAY,CAACM,eAAe,EAAEM,KAAK,CAAC;AAEpCN,eAAe,CAACQ,YAAY,GAAG,UAASN,QAAQ,EAAEC,KAAK,EAAE;EACvD,IAAIM,wBAAwB,GAAG;IACzBC,OAAO,EAAE,SAATA,OAAOA,CAAWC,WAAW,EAAE;MAC7B,OAAO,IAAI,GAAGC,aAAa,CAACD,WAAW,CAACE,IAAI,CAAC,GAAG,IAAI;IACtD,CAAC;IAED,OAAO,EAAE,SAATC,MAAOA,CAAWH,WAAW,EAAE;MAC7B,IAAII,YAAY,GAAG,EAAE;QACjBC,CAAC;MAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACM,KAAK,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC7CD,YAAY,IAAIJ,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,YAAYG,KAAK,GACjDC,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGI,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjFI,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC;MACvC;MAEA,OAAO,GAAG,IAAIL,WAAW,CAACU,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGN,YAAY,GAAG,GAAG;IACrE,CAAC;IAEDO,GAAG,EAAE,SAALA,GAAGA,CAAWX,WAAW,EAAE;MACzB,OAAO,eAAe;IACxB,CAAC;IAEDY,GAAG,EAAE,SAALA,GAAGA,CAAWZ,WAAW,EAAE;MACzB,OAAO,cAAc;IACvB,CAAC;IAEDa,KAAK,EAAE,SAAPA,KAAKA,CAAWb,WAAW,EAAE;MAC3B,OAAOA,WAAW,CAACc,WAAW;IAChC;EACF,CAAC;EAEL,SAASC,GAAGA,CAACC,EAAE,EAAE;IACf,OAAOA,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,EAAE;EACpD;EAEA,SAASlB,aAAaA,CAACmB,CAAC,EAAE;IACxB,OAAOA,CAAC,CACLC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAG,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,cAAc,EAAW,UAASL,EAAE,EAAE;MAAE,OAAO,MAAM,GAAGD,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC,CAC3EK,OAAO,CAAC,uBAAuB,EAAE,UAASL,EAAE,EAAE;MAAE,OAAO,KAAK,GAAID,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC;EAChF;EAEA,SAASP,WAAWA,CAACW,CAAC,EAAE;IACtB,OAAOA,CAAC,CACLC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAG,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,cAAc,EAAW,UAASL,EAAE,EAAE;MAAE,OAAO,MAAM,GAAGD,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC,CAC3EK,OAAO,CAAC,uBAAuB,EAAE,UAASL,EAAE,EAAE;MAAE,OAAO,KAAK,GAAID,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC;EAChF;EAEA,SAASM,mBAAmBA,CAACtB,WAAW,EAAE;IACxC,OAAOF,wBAAwB,CAACE,WAAW,CAACuB,IAAI,CAAC,CAACvB,WAAW,CAAC;EAChE;EAEA,SAASwB,gBAAgBA,CAACjC,QAAQ,EAAE;IAClC,IAAIkC,YAAY,GAAG,IAAIjB,KAAK,CAACjB,QAAQ,CAACgB,MAAM,CAAC;MACzCF,CAAC;MAAEqB,CAAC;IAER,KAAKrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,QAAQ,CAACgB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpCoB,YAAY,CAACpB,CAAC,CAAC,GAAGiB,mBAAmB,CAAC/B,QAAQ,CAACc,CAAC,CAAC,CAAC;IACpD;IAEAoB,YAAY,CAACE,IAAI,EAAE;IAEnB,IAAIF,YAAY,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC3B,KAAKF,CAAC,GAAG,CAAC,EAAEqB,CAAC,GAAG,CAAC,EAAErB,CAAC,GAAGoB,YAAY,CAAClB,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC/C,IAAIoB,YAAY,CAACpB,CAAC,GAAG,CAAC,CAAC,KAAKoB,YAAY,CAACpB,CAAC,CAAC,EAAE;UAC3CoB,YAAY,CAACC,CAAC,CAAC,GAAGD,YAAY,CAACpB,CAAC,CAAC;UACjCqB,CAAC,EAAE;QACL;MACF;MACAD,YAAY,CAAClB,MAAM,GAAGmB,CAAC;IACzB;IAEA,QAAQD,YAAY,CAAClB,MAAM;MACzB,KAAK,CAAC;QACJ,OAAOkB,YAAY,CAAC,CAAC,CAAC;MAExB,KAAK,CAAC;QACJ,OAAOA,YAAY,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,YAAY,CAAC,CAAC,CAAC;MAEnD;QACE,OAAOA,YAAY,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GACvC,OAAO,GACPJ,YAAY,CAACA,YAAY,CAAClB,MAAM,GAAG,CAAC,CAAC;IAAC;EAEhD;EAEA,SAASuB,aAAaA,CAACtC,KAAK,EAAE;IAC5B,OAAOA,KAAK,GAAG,IAAI,GAAGS,aAAa,CAACT,KAAK,CAAC,GAAG,IAAI,GAAG,cAAc;EACpE;EAEA,OAAO,WAAW,GAAGgC,gBAAgB,CAACjC,QAAQ,CAAC,GAAG,OAAO,GAAGuC,aAAa,CAACtC,KAAK,CAAC,GAAG,SAAS;AAC9F,CAAC;AAED,SAASuC,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACjCA,OAAO,GAAGA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC;EAE3C,IAAIC,UAAU,GAAG,CAAC,CAAC;IAEfC,sBAAsB,GAAG;MAAEC,aAAa,EAAEC;IAAuB,CAAC;IAClEC,qBAAqB,GAAID,sBAAsB;IAE/CE,MAAM,GAAG,SAATA,MAAMA,CAAYC,EAAE,EAAE;MAAE,OAAOA,EAAE;IAAE,CAAC;IACpCC,MAAM,GAAG,SAATA,MAAMA,CAAYC,CAAC,EAAEF,EAAE,EAAE;MACjB,OAAOG,iBAAiB,CAACD,CAAC,EAAEF,EAAE,CAAC;IACnC,CAAC;IACLI,MAAM,GAAG,QAAQ;IACjBC,MAAM,GAAGC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;IAChDC,MAAM,GAAG,GAAG;IACZC,MAAM,GAAGF,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC3CG,MAAM,GAAG,GAAG;IACZC,MAAM,GAAGJ,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC3CK,MAAM,GAAG,SAATA,MAAMA,CAAYC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;MAC5B,OAAO,CACHL,CAAC,EAAEE,CAAC,EAAEE,CAAC,EACPH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CACV;IACL,CAAC;IACLC,MAAM,GAAG,WAAW;IACpBC,OAAO,GAAGb,sBAAsB,CAAC,WAAW,EAAE,KAAK,CAAC;IACpDc,OAAO,GAAG,SAAVA,OAAOA,CAAYC,EAAE,EAAEC,EAAE,EAAE;MACnB,OAAO,CACH,CAAC,EAAE,CAAC,EAAED,EAAE,EACR,CAAC,EAAE,CAAC,EAAEC,EAAE,IAAI,CAAC,CAChB;IACL,CAAC;IACLC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGlB,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;IAChDmB,OAAO,GAAG,SAAVA,OAAOA,CAAYC,EAAE,EAAEC,EAAE,EAAE;MACnB,OAAO,CACHD,EAAE,EAAE,CAAC,EAAsB,CAAC,EAC5B,CAAC,EAAGC,EAAE,KAAK,IAAI,GAAGD,EAAE,GAAGC,EAAE,EAAE,CAAC,CAC/B;IACL,CAAC;IACLC,OAAO,GAAG,QAAQ;IAClBC,OAAO,GAAGvB,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;IACjDwB,OAAO,GAAG,SAAVA,OAAOA,CAAYC,KAAK,EAAEjB,CAAC,EAAE;MACrB,IAAIkB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACE,OAAO,GAAGH,KAAK,CAAC;MACnC,IAAII,GAAG,GAAGF,IAAI,CAACE,GAAG,CAACD,OAAO,GAAGH,KAAK,CAAC;MACnC,IAAIjB,CAAC,KAAK,IAAI,EAAE;QACZ,IAAIsB,CAAC,GAAGtB,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIuB,CAAC,GAAGvB,CAAC,CAAC,CAAC,CAAC;QACZ,OAAO,CACHkB,GAAG,EAAE,CAACG,GAAG,EAAEH,GAAG,GAAG,CAACI,CAAC,GAAG,CAACD,GAAG,GAAG,CAACE,CAAC,GAAGD,CAAC,EACnCD,GAAG,EAAGH,GAAG,EAAEG,GAAG,GAAG,CAACC,CAAC,GAAIJ,GAAG,GAAG,CAACK,CAAC,GAAGA,CAAC,CACtC;MACL;MACA,OAAO,CACHL,GAAG,EAAE,CAACG,GAAG,EAAE,CAAC,EACZA,GAAG,EAAGH,GAAG,EAAE,CAAC,CACf;IACL,CAAC;IACLM,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGjC,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;IAChDkC,OAAO,GAAG,SAAVA,OAAOA,CAAYT,KAAK,EAAE;MAClB,OAAO,CACH,CAAC,EAAEE,IAAI,CAACQ,GAAG,CAACP,OAAO,GAAGH,KAAK,CAAC,EAAE,CAAC,EAC/B,CAAC,EAAE,CAAC,EAA0B,CAAC,CAClC;IACL,CAAC;IACLW,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGrC,sBAAsB,CAAC,OAAO,EAAE,KAAK,CAAC;IAChDsC,OAAO,GAAG,SAAVA,OAAOA,CAAYb,KAAK,EAAE;MAClB,OAAO,CACH,CAAC,EAA0B,CAAC,EAAE,CAAC,EAC/BE,IAAI,CAACQ,GAAG,CAACP,OAAO,GAAGH,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,CAClC;IACL,CAAC;IACLc,OAAO,GAAG,SAAVA,OAAOA,CAAY5B,CAAC,EAAE;MAAE,OAAO6B,UAAU,CAAC7B,CAAC,CAAC5B,IAAI,CAAC,EAAE,CAAC,CAAC;IAAE,CAAC;IACxD0D,OAAO,GAAG,SAAVA,OAAOA,CAAYlF,CAAC,EAAE;MAAE,OAAOmF,QAAQ,CAACnF,CAAC,CAACwB,IAAI,CAAC,EAAE,CAAC,CAAC;IAAE,CAAC;IACtD4D,OAAO,GAAG,SAAVA,OAAOA,CAAYC,CAAC,EAAE;MAAE,OAAOA,CAAC;IAAE,CAAC;IACnCC,OAAO,GAAG,SAAVA,OAAOA,CAAYC,EAAE,EAAEC,EAAE,EAAE;MAAE,OAAO,CAACD,EAAE,EAAEC,EAAE,CAAC;IAAE,CAAC;IAC/CC,OAAO,GAAG,GAAG;IACbC,OAAO,GAAGjD,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC5CkD,OAAO,GAAG,SAAVA,OAAOA,CAAYC,EAAE,EAAE;MAAE,OAAOA,EAAE,CAACpE,IAAI,CAAC,EAAE,CAAC;IAAE,CAAC;IAC9CqE,OAAO,GAAG,SAAVA,OAAOA,CAAYzC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAAC5B,IAAI,CAAC,EAAE,CAAC;IAAE,CAAC;IAC5CsE,OAAO,GAAG,SAAVA,OAAOA,CAAY5C,CAAC,EAAE;MAAE,OAAOA,CAAC,CAAC1B,IAAI,CAAC,EAAE,CAAC;IAAE,CAAC;IAC5CuE,OAAO,GAAGC,oBAAoB,CAAC,oBAAoB,CAAC;IACpDC,OAAO,GAAG,GAAG;IACbC,OAAO,GAAGzD,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC5C0D,OAAO,GAAG,SAAVA,OAAOA,CAAYC,EAAE,EAAEC,EAAE,EAAE;MAAE,OAAO,CAACD,EAAE,GAAGA,EAAE,CAAC5E,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE6E,EAAE,CAAC7E,IAAI,CAAC,EAAE,CAAC,CAAC,CAACA,IAAI,CAAC,EAAE,CAAC;IAAE,CAAC;IAC3F8E,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGC,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDC,OAAO,GAAG,SAAVA,OAAOA,CAAYtD,CAAC,EAAE;MAAE,OAAO,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC3B,IAAI,CAAC,EAAE,CAAC,CAAC,CAACA,IAAI,CAAC,EAAE,CAAC;IAAE,CAAC;IACtEkF,OAAO,GAAG,QAAQ;IAClBC,OAAO,GAAGH,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDI,OAAO,GAAG,QAAQ;IAClBC,OAAO,GAAGL,oBAAoB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1DM,OAAO,GAAG,YAAY;IACtBC,OAAO,GAAGP,oBAAoB,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAErEQ,WAAW,GAAY,CAAC;IACxBC,YAAY,GAAW,CAAC;IACxBC,mBAAmB,GAAI,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IAC/CC,cAAc,GAAS,CAAC;IACxBC,mBAAmB,GAAI,EAAE;IACzBC,eAAe,GAAQ,CAAC;IAExBC,UAAU;EAEd,IAAI,WAAW,IAAI5F,OAAO,EAAE;IAC1B,IAAI,EAAEA,OAAO,CAAC6F,SAAS,IAAI3F,sBAAsB,CAAC,EAAE;MAClD,MAAM,IAAIxC,KAAK,CAAC,kCAAkC,GAAGsC,OAAO,CAAC6F,SAAS,GAAG,KAAK,CAAC;IACjF;IAEAxF,qBAAqB,GAAGH,sBAAsB,CAACF,OAAO,CAAC6F,SAAS,CAAC;EACnE;EAEA,SAAS5H,IAAIA,CAAA,EAAG;IACd,OAAO8B,KAAK,CAAC+F,SAAS,CAACT,YAAY,EAAED,WAAW,CAAC;EACnD;EAEA,SAAS5H,QAAQA,CAAA,EAAG;IAClB,OAAOuI,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;EACvD;EAEA,SAAS9H,QAAQA,CAACuB,WAAW,EAAErB,QAAQ,EAAE;IACvCA,QAAQ,GAAGA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGuI,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;IAE1F,MAAMY,wBAAwB,CAC5B,CAAC5B,oBAAoB,CAACvF,WAAW,CAAC,CAAC,EACnCkB,KAAK,CAAC+F,SAAS,CAACT,YAAY,EAAED,WAAW,CAAC,EAC1C5H,QAAQ,CACT;EACH;EAEA,SAASyI,KAAKA,CAAC5I,OAAO,EAAEG,QAAQ,EAAE;IAChCA,QAAQ,GAAGA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGuI,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;IAE1F,MAAMc,oBAAoB,CAAC7I,OAAO,EAAEG,QAAQ,CAAC;EAC/C;EAEA,SAASqD,sBAAsBA,CAAC5C,IAAI,EAAEkI,UAAU,EAAE;IAChD,OAAO;MAAE7G,IAAI,EAAE,SAAS;MAAErB,IAAI,EAAEA,IAAI;MAAEkI,UAAU,EAAEA;IAAW,CAAC;EAChE;EAEA,SAASvB,oBAAoBA,CAACvG,KAAK,EAAEI,QAAQ,EAAE0H,UAAU,EAAE;IACzD,OAAO;MAAE7G,IAAI,EAAE,OAAO;MAAEjB,KAAK,EAAEA,KAAK;MAAEI,QAAQ,EAAEA,QAAQ;MAAE0H,UAAU,EAAEA;IAAW,CAAC;EACpF;EAEA,SAASC,kBAAkBA,CAAA,EAAG;IAC5B,OAAO;MAAE9G,IAAI,EAAE;IAAM,CAAC;EACxB;EAEA,SAAS+G,kBAAkBA,CAAA,EAAG;IAC5B,OAAO;MAAE/G,IAAI,EAAE;IAAM,CAAC;EACxB;EAEA,SAAS8E,oBAAoBA,CAACvF,WAAW,EAAE;IACzC,OAAO;MAAES,IAAI,EAAE,OAAO;MAAET,WAAW,EAAEA;IAAY,CAAC;EACpD;EAEA,SAASyH,qBAAqBA,CAACC,GAAG,EAAE;IAClC,IAAIC,OAAO,GAAGlB,mBAAmB,CAACiB,GAAG,CAAC;MAAEE,CAAC;IAEzC,IAAID,OAAO,EAAE;MACX,OAAOA,OAAO;IAChB,CAAC,MAAM;MACLC,CAAC,GAAGF,GAAG,GAAG,CAAC;MACX,OAAO,CAACjB,mBAAmB,CAACmB,CAAC,CAAC,EAAE;QAC9BA,CAAC,EAAE;MACL;MAEAD,OAAO,GAAGlB,mBAAmB,CAACmB,CAAC,CAAC;MAChCD,OAAO,GAAG;QACRjB,IAAI,EAAIiB,OAAO,CAACjB,IAAI;QACpBC,MAAM,EAAEgB,OAAO,CAAChB;MAClB,CAAC;MAED,OAAOiB,CAAC,GAAGF,GAAG,EAAE;QACd,IAAIxG,KAAK,CAACf,UAAU,CAACyH,CAAC,CAAC,KAAK,EAAE,EAAE;UAC9BD,OAAO,CAACjB,IAAI,EAAE;UACdiB,OAAO,CAAChB,MAAM,GAAG,CAAC;QACpB,CAAC,MAAM;UACLgB,OAAO,CAAChB,MAAM,EAAE;QAClB;QAEAiB,CAAC,EAAE;MACL;MAEAnB,mBAAmB,CAACiB,GAAG,CAAC,GAAGC,OAAO;MAClC,OAAOA,OAAO;IAChB;EACF;EAEA,SAAST,mBAAmBA,CAACW,QAAQ,EAAEC,MAAM,EAAE;IAC7C,IAAIC,eAAe,GAAGN,qBAAqB,CAACI,QAAQ,CAAC;MACjDG,aAAa,GAAKP,qBAAqB,CAACK,MAAM,CAAC;IAEnD,OAAO;MACLG,KAAK,EAAE;QACLC,MAAM,EAAEL,QAAQ;QAChBnB,IAAI,EAAIqB,eAAe,CAACrB,IAAI;QAC5BC,MAAM,EAAEoB,eAAe,CAACpB;MAC1B,CAAC;MACD7G,GAAG,EAAE;QACHoI,MAAM,EAAEJ,MAAM;QACdpB,IAAI,EAAIsB,aAAa,CAACtB,IAAI;QAC1BC,MAAM,EAAEqB,aAAa,CAACrB;MACxB;IACF,CAAC;EACH;EAEA,SAASwB,QAAQA,CAAC1J,QAAQ,EAAE;IAC1B,IAAI8H,WAAW,GAAGK,cAAc,EAAE;MAAE;IAAQ;IAE5C,IAAIL,WAAW,GAAGK,cAAc,EAAE;MAChCA,cAAc,GAAGL,WAAW;MAC5BM,mBAAmB,GAAG,EAAE;IAC1B;IAEAA,mBAAmB,CAACuB,IAAI,CAAC3J,QAAQ,CAAC;EACpC;EAEA,SAAS4I,oBAAoBA,CAAC7I,OAAO,EAAEG,QAAQ,EAAE;IAC/C,OAAO,IAAIJ,eAAe,CAACC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEG,QAAQ,CAAC;EAC3D;EAEA,SAASwI,wBAAwBA,CAAC1I,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAC3D,OAAO,IAAIJ,eAAe,CACxBA,eAAe,CAACQ,YAAY,CAACN,QAAQ,EAAEC,KAAK,CAAC,EAC7CD,QAAQ,EACRC,KAAK,EACLC,QAAQ,CACT;EACH;EAEA,SAAS4C,sBAAsBA,CAAA,EAAG;IAChC,IAAI8G,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAG,EAAE;IACPC,EAAE,GAAGG,YAAY,EAAE;IACnB,OAAOH,EAAE,KAAKnH,UAAU,EAAE;MACxBkH,EAAE,CAACF,IAAI,CAACG,EAAE,CAAC;MACXA,EAAE,GAAGG,YAAY,EAAE;IACrB;IACA,IAAIJ,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAGI,mBAAmB,EAAE;MAC1B,IAAIJ,EAAE,KAAKnH,UAAU,EAAE;QACrBmH,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGC,YAAY,EAAE;QACnB,OAAOD,EAAE,KAAKrH,UAAU,EAAE;UACxBoH,EAAE,CAACJ,IAAI,CAACK,EAAE,CAAC;UACXA,EAAE,GAAGC,YAAY,EAAE;QACrB;QACA,IAAIF,EAAE,KAAKpH,UAAU,EAAE;UACrBoF,YAAY,GAAG6B,EAAE;UACjBC,EAAE,GAAG7G,MAAM,CAAC8G,EAAE,CAAC;UACfF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASM,mBAAmBA,CAAA,EAAG;IAC7B,IAAIN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAGM,kBAAkB,EAAE;IACzB,IAAIN,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGK,iBAAiB,EAAE;MACxB,OAAOL,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGK,iBAAiB,EAAE;MAC1B;MACA,IAAIN,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAGG,mBAAmB,EAAE;QAC1B,IAAIH,EAAE,KAAKpH,UAAU,EAAE;UACrBoF,YAAY,GAAG6B,EAAE;UACjBC,EAAE,GAAG3G,MAAM,CAAC2G,EAAE,EAAEE,EAAE,CAAC;UACnBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IACA,IAAIiH,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAGO,kBAAkB,EAAE;IAC3B;IAEA,OAAOP,EAAE;EACX;EAEA,SAASO,kBAAkBA,CAAA,EAAG;IAC5B,IAAIP,EAAE;IAENA,EAAE,GAAGS,eAAe,EAAE;IACtB,IAAIT,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAGU,kBAAkB,EAAE;MACzB,IAAIV,EAAE,KAAKjH,UAAU,EAAE;QACrBiH,EAAE,GAAGW,cAAc,EAAE;QACrB,IAAIX,EAAE,KAAKjH,UAAU,EAAE;UACrBiH,EAAE,GAAGY,eAAe,EAAE;UACtB,IAAIZ,EAAE,KAAKjH,UAAU,EAAE;YACrBiH,EAAE,GAAGa,cAAc,EAAE;YACrB,IAAIb,EAAE,KAAKjH,UAAU,EAAE;cACrBiH,EAAE,GAAGc,cAAc,EAAE;YACvB;UACF;QACF;MACF;IACF;IAEA,OAAOd,EAAE;EACX;EAEA,SAASS,eAAeA,CAAA,EAAG;IACzB,IAAIT,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG;IAElF3B,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAKzE,MAAM,EAAE;MAC3CwG,EAAE,GAAGxG,MAAM;MACXyE,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAACpG,MAAM,CAAC;MAAE;IACjD;IACA,IAAIuG,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAGR,iBAAiB,EAAE;cACxB,IAAIQ,EAAE,KAAKjI,UAAU,EAAE;gBACrBkI,EAAE,GAAGY,eAAe,EAAE;gBACtB,IAAIZ,EAAE,KAAKlI,UAAU,EAAE;kBACrBmI,EAAE,GAAGV,iBAAiB,EAAE;kBACxB,IAAIU,EAAE,KAAKnI,UAAU,EAAE;oBACrBoI,EAAE,GAAGU,eAAe,EAAE;oBACtB,IAAIV,EAAE,KAAKpI,UAAU,EAAE;sBACrBqI,GAAG,GAAGZ,iBAAiB,EAAE;sBACzB,IAAIY,GAAG,KAAKrI,UAAU,EAAE;wBACtBsI,GAAG,GAAGQ,eAAe,EAAE;wBACvB,IAAIR,GAAG,KAAKtI,UAAU,EAAE;0BACtBuI,GAAG,GAAGd,iBAAiB,EAAE;0BACzB,IAAIc,GAAG,KAAKvI,UAAU,EAAE;4BACtBwI,GAAG,GAAGM,eAAe,EAAE;4BACvB,IAAIN,GAAG,KAAKxI,UAAU,EAAE;8BACtByI,GAAG,GAAGhB,iBAAiB,EAAE;8BACzB,IAAIgB,GAAG,KAAKzI,UAAU,EAAE;gCACtB0I,GAAG,GAAGI,eAAe,EAAE;gCACvB,IAAIJ,GAAG,KAAK1I,UAAU,EAAE;kCACtB2I,GAAG,GAAG,EAAE;kCACRC,GAAG,GAAGtB,YAAY,EAAE;kCACpB,OAAOsB,GAAG,KAAK5I,UAAU,EAAE;oCACzB2I,GAAG,CAAC3B,IAAI,CAAC4B,GAAG,CAAC;oCACbA,GAAG,GAAGtB,YAAY,EAAE;kCACtB;kCACA,IAAIqB,GAAG,KAAK3I,UAAU,EAAE;oCACtB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;sCACxCyD,GAAG,GAAG7H,MAAM;sCACZoE,WAAW,EAAE;oCACf,CAAC,MAAM;sCACLyD,GAAG,GAAG5I,UAAU;sCAChB,IAAI0F,eAAe,KAAK,CAAC,EAAE;wCAAEqB,QAAQ,CAAC/F,MAAM,CAAC;sCAAE;oCACjD;oCACA,IAAI4H,GAAG,KAAK5I,UAAU,EAAE;sCACtBoF,YAAY,GAAG6B,EAAE;sCACjBC,EAAE,GAAGjG,MAAM,CAAC+G,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,GAAG,EAAEE,GAAG,EAAEE,GAAG,CAAC;sCACtCzB,EAAE,GAAGC,EAAE;oCACT,CAAC,MAAM;sCACL/B,WAAW,GAAG8B,EAAE;sCAChBA,EAAE,GAAGjH,UAAU;oCACjB;kCACF,CAAC,MAAM;oCACLmF,WAAW,GAAG8B,EAAE;oCAChBA,EAAE,GAAGjH,UAAU;kCACjB;gCACF,CAAC,MAAM;kCACLmF,WAAW,GAAG8B,EAAE;kCAChBA,EAAE,GAAGjH,UAAU;gCACjB;8BACF,CAAC,MAAM;gCACLmF,WAAW,GAAG8B,EAAE;gCAChBA,EAAE,GAAGjH,UAAU;8BACjB;4BACF,CAAC,MAAM;8BACLmF,WAAW,GAAG8B,EAAE;8BAChBA,EAAE,GAAGjH,UAAU;4BACjB;0BACF,CAAC,MAAM;4BACLmF,WAAW,GAAG8B,EAAE;4BAChBA,EAAE,GAAGjH,UAAU;0BACjB;wBACF,CAAC,MAAM;0BACLmF,WAAW,GAAG8B,EAAE;0BAChBA,EAAE,GAAGjH,UAAU;wBACjB;sBACF,CAAC,MAAM;wBACLmF,WAAW,GAAG8B,EAAE;wBAChBA,EAAE,GAAGjH,UAAU;sBACjB;oBACF,CAAC,MAAM;sBACLmF,WAAW,GAAG8B,EAAE;sBAChBA,EAAE,GAAGjH,UAAU;oBACjB;kBACF,CAAC,MAAM;oBACLmF,WAAW,GAAG8B,EAAE;oBAChBA,EAAE,GAAGjH,UAAU;kBACjB;gBACF,CAAC,MAAM;kBACLmF,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASU,kBAAkBA,CAAA,EAAG;IAC5B,IAAIV,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtClB,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAK3D,MAAM,EAAE;MAC3C0F,EAAE,GAAG1F,MAAM;MACX2D,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAACtF,OAAO,CAAC;MAAE;IAClD;IACA,IAAIyF,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAGc,uBAAuB,EAAE;cAC9B,IAAId,EAAE,KAAKjI,UAAU,EAAE;gBACrBiI,EAAE,GAAG,IAAI;cACX;cACA,IAAIA,EAAE,KAAKjI,UAAU,EAAE;gBACrBkI,EAAE,GAAG,EAAE;gBACPC,EAAE,GAAGb,YAAY,EAAE;gBACnB,OAAOa,EAAE,KAAKnI,UAAU,EAAE;kBACxBkI,EAAE,CAAClB,IAAI,CAACmB,EAAE,CAAC;kBACXA,EAAE,GAAGb,YAAY,EAAE;gBACrB;gBACA,IAAIY,EAAE,KAAKlI,UAAU,EAAE;kBACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;oBACxCgD,EAAE,GAAGpH,MAAM;oBACXoE,WAAW,EAAE;kBACf,CAAC,MAAM;oBACLgD,EAAE,GAAGnI,UAAU;oBACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;sBAAEqB,QAAQ,CAAC/F,MAAM,CAAC;oBAAE;kBACjD;kBACA,IAAImH,EAAE,KAAKnI,UAAU,EAAE;oBACrBoF,YAAY,GAAG6B,EAAE;oBACjBC,EAAE,GAAGxF,OAAO,CAACsG,EAAE,EAAEC,EAAE,CAAC;oBACpBhB,EAAE,GAAGC,EAAE;kBACT,CAAC,MAAM;oBACL/B,WAAW,GAAG8B,EAAE;oBAChBA,EAAE,GAAGjH,UAAU;kBACjB;gBACF,CAAC,MAAM;kBACLmF,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASW,cAAcA,CAAA,EAAG;IACxB,IAAIX,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtClB,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAKtD,OAAO,EAAE;MAC5CqF,EAAE,GAAGrF,OAAO;MACZsD,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAACjF,OAAO,CAAC;MAAE;IAClD;IACA,IAAIoF,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAGc,uBAAuB,EAAE;cAC9B,IAAId,EAAE,KAAKjI,UAAU,EAAE;gBACrBiI,EAAE,GAAG,IAAI;cACX;cACA,IAAIA,EAAE,KAAKjI,UAAU,EAAE;gBACrBkI,EAAE,GAAG,EAAE;gBACPC,EAAE,GAAGb,YAAY,EAAE;gBACnB,OAAOa,EAAE,KAAKnI,UAAU,EAAE;kBACxBkI,EAAE,CAAClB,IAAI,CAACmB,EAAE,CAAC;kBACXA,EAAE,GAAGb,YAAY,EAAE;gBACrB;gBACA,IAAIY,EAAE,KAAKlI,UAAU,EAAE;kBACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;oBACxCgD,EAAE,GAAGpH,MAAM;oBACXoE,WAAW,EAAE;kBACf,CAAC,MAAM;oBACLgD,EAAE,GAAGnI,UAAU;oBACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;sBAAEqB,QAAQ,CAAC/F,MAAM,CAAC;oBAAE;kBACjD;kBACA,IAAImH,EAAE,KAAKnI,UAAU,EAAE;oBACrBoF,YAAY,GAAG6B,EAAE;oBACjBC,EAAE,GAAGnF,OAAO,CAACiG,EAAE,EAAEC,EAAE,CAAC;oBACpBhB,EAAE,GAAGC,EAAE;kBACT,CAAC,MAAM;oBACL/B,WAAW,GAAG8B,EAAE;oBAChBA,EAAE,GAAGjH,UAAU;kBACjB;gBACF,CAAC,MAAM;kBACLmF,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASY,eAAeA,CAAA,EAAG;IACzB,IAAIZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtClB,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAKjD,OAAO,EAAE;MAC5CgF,EAAE,GAAGhF,OAAO;MACZiD,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAC5E,OAAO,CAAC;MAAE;IAClD;IACA,IAAI+E,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAGe,2BAA2B,EAAE;cAClC,IAAIf,EAAE,KAAKjI,UAAU,EAAE;gBACrBiI,EAAE,GAAG,IAAI;cACX;cACA,IAAIA,EAAE,KAAKjI,UAAU,EAAE;gBACrBkI,EAAE,GAAG,EAAE;gBACPC,EAAE,GAAGb,YAAY,EAAE;gBACnB,OAAOa,EAAE,KAAKnI,UAAU,EAAE;kBACxBkI,EAAE,CAAClB,IAAI,CAACmB,EAAE,CAAC;kBACXA,EAAE,GAAGb,YAAY,EAAE;gBACrB;gBACA,IAAIY,EAAE,KAAKlI,UAAU,EAAE;kBACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;oBACxCgD,EAAE,GAAGpH,MAAM;oBACXoE,WAAW,EAAE;kBACf,CAAC,MAAM;oBACLgD,EAAE,GAAGnI,UAAU;oBACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;sBAAEqB,QAAQ,CAAC/F,MAAM,CAAC;oBAAE;kBACjD;kBACA,IAAImH,EAAE,KAAKnI,UAAU,EAAE;oBACrBoF,YAAY,GAAG6B,EAAE;oBACjBC,EAAE,GAAG9E,OAAO,CAAC4F,EAAE,EAAEC,EAAE,CAAC;oBACpBhB,EAAE,GAAGC,EAAE;kBACT,CAAC,MAAM;oBACL/B,WAAW,GAAG8B,EAAE;oBAChBA,EAAE,GAAGjH,UAAU;kBACjB;gBACF,CAAC,MAAM;kBACLmF,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASa,cAAcA,CAAA,EAAG;IACxB,IAAIb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElCjB,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAKvC,OAAO,EAAE;MAC5CsE,EAAE,GAAGtE,OAAO;MACZuC,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAClE,OAAO,CAAC;MAAE;IAClD;IACA,IAAIqE,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAG,EAAE;cACPC,EAAE,GAAGZ,YAAY,EAAE;cACnB,OAAOY,EAAE,KAAKlI,UAAU,EAAE;gBACxBiI,EAAE,CAACjB,IAAI,CAACkB,EAAE,CAAC;gBACXA,EAAE,GAAGZ,YAAY,EAAE;cACrB;cACA,IAAIW,EAAE,KAAKjI,UAAU,EAAE;gBACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;kBACxC+C,EAAE,GAAGnH,MAAM;kBACXoE,WAAW,EAAE;gBACf,CAAC,MAAM;kBACL+C,EAAE,GAAGlI,UAAU;kBACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;oBAAEqB,QAAQ,CAAC/F,MAAM,CAAC;kBAAE;gBACjD;gBACA,IAAIkH,EAAE,KAAKlI,UAAU,EAAE;kBACrBoF,YAAY,GAAG6B,EAAE;kBACjBC,EAAE,GAAGpE,OAAO,CAACkF,EAAE,CAAC;kBAChBf,EAAE,GAAGC,EAAE;gBACT,CAAC,MAAM;kBACL/B,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASc,cAAcA,CAAA,EAAG;IACxB,IAAId,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElCjB,EAAE,GAAG9B,WAAW;IAChB,IAAIrF,KAAK,CAAC+I,MAAM,CAAC1D,WAAW,EAAE,CAAC,CAAC,KAAKnC,OAAO,EAAE;MAC5CkE,EAAE,GAAGlE,OAAO;MACZmC,WAAW,IAAI,CAAC;IAClB,CAAC,MAAM;MACL+B,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAC9D,OAAO,CAAC;MAAE;IAClD;IACA,IAAIiE,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,EAAE;MACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;QACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,EAAE;MACrB;MACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCiC,EAAE,GAAGvG,MAAM;UACXsE,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAGpH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAACjG,MAAM,CAAC;UAAE;QACjD;QACA,IAAIsG,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAG,EAAE;UACPW,EAAE,GAAGV,YAAY,EAAE;UACnB,OAAOU,EAAE,KAAKhI,UAAU,EAAE;YACxBqH,EAAE,CAACL,IAAI,CAACgB,EAAE,CAAC;YACXA,EAAE,GAAGV,YAAY,EAAE;UACrB;UACA,IAAID,EAAE,KAAKrH,UAAU,EAAE;YACrBgI,EAAE,GAAGc,eAAe,EAAE;YACtB,IAAId,EAAE,KAAKhI,UAAU,EAAE;cACrBiI,EAAE,GAAG,EAAE;cACPC,EAAE,GAAGZ,YAAY,EAAE;cACnB,OAAOY,EAAE,KAAKlI,UAAU,EAAE;gBACxBiI,EAAE,CAACjB,IAAI,CAACkB,EAAE,CAAC;gBACXA,EAAE,GAAGZ,YAAY,EAAE;cACrB;cACA,IAAIW,EAAE,KAAKjI,UAAU,EAAE;gBACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;kBACxC+C,EAAE,GAAGnH,MAAM;kBACXoE,WAAW,EAAE;gBACf,CAAC,MAAM;kBACL+C,EAAE,GAAGlI,UAAU;kBACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;oBAAEqB,QAAQ,CAAC/F,MAAM,CAAC;kBAAE;gBACjD;gBACA,IAAIkH,EAAE,KAAKlI,UAAU,EAAE;kBACrBoF,YAAY,GAAG6B,EAAE;kBACjBC,EAAE,GAAGhE,OAAO,CAAC8E,EAAE,CAAC;kBAChBf,EAAE,GAAGC,EAAE;gBACT,CAAC,MAAM;kBACL/B,WAAW,GAAG8B,EAAE;kBAChBA,EAAE,GAAGjH,UAAU;gBACjB;cACF,CAAC,MAAM;gBACLmF,WAAW,GAAG8B,EAAE;gBAChBA,EAAE,GAAGjH,UAAU;cACjB;YACF,CAAC,MAAM;cACLmF,WAAW,GAAG8B,EAAE;cAChBA,EAAE,GAAGjH,UAAU;YACjB;UACF,CAAC,MAAM;YACLmF,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAAS6B,eAAeA,CAAA,EAAG;IACzB,IAAI7B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG8B,aAAa,EAAE;IACpB,IAAI9B,EAAE,KAAKnH,UAAU,EAAE;MACrBmH,EAAE,GAAG,IAAI;IACX;IACA,IAAIA,EAAE,KAAKnH,UAAU,EAAE;MACrBoH,EAAE,GAAG8B,8BAA8B,EAAE;MACrC,IAAI9B,EAAE,KAAKpH,UAAU,EAAE;QACrBmH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;QACbF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAGlH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAGlH,UAAU;IACjB;IACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;MACrBoF,YAAY,GAAG6B,EAAE;MACjBC,EAAE,GAAG/D,OAAO,CAAC+D,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IACP,IAAID,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAG9B,WAAW;MAChB+B,EAAE,GAAG/B,WAAW;MAChBgC,EAAE,GAAG8B,aAAa,EAAE;MACpB,IAAI9B,EAAE,KAAKnH,UAAU,EAAE;QACrBmH,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAG+B,wBAAwB,EAAE;QAC/B,IAAI/B,EAAE,KAAKpH,UAAU,EAAE;UACrBmH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAGlH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAGlH,UAAU;MACjB;MACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;QACrBoF,YAAY,GAAG6B,EAAE;QACjBC,EAAE,GAAG7D,OAAO,CAAC6D,EAAE,CAAC;MAClB;MACAD,EAAE,GAAGC,EAAE;IACT;IAEA,OAAOD,EAAE;EACX;EAEA,SAAS8B,uBAAuBA,CAAA,EAAG;IACjC,IAAI9B,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEdF,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAGO,iBAAiB,EAAE;IACxB,IAAIP,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG2B,eAAe,EAAE;MACtB,IAAI3B,EAAE,KAAKnH,UAAU,EAAE;QACrBoF,YAAY,GAAG6B,EAAE;QACjBC,EAAE,GAAG3D,OAAO,CAAC4D,EAAE,CAAC;QAChBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACL/B,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAAS+B,2BAA2BA,CAAA,EAAG;IACrC,IAAI/B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAGO,iBAAiB,EAAE;IACxB,IAAIP,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAG2B,eAAe,EAAE;MACtB,IAAI3B,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAGK,iBAAiB,EAAE;QACxB,IAAIL,EAAE,KAAKpH,UAAU,EAAE;UACrBqH,EAAE,GAAGyB,eAAe,EAAE;UACtB,IAAIzB,EAAE,KAAKrH,UAAU,EAAE;YACrBoF,YAAY,GAAG6B,EAAE;YACjBC,EAAE,GAAGzD,OAAO,CAAC0D,EAAE,EAAEE,EAAE,CAAC;YACpBJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACL/B,WAAW,GAAG8B,EAAE;YAChBA,EAAE,GAAGjH,UAAU;UACjB;QACF,CAAC,MAAM;UACLmF,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASQ,iBAAiBA,CAAA,EAAG;IAC3B,IAAIR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAG,EAAE;IACPC,EAAE,GAAGG,YAAY,EAAE;IACnB,IAAIH,EAAE,KAAKnH,UAAU,EAAE;MACrB,OAAOmH,EAAE,KAAKnH,UAAU,EAAE;QACxBkH,EAAE,CAACF,IAAI,CAACG,EAAE,CAAC;QACXA,EAAE,GAAGG,YAAY,EAAE;MACrB;IACF,CAAC,MAAM;MACLJ,EAAE,GAAGlH,UAAU;IACjB;IACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;MACrBmH,EAAE,GAAGiC,cAAc,EAAE;MACrB,IAAIjC,EAAE,KAAKnH,UAAU,EAAE;QACrBmH,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGC,YAAY,EAAE;QACnB,OAAOD,EAAE,KAAKrH,UAAU,EAAE;UACxBoH,EAAE,CAACJ,IAAI,CAACK,EAAE,CAAC;UACXA,EAAE,GAAGC,YAAY,EAAE;QACrB;QACA,IAAIF,EAAE,KAAKpH,UAAU,EAAE;UACrBkH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UACjBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IACA,IAAIiH,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAG9B,WAAW;MAChB+B,EAAE,GAAGkC,cAAc,EAAE;MACrB,IAAIlC,EAAE,KAAKlH,UAAU,EAAE;QACrBmH,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGE,YAAY,EAAE;QACnB,OAAOF,EAAE,KAAKpH,UAAU,EAAE;UACxBmH,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;UACXA,EAAE,GAAGE,YAAY,EAAE;QACrB;QACA,IAAIH,EAAE,KAAKnH,UAAU,EAAE;UACrBkH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASmC,cAAcA,CAAA,EAAG;IACxB,IAAInC,EAAE;IAEN,IAAInH,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;MACxC8B,EAAE,GAAGrD,OAAO;MACZuB,WAAW,EAAE;IACf,CAAC,MAAM;MACL8B,EAAE,GAAGjH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAClD,OAAO,CAAC;MAAE;IAClD;IAEA,OAAOoD,EAAE;EACX;EAEA,SAASkC,wBAAwBA,CAAA,EAAG;IAClC,IAAIlC,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAGmC,sBAAsB,EAAE;IAC7B,IAAInC,EAAE,KAAKlH,UAAU,EAAE;MACrBoF,YAAY,GAAG6B,EAAE;MACjBC,EAAE,GAAGpD,OAAO,CAACoD,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASiC,8BAA8BA,CAAA,EAAG;IACxC,IAAIjC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGmC,2BAA2B,EAAE;IAClC,IAAInC,EAAE,KAAKnH,UAAU,EAAE;MACrBoH,EAAE,GAAGmC,iBAAiB,EAAE;MACxB,IAAInC,EAAE,KAAKpH,UAAU,EAAE;QACrBoH,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAKpH,UAAU,EAAE;QACrBmH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;QACbF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAGlH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAGlH,UAAU;IACjB;IACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;MACrBoF,YAAY,GAAG6B,EAAE;MACjBC,EAAE,GAAGlD,OAAO,CAACkD,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IACP,IAAID,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAG9B,WAAW;MAChB+B,EAAE,GAAG/B,WAAW;MAChBgC,EAAE,GAAGkC,sBAAsB,EAAE;MAC7B,IAAIlC,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAGmC,iBAAiB,EAAE;QACxB,IAAInC,EAAE,KAAKpH,UAAU,EAAE;UACrBmH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAGlH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAGlH,UAAU;MACjB;MACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;QACrBoF,YAAY,GAAG6B,EAAE;QACjBC,EAAE,GAAGjD,OAAO,CAACiD,EAAE,CAAC;MAClB;MACAD,EAAE,GAAGC,EAAE;IACT;IAEA,OAAOD,EAAE;EACX;EAEA,SAASqC,2BAA2BA,CAAA,EAAG;IACrC,IAAIrC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElB1B,eAAe,EAAE;IACjBuB,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAGmC,sBAAsB,EAAE;IAC7B,IAAInC,EAAE,KAAKlH,UAAU,EAAE;MACrBkH,EAAE,GAAG,IAAI;IACX;IACA,IAAIA,EAAE,KAAKlH,UAAU,EAAE;MACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;QACxCgC,EAAE,GAAG/C,OAAO;QACZe,WAAW,EAAE;MACf,CAAC,MAAM;QACLgC,EAAE,GAAGnH,UAAU;QACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;UAAEqB,QAAQ,CAAC1C,OAAO,CAAC;QAAE;MAClD;MACA,IAAI8C,EAAE,KAAKnH,UAAU,EAAE;QACrBoH,EAAE,GAAGiC,sBAAsB,EAAE;QAC7B,IAAIjC,EAAE,KAAKpH,UAAU,EAAE;UACrBoF,YAAY,GAAG6B,EAAE;UACjBC,EAAE,GAAG5C,OAAO,CAAC4C,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG8B,EAAE;MAChBA,EAAE,GAAGjH,UAAU;IACjB;IACA,IAAIiH,EAAE,KAAKjH,UAAU,EAAE;MACrBiH,EAAE,GAAG9B,WAAW;MAChB+B,EAAE,GAAGmC,sBAAsB,EAAE;MAC7B,IAAInC,EAAE,KAAKlH,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAACoG,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCgC,EAAE,GAAG/C,OAAO;UACZe,WAAW,EAAE;QACf,CAAC,MAAM;UACLgC,EAAE,GAAGnH,UAAU;UACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;YAAEqB,QAAQ,CAAC1C,OAAO,CAAC;UAAE;QAClD;QACA,IAAI8C,EAAE,KAAKnH,UAAU,EAAE;UACrBoF,YAAY,GAAG6B,EAAE;UACjBC,EAAE,GAAGjD,OAAO,CAACiD,EAAE,CAAC;UAChBD,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACL/B,WAAW,GAAG8B,EAAE;UAChBA,EAAE,GAAGjH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG8B,EAAE;QAChBA,EAAE,GAAGjH,UAAU;MACjB;IACF;IACA0F,eAAe,EAAE;IACjB,IAAIuB,EAAE,KAAKjH,UAAU,EAAE;MACrBkH,EAAE,GAAGlH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAC7C,OAAO,CAAC;MAAE;IAClD;IAEA,OAAO+C,EAAE;EACX;EAEA,SAASsC,iBAAiBA,CAAA,EAAG;IAC3B,IAAItC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG9B,WAAW;IAChB+B,EAAE,GAAG/B,WAAW;IAChB,IAAIV,OAAO,CAAC+E,IAAI,CAAC1J,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAGrH,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAGnH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAACrC,OAAO,CAAC;MAAE;IAClD;IACA,IAAIyC,EAAE,KAAKnH,UAAU,EAAE;MACrBoH,EAAE,GAAG6B,aAAa,EAAE;MACpB,IAAI7B,EAAE,KAAKpH,UAAU,EAAE;QACrBoH,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAKpH,UAAU,EAAE;QACrBqH,EAAE,GAAGgC,sBAAsB,EAAE;QAC7B,IAAIhC,EAAE,KAAKrH,UAAU,EAAE;UACrBmH,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UACjBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAGlH,UAAU;QACjB;MACF,CAAC,MAAM;QACLmF,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAGlH,UAAU;MACjB;IACF,CAAC,MAAM;MACLmF,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAGlH,UAAU;IACjB;IACA,IAAIkH,EAAE,KAAKlH,UAAU,EAAE;MACrBoF,YAAY,GAAG6B,EAAE;MACjBC,EAAE,GAAGtC,OAAO,CAACsC,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASgC,aAAaA,CAAA,EAAG;IACvB,IAAIhC,EAAE;IAEN,IAAIpC,OAAO,CAAC2E,IAAI,CAAC1J,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC,CAAC,EAAE;MAC3C8B,EAAE,GAAGnH,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACL8B,EAAE,GAAGjH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAACjC,OAAO,CAAC;MAAE;IAClD;IAEA,OAAOmC,EAAE;EACX;EAEA,SAASoC,sBAAsBA,CAAA,EAAG;IAChC,IAAIpC,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG,EAAE;IACPC,EAAE,GAAGwC,cAAc,EAAE;IACrB,IAAIxC,EAAE,KAAKlH,UAAU,EAAE;MACrB,OAAOkH,EAAE,KAAKlH,UAAU,EAAE;QACxBiH,EAAE,CAACD,IAAI,CAACE,EAAE,CAAC;QACXA,EAAE,GAAGwC,cAAc,EAAE;MACvB;IACF,CAAC,MAAM;MACLzC,EAAE,GAAGjH,UAAU;IACjB;IAEA,OAAOiH,EAAE;EACX;EAEA,SAASyC,cAAcA,CAAA,EAAG;IACxB,IAAIzC,EAAE;IAEN,IAAIlC,OAAO,CAACyE,IAAI,CAAC1J,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC,CAAC,EAAE;MAC3C8B,EAAE,GAAGnH,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACL8B,EAAE,GAAGjH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAC/B,OAAO,CAAC;MAAE;IAClD;IAEA,OAAOiC,EAAE;EACX;EAEA,SAASK,YAAYA,CAAA,EAAG;IACtB,IAAIL,EAAE;IAEN,IAAIhC,OAAO,CAACuE,IAAI,CAAC1J,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC,CAAC,EAAE;MAC3C8B,EAAE,GAAGnH,KAAK,CAAC2J,MAAM,CAACtE,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACL8B,EAAE,GAAGjH,UAAU;MACf,IAAI0F,eAAe,KAAK,CAAC,EAAE;QAAEqB,QAAQ,CAAC7B,OAAO,CAAC;MAAE;IAClD;IAEA,OAAO+B,EAAE;EACX;EAGI,IAAIzE,OAAO,GAAGD,IAAI,CAACoH,EAAE,GAAG,GAAG;EAS3B,SAASlJ,iBAAiBA,CAACmJ,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAIC,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;IACb,IAAIG,EAAE,GAAGH,CAAC,CAAC,CAAC,CAAC;IACb,IAAII,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IACb,IAAIK,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC;IACb,IAAIM,EAAE,GAAGN,CAAC,CAAC,CAAC,CAAC;IACb,IAAIO,EAAE,GAAGP,CAAC,CAAC,CAAC,CAAC;IAEb,IAAIQ,EAAE,GAAGP,CAAC,CAAC,CAAC,CAAC;IACb,IAAIQ,EAAE,GAAGR,CAAC,CAAC,CAAC,CAAC;IACb,IAAIS,EAAE,GAAGT,CAAC,CAAC,CAAC,CAAC;IACb,IAAIU,EAAE,GAAGV,CAAC,CAAC,CAAC,CAAC;IACb,IAAIW,EAAE,GAAGX,CAAC,CAAC,CAAC,CAAC;IACb,IAAIY,EAAE,GAAGZ,CAAC,CAAC,CAAC,CAAC;IAEb,IAAI3I,CAAC,GAAG4I,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGQ,EAAE;IACzB,IAAInJ,CAAC,GAAG0I,EAAE,GAAGO,EAAE,GAAGN,EAAE,GAAGS,EAAE;IACzB,IAAIlJ,CAAC,GAAGwI,EAAE,GAAGQ,EAAE,GAAGP,EAAE,GAAGU,EAAE,GAAGT,EAAE;IAC9B,IAAI7I,CAAC,GAAG8I,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGK,EAAE;IACzB,IAAIlJ,CAAC,GAAG4I,EAAE,GAAGI,EAAE,GAAGH,EAAE,GAAGM,EAAE;IACzB,IAAIjJ,CAAC,GAAG0I,EAAE,GAAGK,EAAE,GAAGJ,EAAE,GAAGO,EAAE,GAAGN,EAAE;IAE9B,OAAO,CAACjJ,CAAC,EAAEE,CAAC,EAAEE,CAAC,EAAEH,CAAC,EAAEE,CAAC,EAAEE,CAAC,CAAC;EAC7B;EAGJoE,UAAU,GAAGvF,qBAAqB,EAAE;EAEpC,IAAIuF,UAAU,KAAK3F,UAAU,IAAImF,WAAW,KAAKrF,KAAK,CAACzB,MAAM,EAAE;IAC7D,OAAOsH,UAAU;EACnB,CAAC,MAAM;IACL,IAAIA,UAAU,KAAK3F,UAAU,IAAImF,WAAW,GAAGrF,KAAK,CAACzB,MAAM,EAAE;MAC3D0I,QAAQ,CAACX,kBAAkB,EAAE,CAAC;IAChC;IAEA,MAAML,wBAAwB,CAC5BN,mBAAmB,EACnBD,cAAc,GAAG1F,KAAK,CAACzB,MAAM,GAAGyB,KAAK,CAAC2J,MAAM,CAACjE,cAAc,CAAC,GAAG,IAAI,EACnEA,cAAc,GAAG1F,KAAK,CAACzB,MAAM,GACzByH,mBAAmB,CAACN,cAAc,EAAEA,cAAc,GAAG,CAAC,CAAC,GACvDM,mBAAmB,CAACN,cAAc,EAAEA,cAAc,CAAC,CACxD;EACH;AACF;AAEAkF,MAAM,CAACC,OAAO,GAAG;EACfC,WAAW,EAAEzN,eAAe;EAC5B0N,KAAK,EAAQhL;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}