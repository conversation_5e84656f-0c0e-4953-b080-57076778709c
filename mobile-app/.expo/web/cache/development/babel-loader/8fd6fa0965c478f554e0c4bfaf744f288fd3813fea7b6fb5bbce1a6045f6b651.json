{"ast": null, "code": "'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-label\", \"accessibilityLabel\", \"blurRadius\", \"defaultSource\", \"draggable\", \"onError\", \"onLayout\", \"onLoad\", \"onLoadEnd\", \"onLoadStart\", \"pointerEvents\", \"source\", \"style\"];\nimport * as React from 'react';\nimport createElement from \"../createElement\";\nimport { getAssetByID } from \"../../modules/AssetRegistry\";\nimport { createBoxShadowValue } from \"../StyleSheet/preprocess\";\nimport ImageLoader from \"../../modules/ImageLoader\";\nimport PixelRatio from \"../PixelRatio\";\nimport StyleSheet from \"../StyleSheet\";\nimport TextAncestorContext from \"../Text/TextAncestorContext\";\nimport View from \"../View\";\nimport { warnOnce } from \"../../modules/warnOnce\";\nvar ERRORED = 'ERRORED';\nvar LOADED = 'LOADED';\nvar LOADING = 'LOADING';\nvar IDLE = 'IDLE';\nvar _filterId = 0;\nvar svgDataUriPattern = /^(data:image\\/svg\\+xml;utf8,)(.*)/;\nfunction createTintColorSVG(tintColor, id) {\n  return tintColor && id != null ? React.createElement(\"svg\", {\n    style: {\n      position: 'absolute',\n      height: 0,\n      visibility: 'hidden',\n      width: 0\n    }\n  }, React.createElement(\"defs\", null, React.createElement(\"filter\", {\n    id: \"tint-\" + id,\n    suppressHydrationWarning: true\n  }, React.createElement(\"feFlood\", {\n    floodColor: \"\" + tintColor,\n    key: tintColor\n  }), React.createElement(\"feComposite\", {\n    in2: \"SourceAlpha\",\n    operator: \"in\"\n  })))) : null;\n}\nfunction extractNonStandardStyleProps(style, blurRadius, filterId, tintColorProp) {\n  var flatStyle = StyleSheet.flatten(style);\n  var filter = flatStyle.filter,\n    resizeMode = flatStyle.resizeMode,\n    shadowOffset = flatStyle.shadowOffset,\n    tintColor = flatStyle.tintColor;\n  if (flatStyle.resizeMode) {\n    warnOnce('Image.style.resizeMode', 'Image: style.resizeMode is deprecated. Please use props.resizeMode.');\n  }\n  if (flatStyle.tintColor) {\n    warnOnce('Image.style.tintColor', 'Image: style.tintColor is deprecated. Please use props.tintColor.');\n  }\n  var filters = [];\n  var _filter = null;\n  if (filter) {\n    filters.push(filter);\n  }\n  if (blurRadius) {\n    filters.push(\"blur(\" + blurRadius + \"px)\");\n  }\n  if (shadowOffset) {\n    var shadowString = createBoxShadowValue(flatStyle);\n    if (shadowString) {\n      filters.push(\"drop-shadow(\" + shadowString + \")\");\n    }\n  }\n  if ((tintColorProp || tintColor) && filterId != null) {\n    filters.push(\"url(#tint-\" + filterId + \")\");\n  }\n  if (filters.length > 0) {\n    _filter = filters.join(' ');\n  }\n  return [resizeMode, _filter, tintColor];\n}\nfunction resolveAssetDimensions(source) {\n  if (typeof source === 'number') {\n    var _getAssetByID = getAssetByID(source),\n      _height = _getAssetByID.height,\n      _width = _getAssetByID.width;\n    return {\n      height: _height,\n      width: _width\n    };\n  } else if (source != null && !Array.isArray(source) && typeof source === 'object') {\n    var _height2 = source.height,\n      _width2 = source.width;\n    return {\n      height: _height2,\n      width: _width2\n    };\n  }\n}\nfunction resolveAssetUri(source) {\n  var uri = null;\n  if (typeof source === 'number') {\n    var asset = getAssetByID(source);\n    if (asset == null) {\n      throw new Error(\"Image: asset with ID \\\"\" + source + \"\\\" could not be found. Please check the image source or packager.\");\n    }\n    var scale = asset.scales[0];\n    if (asset.scales.length > 1) {\n      var preferredScale = PixelRatio.get();\n      scale = asset.scales.reduce(function (prev, curr) {\n        return Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev;\n      });\n    }\n    var scaleSuffix = scale !== 1 ? \"@\" + scale + \"x\" : '';\n    uri = asset ? asset.httpServerLocation + \"/\" + asset.name + scaleSuffix + \".\" + asset.type : '';\n  } else if (typeof source === 'string') {\n    uri = source;\n  } else if (source && typeof source.uri === 'string') {\n    uri = source.uri;\n  }\n  if (uri) {\n    var match = uri.match(svgDataUriPattern);\n    if (match) {\n      var prefix = match[1],\n        svg = match[2];\n      var encodedSvg = encodeURIComponent(svg);\n      return \"\" + prefix + encodedSvg;\n    }\n  }\n  return uri;\n}\nvar Image = React.forwardRef(function (props, ref) {\n  var _ariaLabel = props['aria-label'],\n    accessibilityLabel = props.accessibilityLabel,\n    blurRadius = props.blurRadius,\n    defaultSource = props.defaultSource,\n    draggable = props.draggable,\n    onError = props.onError,\n    onLayout = props.onLayout,\n    onLoad = props.onLoad,\n    onLoadEnd = props.onLoadEnd,\n    onLoadStart = props.onLoadStart,\n    pointerEvents = props.pointerEvents,\n    source = props.source,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var ariaLabel = _ariaLabel || accessibilityLabel;\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.children) {\n      throw new Error('The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.');\n    }\n  }\n  var _React$useState = React.useState(function () {\n      var uri = resolveAssetUri(source);\n      if (uri != null) {\n        var isLoaded = ImageLoader.has(uri);\n        if (isLoaded) {\n          return LOADED;\n        }\n      }\n      return IDLE;\n    }),\n    state = _React$useState[0],\n    updateState = _React$useState[1];\n  var _React$useState2 = React.useState({}),\n    layout = _React$useState2[0],\n    updateLayout = _React$useState2[1];\n  var hasTextAncestor = React.useContext(TextAncestorContext);\n  var hiddenImageRef = React.useRef(null);\n  var filterRef = React.useRef(_filterId++);\n  var requestRef = React.useRef(null);\n  var shouldDisplaySource = state === LOADED || state === LOADING && defaultSource == null;\n  var _extractNonStandardSt = extractNonStandardStyleProps(style, blurRadius, filterRef.current, props.tintColor),\n    _resizeMode = _extractNonStandardSt[0],\n    filter = _extractNonStandardSt[1],\n    _tintColor = _extractNonStandardSt[2];\n  var resizeMode = props.resizeMode || _resizeMode || 'cover';\n  var tintColor = props.tintColor || _tintColor;\n  var selectedSource = shouldDisplaySource ? source : defaultSource;\n  var displayImageUri = resolveAssetUri(selectedSource);\n  var imageSizeStyle = resolveAssetDimensions(selectedSource);\n  var backgroundImage = displayImageUri ? \"url(\\\"\" + displayImageUri + \"\\\")\" : null;\n  var backgroundSize = getBackgroundSize();\n  var hiddenImage = displayImageUri ? createElement('img', {\n    alt: ariaLabel || '',\n    style: styles.accessibilityImage$raw,\n    draggable: draggable || false,\n    ref: hiddenImageRef,\n    src: displayImageUri\n  }) : null;\n  function getBackgroundSize() {\n    if (hiddenImageRef.current != null && (resizeMode === 'center' || resizeMode === 'repeat')) {\n      var _hiddenImageRef$curre = hiddenImageRef.current,\n        naturalHeight = _hiddenImageRef$curre.naturalHeight,\n        naturalWidth = _hiddenImageRef$curre.naturalWidth;\n      var _height3 = layout.height,\n        _width3 = layout.width;\n      if (naturalHeight && naturalWidth && _height3 && _width3) {\n        var scaleFactor = Math.min(1, _width3 / naturalWidth, _height3 / naturalHeight);\n        var x = Math.ceil(scaleFactor * naturalWidth);\n        var y = Math.ceil(scaleFactor * naturalHeight);\n        return x + \"px \" + y + \"px\";\n      }\n    }\n  }\n  function handleLayout(e) {\n    if (resizeMode === 'center' || resizeMode === 'repeat' || onLayout) {\n      var _layout = e.nativeEvent.layout;\n      onLayout && onLayout(e);\n      updateLayout(_layout);\n    }\n  }\n  var uri = resolveAssetUri(source);\n  React.useEffect(function () {\n    abortPendingRequest();\n    if (uri != null) {\n      updateState(LOADING);\n      if (onLoadStart) {\n        onLoadStart();\n      }\n      requestRef.current = ImageLoader.load(uri, function load(e) {\n        updateState(LOADED);\n        if (onLoad) {\n          onLoad(e);\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      }, function error() {\n        updateState(ERRORED);\n        if (onError) {\n          onError({\n            nativeEvent: {\n              error: \"Failed to load resource \" + uri\n            }\n          });\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      });\n    }\n    function abortPendingRequest() {\n      if (requestRef.current != null) {\n        ImageLoader.abort(requestRef.current);\n        requestRef.current = null;\n      }\n    }\n    return abortPendingRequest;\n  }, [uri, requestRef, updateState, onError, onLoad, onLoadEnd, onLoadStart]);\n  return React.createElement(View, _extends({}, rest, {\n    \"aria-label\": ariaLabel,\n    onLayout: handleLayout,\n    pointerEvents: pointerEvents,\n    ref: ref,\n    style: [styles.root, hasTextAncestor && styles.inline, imageSizeStyle, style, styles.undo, {\n      boxShadow: null\n    }]\n  }), React.createElement(View, {\n    style: [styles.image, resizeModeStyles[resizeMode], {\n      backgroundImage: backgroundImage,\n      filter: filter\n    }, backgroundSize != null && {\n      backgroundSize: backgroundSize\n    }],\n    suppressHydrationWarning: true\n  }), hiddenImage, createTintColorSVG(tintColor, filterRef.current));\n});\nImage.displayName = 'Image';\nvar ImageWithStatics = Image;\nImageWithStatics.getSize = function (uri, success, failure) {\n  ImageLoader.getSize(uri, success, failure);\n};\nImageWithStatics.prefetch = function (uri) {\n  return ImageLoader.prefetch(uri);\n};\nImageWithStatics.queryCache = function (uris) {\n  return ImageLoader.queryCache(uris);\n};\nvar styles = StyleSheet.create({\n  root: {\n    flexBasis: 'auto',\n    overflow: 'hidden',\n    zIndex: 0\n  },\n  inline: {\n    display: 'inline-flex'\n  },\n  undo: {\n    blurRadius: null,\n    shadowColor: null,\n    shadowOpacity: null,\n    shadowOffset: null,\n    shadowRadius: null,\n    tintColor: null,\n    overlayColor: null,\n    resizeMode: null\n  },\n  image: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    backgroundColor: 'transparent',\n    backgroundPosition: 'center',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: 'cover',\n    height: '100%',\n    width: '100%',\n    zIndex: -1\n  }),\n  accessibilityImage$raw: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    height: '100%',\n    opacity: 0,\n    width: '100%',\n    zIndex: -1\n  })\n});\nvar resizeModeStyles = StyleSheet.create({\n  center: {\n    backgroundSize: 'auto'\n  },\n  contain: {\n    backgroundSize: 'contain'\n  },\n  cover: {\n    backgroundSize: 'cover'\n  },\n  none: {\n    backgroundPosition: '0',\n    backgroundSize: 'auto'\n  },\n  repeat: {\n    backgroundPosition: '0',\n    backgroundRepeat: 'repeat',\n    backgroundSize: 'auto'\n  },\n  stretch: {\n    backgroundSize: '100% 100%'\n  }\n});\nexport default ImageWithStatics;", "map": {"version": 3, "names": ["_objectSpread", "_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "createElement", "getAssetByID", "createBoxShadowValue", "ImageLoader", "PixelRatio", "StyleSheet", "TextAncestorContext", "View", "warnOnce", "ERRORED", "LOADED", "LOADING", "IDLE", "_filterId", "svgDataUriPattern", "createTintColorSVG", "tintColor", "id", "style", "position", "height", "visibility", "width", "suppressHydrationWarning", "floodColor", "key", "in2", "operator", "extractNonStandardStyleProps", "blurRadius", "filterId", "tintColorProp", "flatStyle", "flatten", "filter", "resizeMode", "shadowOffset", "filters", "_filter", "push", "shadowString", "length", "join", "resolveAssetDimensions", "source", "_getAssetByID", "_height", "_width", "Array", "isArray", "_height2", "_width2", "resolveAssetUri", "uri", "asset", "Error", "scale", "scales", "preferredScale", "get", "reduce", "prev", "curr", "Math", "abs", "scaleSuffix", "httpServerLocation", "name", "type", "match", "prefix", "svg", "encodedSvg", "encodeURIComponent", "Image", "forwardRef", "props", "ref", "_a<PERSON><PERSON><PERSON><PERSON>", "accessibilityLabel", "defaultSource", "draggable", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "pointerEvents", "rest", "aria<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "children", "_React$useState", "useState", "isLoaded", "has", "state", "updateState", "_React$useState2", "layout", "updateLayout", "hasTextAncestor", "useContext", "hiddenImageRef", "useRef", "filterRef", "requestRef", "shouldDisplaySource", "_extractNonStandardSt", "current", "_resizeMode", "_tintColor", "selectedSource", "displayImageUri", "imageSizeStyle", "backgroundImage", "backgroundSize", "getBackgroundSize", "hiddenImage", "alt", "styles", "accessibilityImage$raw", "src", "_hiddenImageRef$curre", "naturalHeight", "naturalWidth", "_height3", "_width3", "scaleFactor", "min", "x", "ceil", "y", "handleLayout", "e", "_layout", "nativeEvent", "useEffect", "abortPendingRequest", "load", "error", "abort", "root", "inline", "undo", "boxShadow", "image", "resizeModeStyles", "displayName", "ImageWithStatics", "getSize", "success", "failure", "prefetch", "queryCache", "uris", "create", "flexBasis", "overflow", "zIndex", "display", "shadowColor", "shadowOpacity", "shadowRadius", "overlayColor", "absoluteFillObject", "backgroundColor", "backgroundPosition", "backgroundRepeat", "opacity", "center", "contain", "cover", "none", "repeat", "stretch"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/Image/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/objectWithoutPropertiesLoose\";\nvar _excluded = [\"aria-label\", \"accessibilityLabel\", \"blurRadius\", \"defaultSource\", \"draggable\", \"onError\", \"onLayout\", \"onLoad\", \"onLoadEnd\", \"onLoadStart\", \"pointerEvents\", \"source\", \"style\"];\nimport * as React from 'react';\nimport createElement from '../createElement';\nimport { getAssetByID } from '../../modules/AssetRegistry';\nimport { createBoxShadowValue } from '../StyleSheet/preprocess';\nimport ImageLoader from '../../modules/ImageLoader';\nimport PixelRatio from '../PixelRatio';\nimport StyleSheet from '../StyleSheet';\nimport TextAncestorContext from '../Text/TextAncestorContext';\nimport View from '../View';\nimport { warnOnce } from '../../modules/warnOnce';\nvar ERRORED = 'ERRORED';\nvar LOADED = 'LOADED';\nvar LOADING = 'LOADING';\nvar IDLE = 'IDLE';\nvar _filterId = 0;\nvar svgDataUriPattern = /^(data:image\\/svg\\+xml;utf8,)(.*)/;\nfunction createTintColorSVG(tintColor, id) {\n  return tintColor && id != null ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      position: 'absolute',\n      height: 0,\n      visibility: 'hidden',\n      width: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"filter\", {\n    id: \"tint-\" + id,\n    suppressHydrationWarning: true\n  }, /*#__PURE__*/React.createElement(\"feFlood\", {\n    floodColor: \"\" + tintColor,\n    key: tintColor\n  }), /*#__PURE__*/React.createElement(\"feComposite\", {\n    in2: \"SourceAlpha\",\n    operator: \"in\"\n  })))) : null;\n}\nfunction extractNonStandardStyleProps(style, blurRadius, filterId, tintColorProp) {\n  var flatStyle = StyleSheet.flatten(style);\n  var filter = flatStyle.filter,\n    resizeMode = flatStyle.resizeMode,\n    shadowOffset = flatStyle.shadowOffset,\n    tintColor = flatStyle.tintColor;\n  if (flatStyle.resizeMode) {\n    warnOnce('Image.style.resizeMode', 'Image: style.resizeMode is deprecated. Please use props.resizeMode.');\n  }\n  if (flatStyle.tintColor) {\n    warnOnce('Image.style.tintColor', 'Image: style.tintColor is deprecated. Please use props.tintColor.');\n  }\n\n  // Add CSS filters\n  // React Native exposes these features as props and proprietary styles\n  var filters = [];\n  var _filter = null;\n  if (filter) {\n    filters.push(filter);\n  }\n  if (blurRadius) {\n    filters.push(\"blur(\" + blurRadius + \"px)\");\n  }\n  if (shadowOffset) {\n    var shadowString = createBoxShadowValue(flatStyle);\n    if (shadowString) {\n      filters.push(\"drop-shadow(\" + shadowString + \")\");\n    }\n  }\n  if ((tintColorProp || tintColor) && filterId != null) {\n    filters.push(\"url(#tint-\" + filterId + \")\");\n  }\n  if (filters.length > 0) {\n    _filter = filters.join(' ');\n  }\n  return [resizeMode, _filter, tintColor];\n}\nfunction resolveAssetDimensions(source) {\n  if (typeof source === 'number') {\n    var _getAssetByID = getAssetByID(source),\n      _height = _getAssetByID.height,\n      _width = _getAssetByID.width;\n    return {\n      height: _height,\n      width: _width\n    };\n  } else if (source != null && !Array.isArray(source) && typeof source === 'object') {\n    var _height2 = source.height,\n      _width2 = source.width;\n    return {\n      height: _height2,\n      width: _width2\n    };\n  }\n}\nfunction resolveAssetUri(source) {\n  var uri = null;\n  if (typeof source === 'number') {\n    // get the URI from the packager\n    var asset = getAssetByID(source);\n    if (asset == null) {\n      throw new Error(\"Image: asset with ID \\\"\" + source + \"\\\" could not be found. Please check the image source or packager.\");\n    }\n    var scale = asset.scales[0];\n    if (asset.scales.length > 1) {\n      var preferredScale = PixelRatio.get();\n      // Get the scale which is closest to the preferred scale\n      scale = asset.scales.reduce((prev, curr) => Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev);\n    }\n    var scaleSuffix = scale !== 1 ? \"@\" + scale + \"x\" : '';\n    uri = asset ? asset.httpServerLocation + \"/\" + asset.name + scaleSuffix + \".\" + asset.type : '';\n  } else if (typeof source === 'string') {\n    uri = source;\n  } else if (source && typeof source.uri === 'string') {\n    uri = source.uri;\n  }\n  if (uri) {\n    var match = uri.match(svgDataUriPattern);\n    // inline SVG markup may contain characters (e.g., #, \") that need to be escaped\n    if (match) {\n      var prefix = match[1],\n        svg = match[2];\n      var encodedSvg = encodeURIComponent(svg);\n      return \"\" + prefix + encodedSvg;\n    }\n  }\n  return uri;\n}\nvar Image = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ariaLabel = props['aria-label'],\n    accessibilityLabel = props.accessibilityLabel,\n    blurRadius = props.blurRadius,\n    defaultSource = props.defaultSource,\n    draggable = props.draggable,\n    onError = props.onError,\n    onLayout = props.onLayout,\n    onLoad = props.onLoad,\n    onLoadEnd = props.onLoadEnd,\n    onLoadStart = props.onLoadStart,\n    pointerEvents = props.pointerEvents,\n    source = props.source,\n    style = props.style,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  var ariaLabel = _ariaLabel || accessibilityLabel;\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.children) {\n      throw new Error('The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.');\n    }\n  }\n  var _React$useState = React.useState(() => {\n      var uri = resolveAssetUri(source);\n      if (uri != null) {\n        var isLoaded = ImageLoader.has(uri);\n        if (isLoaded) {\n          return LOADED;\n        }\n      }\n      return IDLE;\n    }),\n    state = _React$useState[0],\n    updateState = _React$useState[1];\n  var _React$useState2 = React.useState({}),\n    layout = _React$useState2[0],\n    updateLayout = _React$useState2[1];\n  var hasTextAncestor = React.useContext(TextAncestorContext);\n  var hiddenImageRef = React.useRef(null);\n  var filterRef = React.useRef(_filterId++);\n  var requestRef = React.useRef(null);\n  var shouldDisplaySource = state === LOADED || state === LOADING && defaultSource == null;\n  var _extractNonStandardSt = extractNonStandardStyleProps(style, blurRadius, filterRef.current, props.tintColor),\n    _resizeMode = _extractNonStandardSt[0],\n    filter = _extractNonStandardSt[1],\n    _tintColor = _extractNonStandardSt[2];\n  var resizeMode = props.resizeMode || _resizeMode || 'cover';\n  var tintColor = props.tintColor || _tintColor;\n  var selectedSource = shouldDisplaySource ? source : defaultSource;\n  var displayImageUri = resolveAssetUri(selectedSource);\n  var imageSizeStyle = resolveAssetDimensions(selectedSource);\n  var backgroundImage = displayImageUri ? \"url(\\\"\" + displayImageUri + \"\\\")\" : null;\n  var backgroundSize = getBackgroundSize();\n\n  // Accessibility image allows users to trigger the browser's image context menu\n  var hiddenImage = displayImageUri ? createElement('img', {\n    alt: ariaLabel || '',\n    style: styles.accessibilityImage$raw,\n    draggable: draggable || false,\n    ref: hiddenImageRef,\n    src: displayImageUri\n  }) : null;\n  function getBackgroundSize() {\n    if (hiddenImageRef.current != null && (resizeMode === 'center' || resizeMode === 'repeat')) {\n      var _hiddenImageRef$curre = hiddenImageRef.current,\n        naturalHeight = _hiddenImageRef$curre.naturalHeight,\n        naturalWidth = _hiddenImageRef$curre.naturalWidth;\n      var _height3 = layout.height,\n        _width3 = layout.width;\n      if (naturalHeight && naturalWidth && _height3 && _width3) {\n        var scaleFactor = Math.min(1, _width3 / naturalWidth, _height3 / naturalHeight);\n        var x = Math.ceil(scaleFactor * naturalWidth);\n        var y = Math.ceil(scaleFactor * naturalHeight);\n        return x + \"px \" + y + \"px\";\n      }\n    }\n  }\n  function handleLayout(e) {\n    if (resizeMode === 'center' || resizeMode === 'repeat' || onLayout) {\n      var _layout = e.nativeEvent.layout;\n      onLayout && onLayout(e);\n      updateLayout(_layout);\n    }\n  }\n\n  // Image loading\n  var uri = resolveAssetUri(source);\n  React.useEffect(() => {\n    abortPendingRequest();\n    if (uri != null) {\n      updateState(LOADING);\n      if (onLoadStart) {\n        onLoadStart();\n      }\n      requestRef.current = ImageLoader.load(uri, function load(e) {\n        updateState(LOADED);\n        if (onLoad) {\n          onLoad(e);\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      }, function error() {\n        updateState(ERRORED);\n        if (onError) {\n          onError({\n            nativeEvent: {\n              error: \"Failed to load resource \" + uri\n            }\n          });\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      });\n    }\n    function abortPendingRequest() {\n      if (requestRef.current != null) {\n        ImageLoader.abort(requestRef.current);\n        requestRef.current = null;\n      }\n    }\n    return abortPendingRequest;\n  }, [uri, requestRef, updateState, onError, onLoad, onLoadEnd, onLoadStart]);\n  return /*#__PURE__*/React.createElement(View, _extends({}, rest, {\n    \"aria-label\": ariaLabel,\n    onLayout: handleLayout,\n    pointerEvents: pointerEvents,\n    ref: ref,\n    style: [styles.root, hasTextAncestor && styles.inline, imageSizeStyle, style, styles.undo,\n    // TEMP: avoid deprecated shadow props regression\n    // until Image refactored to use createElement.\n    {\n      boxShadow: null\n    }]\n  }), /*#__PURE__*/React.createElement(View, {\n    style: [styles.image, resizeModeStyles[resizeMode], {\n      backgroundImage,\n      filter\n    }, backgroundSize != null && {\n      backgroundSize\n    }],\n    suppressHydrationWarning: true\n  }), hiddenImage, createTintColorSVG(tintColor, filterRef.current));\n});\nImage.displayName = 'Image';\n\n// $FlowIgnore: This is the correct type, but casting makes it unhappy since the variables aren't defined yet\nvar ImageWithStatics = Image;\nImageWithStatics.getSize = function (uri, success, failure) {\n  ImageLoader.getSize(uri, success, failure);\n};\nImageWithStatics.prefetch = function (uri) {\n  return ImageLoader.prefetch(uri);\n};\nImageWithStatics.queryCache = function (uris) {\n  return ImageLoader.queryCache(uris);\n};\nvar styles = StyleSheet.create({\n  root: {\n    flexBasis: 'auto',\n    overflow: 'hidden',\n    zIndex: 0\n  },\n  inline: {\n    display: 'inline-flex'\n  },\n  undo: {\n    // These styles are converted to CSS filters applied to the\n    // element displaying the background image.\n    blurRadius: null,\n    shadowColor: null,\n    shadowOpacity: null,\n    shadowOffset: null,\n    shadowRadius: null,\n    tintColor: null,\n    // These styles are not supported\n    overlayColor: null,\n    resizeMode: null\n  },\n  image: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    backgroundColor: 'transparent',\n    backgroundPosition: 'center',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: 'cover',\n    height: '100%',\n    width: '100%',\n    zIndex: -1\n  }),\n  accessibilityImage$raw: _objectSpread(_objectSpread({}, StyleSheet.absoluteFillObject), {}, {\n    height: '100%',\n    opacity: 0,\n    width: '100%',\n    zIndex: -1\n  })\n});\nvar resizeModeStyles = StyleSheet.create({\n  center: {\n    backgroundSize: 'auto'\n  },\n  contain: {\n    backgroundSize: 'contain'\n  },\n  cover: {\n    backgroundSize: 'cover'\n  },\n  none: {\n    backgroundPosition: '0',\n    backgroundSize: 'auto'\n  },\n  repeat: {\n    backgroundPosition: '0',\n    backgroundRepeat: 'repeat',\n    backgroundSize: 'auto'\n  },\n  stretch: {\n    backgroundSize: '100% 100%'\n  }\n});\nexport default ImageWithStatics;"], "mappings": "AAUA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,sCAAsC;AAChE,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,6BAA6B,MAAM,qDAAqD;AAC/F,IAAIC,SAAS,GAAG,CAAC,YAAY,EAAE,oBAAoB,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,CAAC;AACjM,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa;AACpB,SAASC,YAAY;AACrB,SAASC,oBAAoB;AAC7B,OAAOC,WAAW;AAClB,OAAOC,UAAU;AACjB,OAAOC,UAAU;AACjB,OAAOC,mBAAmB;AAC1B,OAAOC,IAAI;AACX,SAASC,QAAQ;AACjB,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,iBAAiB,GAAG,mCAAmC;AAC3D,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,EAAE,EAAE;EACzC,OAAOD,SAAS,IAAIC,EAAE,IAAI,IAAI,GAAgBlB,KAAK,CAACC,aAAa,CAAC,KAAK,EAAE;IACvEkB,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,QAAQ;MACpBC,KAAK,EAAE;IACT;EACF,CAAC,EAAevB,KAAK,CAACC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAeD,KAAK,CAACC,aAAa,CAAC,QAAQ,EAAE;IAC3FiB,EAAE,EAAE,OAAO,GAAGA,EAAE;IAChBM,wBAAwB,EAAE;EAC5B,CAAC,EAAexB,KAAK,CAACC,aAAa,CAAC,SAAS,EAAE;IAC7CwB,UAAU,EAAE,EAAE,GAAGR,SAAS;IAC1BS,GAAG,EAAET;EACP,CAAC,CAAC,EAAejB,KAAK,CAACC,aAAa,CAAC,aAAa,EAAE;IAClD0B,GAAG,EAAE,aAAa;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACd;AACA,SAASC,4BAA4BA,CAACV,KAAK,EAAEW,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EAChF,IAAIC,SAAS,GAAG3B,UAAU,CAAC4B,OAAO,CAACf,KAAK,CAAC;EACzC,IAAIgB,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3BC,UAAU,GAAGH,SAAS,CAACG,UAAU;IACjCC,YAAY,GAAGJ,SAAS,CAACI,YAAY;IACrCpB,SAAS,GAAGgB,SAAS,CAAChB,SAAS;EACjC,IAAIgB,SAAS,CAACG,UAAU,EAAE;IACxB3B,QAAQ,CAAC,wBAAwB,EAAE,qEAAqE,CAAC;EAC3G;EACA,IAAIwB,SAAS,CAAChB,SAAS,EAAE;IACvBR,QAAQ,CAAC,uBAAuB,EAAE,mEAAmE,CAAC;EACxG;EAIA,IAAI6B,OAAO,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIJ,MAAM,EAAE;IACVG,OAAO,CAACE,IAAI,CAACL,MAAM,CAAC;EACtB;EACA,IAAIL,UAAU,EAAE;IACdQ,OAAO,CAACE,IAAI,CAAC,OAAO,GAAGV,UAAU,GAAG,KAAK,CAAC;EAC5C;EACA,IAAIO,YAAY,EAAE;IAChB,IAAII,YAAY,GAAGtC,oBAAoB,CAAC8B,SAAS,CAAC;IAClD,IAAIQ,YAAY,EAAE;MAChBH,OAAO,CAACE,IAAI,CAAC,cAAc,GAAGC,YAAY,GAAG,GAAG,CAAC;IACnD;EACF;EACA,IAAI,CAACT,aAAa,IAAIf,SAAS,KAAKc,QAAQ,IAAI,IAAI,EAAE;IACpDO,OAAO,CAACE,IAAI,CAAC,YAAY,GAAGT,QAAQ,GAAG,GAAG,CAAC;EAC7C;EACA,IAAIO,OAAO,CAACI,MAAM,GAAG,CAAC,EAAE;IACtBH,OAAO,GAAGD,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC;EAC7B;EACA,OAAO,CAACP,UAAU,EAAEG,OAAO,EAAEtB,SAAS,CAAC;AACzC;AACA,SAAS2B,sBAAsBA,CAACC,MAAM,EAAE;EACtC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIC,aAAa,GAAG5C,YAAY,CAAC2C,MAAM,CAAC;MACtCE,OAAO,GAAGD,aAAa,CAACzB,MAAM;MAC9B2B,MAAM,GAAGF,aAAa,CAACvB,KAAK;IAC9B,OAAO;MACLF,MAAM,EAAE0B,OAAO;MACfxB,KAAK,EAAEyB;IACT,CAAC;EACH,CAAC,MAAM,IAAIH,MAAM,IAAI,IAAI,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACjF,IAAIM,QAAQ,GAAGN,MAAM,CAACxB,MAAM;MAC1B+B,OAAO,GAAGP,MAAM,CAACtB,KAAK;IACxB,OAAO;MACLF,MAAM,EAAE8B,QAAQ;MAChB5B,KAAK,EAAE6B;IACT,CAAC;EACH;AACF;AACA,SAASC,eAAeA,CAACR,MAAM,EAAE;EAC/B,IAAIS,GAAG,GAAG,IAAI;EACd,IAAI,OAAOT,MAAM,KAAK,QAAQ,EAAE;IAE9B,IAAIU,KAAK,GAAGrD,YAAY,CAAC2C,MAAM,CAAC;IAChC,IAAIU,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAGX,MAAM,GAAG,mEAAmE,CAAC;IAC3H;IACA,IAAIY,KAAK,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIH,KAAK,CAACG,MAAM,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAIiB,cAAc,GAAGtD,UAAU,CAACuD,GAAG,CAAC,CAAC;MAErCH,KAAK,GAAGF,KAAK,CAACG,MAAM,CAACG,MAAM,CAAC,UAACC,IAAI,EAAEC,IAAI;QAAA,OAAKC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAGJ,cAAc,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACH,IAAI,GAAGH,cAAc,CAAC,GAAGI,IAAI,GAAGD,IAAI;MAAA,EAAC;IAC9H;IACA,IAAII,WAAW,GAAGT,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG,GAAG,EAAE;IACtDH,GAAG,GAAGC,KAAK,GAAGA,KAAK,CAACY,kBAAkB,GAAG,GAAG,GAAGZ,KAAK,CAACa,IAAI,GAAGF,WAAW,GAAG,GAAG,GAAGX,KAAK,CAACc,IAAI,GAAG,EAAE;EACjG,CAAC,MAAM,IAAI,OAAOxB,MAAM,KAAK,QAAQ,EAAE;IACrCS,GAAG,GAAGT,MAAM;EACd,CAAC,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACS,GAAG,KAAK,QAAQ,EAAE;IACnDA,GAAG,GAAGT,MAAM,CAACS,GAAG;EAClB;EACA,IAAIA,GAAG,EAAE;IACP,IAAIgB,KAAK,GAAGhB,GAAG,CAACgB,KAAK,CAACvD,iBAAiB,CAAC;IAExC,IAAIuD,KAAK,EAAE;MACT,IAAIC,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;QACnBE,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC;MAChB,IAAIG,UAAU,GAAGC,kBAAkB,CAACF,GAAG,CAAC;MACxC,OAAO,EAAE,GAAGD,MAAM,GAAGE,UAAU;IACjC;EACF;EACA,OAAOnB,GAAG;AACZ;AACA,IAAIqB,KAAK,GAAgB3E,KAAK,CAAC4E,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EACxD,IAAIC,UAAU,GAAGF,KAAK,CAAC,YAAY,CAAC;IAClCG,kBAAkB,GAAGH,KAAK,CAACG,kBAAkB;IAC7ClD,UAAU,GAAG+C,KAAK,CAAC/C,UAAU;IAC7BmD,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,aAAa,GAAGX,KAAK,CAACW,aAAa;IACnC3C,MAAM,GAAGgC,KAAK,CAAChC,MAAM;IACrB1B,KAAK,GAAG0D,KAAK,CAAC1D,KAAK;IACnBsE,IAAI,GAAG3F,6BAA6B,CAAC+E,KAAK,EAAE9E,SAAS,CAAC;EACxD,IAAI2F,SAAS,GAAGX,UAAU,IAAIC,kBAAkB;EAChD,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhB,KAAK,CAACiB,QAAQ,EAAE;MAClB,MAAM,IAAItC,KAAK,CAAC,2KAA2K,CAAC;IAC9L;EACF;EACA,IAAIuC,eAAe,GAAG/F,KAAK,CAACgG,QAAQ,CAAC,YAAM;MACvC,IAAI1C,GAAG,GAAGD,eAAe,CAACR,MAAM,CAAC;MACjC,IAAIS,GAAG,IAAI,IAAI,EAAE;QACf,IAAI2C,QAAQ,GAAG7F,WAAW,CAAC8F,GAAG,CAAC5C,GAAG,CAAC;QACnC,IAAI2C,QAAQ,EAAE;UACZ,OAAOtF,MAAM;QACf;MACF;MACA,OAAOE,IAAI;IACb,CAAC,CAAC;IACFsF,KAAK,GAAGJ,eAAe,CAAC,CAAC,CAAC;IAC1BK,WAAW,GAAGL,eAAe,CAAC,CAAC,CAAC;EAClC,IAAIM,gBAAgB,GAAGrG,KAAK,CAACgG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCM,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,eAAe,GAAGxG,KAAK,CAACyG,UAAU,CAAClG,mBAAmB,CAAC;EAC3D,IAAImG,cAAc,GAAG1G,KAAK,CAAC2G,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIC,SAAS,GAAG5G,KAAK,CAAC2G,MAAM,CAAC7F,SAAS,EAAE,CAAC;EACzC,IAAI+F,UAAU,GAAG7G,KAAK,CAAC2G,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIG,mBAAmB,GAAGX,KAAK,KAAKxF,MAAM,IAAIwF,KAAK,KAAKvF,OAAO,IAAIqE,aAAa,IAAI,IAAI;EACxF,IAAI8B,qBAAqB,GAAGlF,4BAA4B,CAACV,KAAK,EAAEW,UAAU,EAAE8E,SAAS,CAACI,OAAO,EAAEnC,KAAK,CAAC5D,SAAS,CAAC;IAC7GgG,WAAW,GAAGF,qBAAqB,CAAC,CAAC,CAAC;IACtC5E,MAAM,GAAG4E,qBAAqB,CAAC,CAAC,CAAC;IACjCG,UAAU,GAAGH,qBAAqB,CAAC,CAAC,CAAC;EACvC,IAAI3E,UAAU,GAAGyC,KAAK,CAACzC,UAAU,IAAI6E,WAAW,IAAI,OAAO;EAC3D,IAAIhG,SAAS,GAAG4D,KAAK,CAAC5D,SAAS,IAAIiG,UAAU;EAC7C,IAAIC,cAAc,GAAGL,mBAAmB,GAAGjE,MAAM,GAAGoC,aAAa;EACjE,IAAImC,eAAe,GAAG/D,eAAe,CAAC8D,cAAc,CAAC;EACrD,IAAIE,cAAc,GAAGzE,sBAAsB,CAACuE,cAAc,CAAC;EAC3D,IAAIG,eAAe,GAAGF,eAAe,GAAG,QAAQ,GAAGA,eAAe,GAAG,KAAK,GAAG,IAAI;EACjF,IAAIG,cAAc,GAAGC,iBAAiB,CAAC,CAAC;EAGxC,IAAIC,WAAW,GAAGL,eAAe,GAAGnH,aAAa,CAAC,KAAK,EAAE;IACvDyH,GAAG,EAAEhC,SAAS,IAAI,EAAE;IACpBvE,KAAK,EAAEwG,MAAM,CAACC,sBAAsB;IACpC1C,SAAS,EAAEA,SAAS,IAAI,KAAK;IAC7BJ,GAAG,EAAE4B,cAAc;IACnBmB,GAAG,EAAET;EACP,CAAC,CAAC,GAAG,IAAI;EACT,SAASI,iBAAiBA,CAAA,EAAG;IAC3B,IAAId,cAAc,CAACM,OAAO,IAAI,IAAI,KAAK5E,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,QAAQ,CAAC,EAAE;MAC1F,IAAI0F,qBAAqB,GAAGpB,cAAc,CAACM,OAAO;QAChDe,aAAa,GAAGD,qBAAqB,CAACC,aAAa;QACnDC,YAAY,GAAGF,qBAAqB,CAACE,YAAY;MACnD,IAAIC,QAAQ,GAAG3B,MAAM,CAACjF,MAAM;QAC1B6G,OAAO,GAAG5B,MAAM,CAAC/E,KAAK;MACxB,IAAIwG,aAAa,IAAIC,YAAY,IAAIC,QAAQ,IAAIC,OAAO,EAAE;QACxD,IAAIC,WAAW,GAAGnE,IAAI,CAACoE,GAAG,CAAC,CAAC,EAAEF,OAAO,GAAGF,YAAY,EAAEC,QAAQ,GAAGF,aAAa,CAAC;QAC/E,IAAIM,CAAC,GAAGrE,IAAI,CAACsE,IAAI,CAACH,WAAW,GAAGH,YAAY,CAAC;QAC7C,IAAIO,CAAC,GAAGvE,IAAI,CAACsE,IAAI,CAACH,WAAW,GAAGJ,aAAa,CAAC;QAC9C,OAAOM,CAAC,GAAG,KAAK,GAAGE,CAAC,GAAG,IAAI;MAC7B;IACF;EACF;EACA,SAASC,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIrG,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,QAAQ,IAAIgD,QAAQ,EAAE;MAClE,IAAIsD,OAAO,GAAGD,CAAC,CAACE,WAAW,CAACrC,MAAM;MAClClB,QAAQ,IAAIA,QAAQ,CAACqD,CAAC,CAAC;MACvBlC,YAAY,CAACmC,OAAO,CAAC;IACvB;EACF;EAGA,IAAIpF,GAAG,GAAGD,eAAe,CAACR,MAAM,CAAC;EACjC7C,KAAK,CAAC4I,SAAS,CAAC,YAAM;IACpBC,mBAAmB,CAAC,CAAC;IACrB,IAAIvF,GAAG,IAAI,IAAI,EAAE;MACf8C,WAAW,CAACxF,OAAO,CAAC;MACpB,IAAI2E,WAAW,EAAE;QACfA,WAAW,CAAC,CAAC;MACf;MACAsB,UAAU,CAACG,OAAO,GAAG5G,WAAW,CAAC0I,IAAI,CAACxF,GAAG,EAAE,SAASwF,IAAIA,CAACL,CAAC,EAAE;QAC1DrC,WAAW,CAACzF,MAAM,CAAC;QACnB,IAAI0E,MAAM,EAAE;UACVA,MAAM,CAACoD,CAAC,CAAC;QACX;QACA,IAAInD,SAAS,EAAE;UACbA,SAAS,CAAC,CAAC;QACb;MACF,CAAC,EAAE,SAASyD,KAAKA,CAAA,EAAG;QAClB3C,WAAW,CAAC1F,OAAO,CAAC;QACpB,IAAIyE,OAAO,EAAE;UACXA,OAAO,CAAC;YACNwD,WAAW,EAAE;cACXI,KAAK,EAAE,0BAA0B,GAAGzF;YACtC;UACF,CAAC,CAAC;QACJ;QACA,IAAIgC,SAAS,EAAE;UACbA,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ;IACA,SAASuD,mBAAmBA,CAAA,EAAG;MAC7B,IAAIhC,UAAU,CAACG,OAAO,IAAI,IAAI,EAAE;QAC9B5G,WAAW,CAAC4I,KAAK,CAACnC,UAAU,CAACG,OAAO,CAAC;QACrCH,UAAU,CAACG,OAAO,GAAG,IAAI;MAC3B;IACF;IACA,OAAO6B,mBAAmB;EAC5B,CAAC,EAAE,CAACvF,GAAG,EAAEuD,UAAU,EAAET,WAAW,EAAEjB,OAAO,EAAEE,MAAM,EAAEC,SAAS,EAAEC,WAAW,CAAC,CAAC;EAC3E,OAAoBvF,KAAK,CAACC,aAAa,CAACO,IAAI,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAE4F,IAAI,EAAE;IAC/D,YAAY,EAAEC,SAAS;IACvBN,QAAQ,EAAEoD,YAAY;IACtBhD,aAAa,EAAEA,aAAa;IAC5BV,GAAG,EAAEA,GAAG;IACR3D,KAAK,EAAE,CAACwG,MAAM,CAACsB,IAAI,EAAEzC,eAAe,IAAImB,MAAM,CAACuB,MAAM,EAAE7B,cAAc,EAAElG,KAAK,EAAEwG,MAAM,CAACwB,IAAI,EAGzF;MACEC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC,EAAepJ,KAAK,CAACC,aAAa,CAACO,IAAI,EAAE;IACzCW,KAAK,EAAE,CAACwG,MAAM,CAAC0B,KAAK,EAAEC,gBAAgB,CAAClH,UAAU,CAAC,EAAE;MAClDkF,eAAe,EAAfA,eAAe;MACfnF,MAAM,EAANA;IACF,CAAC,EAAEoF,cAAc,IAAI,IAAI,IAAI;MAC3BA,cAAc,EAAdA;IACF,CAAC,CAAC;IACF/F,wBAAwB,EAAE;EAC5B,CAAC,CAAC,EAAEiG,WAAW,EAAEzG,kBAAkB,CAACC,SAAS,EAAE2F,SAAS,CAACI,OAAO,CAAC,CAAC;AACpE,CAAC,CAAC;AACFrC,KAAK,CAAC4E,WAAW,GAAG,OAAO;AAG3B,IAAIC,gBAAgB,GAAG7E,KAAK;AAC5B6E,gBAAgB,CAACC,OAAO,GAAG,UAAUnG,GAAG,EAAEoG,OAAO,EAAEC,OAAO,EAAE;EAC1DvJ,WAAW,CAACqJ,OAAO,CAACnG,GAAG,EAAEoG,OAAO,EAAEC,OAAO,CAAC;AAC5C,CAAC;AACDH,gBAAgB,CAACI,QAAQ,GAAG,UAAUtG,GAAG,EAAE;EACzC,OAAOlD,WAAW,CAACwJ,QAAQ,CAACtG,GAAG,CAAC;AAClC,CAAC;AACDkG,gBAAgB,CAACK,UAAU,GAAG,UAAUC,IAAI,EAAE;EAC5C,OAAO1J,WAAW,CAACyJ,UAAU,CAACC,IAAI,CAAC;AACrC,CAAC;AACD,IAAInC,MAAM,GAAGrH,UAAU,CAACyJ,MAAM,CAAC;EAC7Bd,IAAI,EAAE;IACJe,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE;EACV,CAAC;EACDhB,MAAM,EAAE;IACNiB,OAAO,EAAE;EACX,CAAC;EACDhB,IAAI,EAAE;IAGJrH,UAAU,EAAE,IAAI;IAChBsI,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBhI,YAAY,EAAE,IAAI;IAClBiI,YAAY,EAAE,IAAI;IAClBrJ,SAAS,EAAE,IAAI;IAEfsJ,YAAY,EAAE,IAAI;IAClBnI,UAAU,EAAE;EACd,CAAC;EACDiH,KAAK,EAAEzJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEU,UAAU,CAACkK,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IACzEC,eAAe,EAAE,aAAa;IAC9BC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,WAAW;IAC7BpD,cAAc,EAAE,OAAO;IACvBlG,MAAM,EAAE,MAAM;IACdE,KAAK,EAAE,MAAM;IACb2I,MAAM,EAAE,CAAC;EACX,CAAC,CAAC;EACFtC,sBAAsB,EAAEhI,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEU,UAAU,CAACkK,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1FnJ,MAAM,EAAE,MAAM;IACduJ,OAAO,EAAE,CAAC;IACVrJ,KAAK,EAAE,MAAM;IACb2I,MAAM,EAAE,CAAC;EACX,CAAC;AACH,CAAC,CAAC;AACF,IAAIZ,gBAAgB,GAAGhJ,UAAU,CAACyJ,MAAM,CAAC;EACvCc,MAAM,EAAE;IACNtD,cAAc,EAAE;EAClB,CAAC;EACDuD,OAAO,EAAE;IACPvD,cAAc,EAAE;EAClB,CAAC;EACDwD,KAAK,EAAE;IACLxD,cAAc,EAAE;EAClB,CAAC;EACDyD,IAAI,EAAE;IACJN,kBAAkB,EAAE,GAAG;IACvBnD,cAAc,EAAE;EAClB,CAAC;EACD0D,MAAM,EAAE;IACNP,kBAAkB,EAAE,GAAG;IACvBC,gBAAgB,EAAE,QAAQ;IAC1BpD,cAAc,EAAE;EAClB,CAAC;EACD2D,OAAO,EAAE;IACP3D,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AACF,eAAeiC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}