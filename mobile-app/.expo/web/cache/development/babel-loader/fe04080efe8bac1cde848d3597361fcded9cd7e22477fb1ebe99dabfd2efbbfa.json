{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nvar _Recording;\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { PermissionStatus, createPermissionHook, EventEmitter, Platform } from 'expo-modules-core';\nimport { _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS } from \"../AV\";\nimport ExponentAV from \"../ExponentAV\";\nimport { isAudioEnabled, throwIfAudioIsDisabled } from \"./AudioAvailability\";\nimport { RecordingOptionsPresets } from \"./RecordingConstants\";\nimport { Sound } from \"./Sound\";\nvar _recorderExists = false;\nvar eventEmitter = Platform.OS === 'android' ? new EventEmitter(ExponentAV) : null;\nexport function getPermissionsAsync() {\n  return _getPermissionsAsync.apply(this, arguments);\n}\nfunction _getPermissionsAsync() {\n  _getPermissionsAsync = _asyncToGenerator(function* () {\n    return ExponentAV.getPermissionsAsync();\n  });\n  return _getPermissionsAsync.apply(this, arguments);\n}\nexport function requestPermissionsAsync() {\n  return _requestPermissionsAsync.apply(this, arguments);\n}\nfunction _requestPermissionsAsync() {\n  _requestPermissionsAsync = _asyncToGenerator(function* () {\n    return ExponentAV.requestPermissionsAsync();\n  });\n  return _requestPermissionsAsync.apply(this, arguments);\n}\nexport var usePermissions = createPermissionHook({\n  getMethod: getPermissionsAsync,\n  requestMethod: requestPermissionsAsync\n});\nexport var Recording = function () {\n  function Recording() {\n    var _this = this;\n    _classCallCheck(this, Recording);\n    this._subscription = null;\n    this._canRecord = false;\n    this._isDoneRecording = false;\n    this._finalDurationMillis = 0;\n    this._uri = null;\n    this._onRecordingStatusUpdate = null;\n    this._progressUpdateTimeoutVariable = null;\n    this._progressUpdateIntervalMillis = _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS;\n    this._options = null;\n    this._cleanupForUnloadedRecorder = function () {\n      var _ref = _asyncToGenerator(function* (finalStatus) {\n        var _finalStatus$duration;\n        _this._canRecord = false;\n        _this._isDoneRecording = true;\n        _this._finalDurationMillis = (_finalStatus$duration = finalStatus == null ? void 0 : finalStatus.durationMillis) != null ? _finalStatus$duration : 0;\n        _recorderExists = false;\n        if (_this._subscription) {\n          _this._subscription.remove();\n          _this._subscription = null;\n        }\n        _this._disablePolling();\n        return yield _this.getStatusAsync();\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    this._pollingLoop = _asyncToGenerator(function* () {\n      if (isAudioEnabled() && _this._canRecord && _this._onRecordingStatusUpdate != null) {\n        _this._progressUpdateTimeoutVariable = setTimeout(_this._pollingLoop, _this._progressUpdateIntervalMillis);\n        try {\n          yield _this.getStatusAsync();\n        } catch (_unused) {\n          _this._disablePolling();\n        }\n      }\n    });\n    this.getStatusAsync = _asyncToGenerator(function* () {\n      if (_this._canRecord) {\n        return _this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.getAudioRecordingStatus();\n        });\n      }\n      var status = {\n        canRecord: false,\n        isRecording: false,\n        isDoneRecording: _this._isDoneRecording,\n        durationMillis: _this._finalDurationMillis\n      };\n      _this._callOnRecordingStatusUpdateForNewStatus(status);\n      return status;\n    });\n  }\n  return _createClass(Recording, [{\n    key: \"_disablePolling\",\n    value: function _disablePolling() {\n      if (this._progressUpdateTimeoutVariable != null) {\n        clearTimeout(this._progressUpdateTimeoutVariable);\n        this._progressUpdateTimeoutVariable = null;\n      }\n    }\n  }, {\n    key: \"_enablePollingIfNecessaryAndPossible\",\n    value: function _enablePollingIfNecessaryAndPossible() {\n      if (isAudioEnabled() && this._canRecord && this._onRecordingStatusUpdate != null) {\n        this._disablePolling();\n        this._pollingLoop();\n      }\n    }\n  }, {\n    key: \"_callOnRecordingStatusUpdateForNewStatus\",\n    value: function _callOnRecordingStatusUpdateForNewStatus(status) {\n      if (this._onRecordingStatusUpdate != null) {\n        this._onRecordingStatusUpdate(status);\n      }\n    }\n  }, {\n    key: \"_performOperationAndHandleStatusAsync\",\n    value: function () {\n      var _performOperationAndHandleStatusAsync2 = _asyncToGenerator(function* (operation) {\n        throwIfAudioIsDisabled();\n        if (this._canRecord) {\n          var status = yield operation();\n          this._callOnRecordingStatusUpdateForNewStatus(status);\n          return status;\n        } else {\n          throw new Error('Cannot complete operation because this recorder is not ready to record.');\n        }\n      });\n      function _performOperationAndHandleStatusAsync(_x2) {\n        return _performOperationAndHandleStatusAsync2.apply(this, arguments);\n      }\n      return _performOperationAndHandleStatusAsync;\n    }()\n  }, {\n    key: \"setOnRecordingStatusUpdate\",\n    value: function setOnRecordingStatusUpdate(onRecordingStatusUpdate) {\n      this._onRecordingStatusUpdate = onRecordingStatusUpdate;\n      if (onRecordingStatusUpdate == null) {\n        this._disablePolling();\n      } else {\n        this._enablePollingIfNecessaryAndPossible();\n      }\n      this.getStatusAsync();\n    }\n  }, {\n    key: \"setProgressUpdateInterval\",\n    value: function setProgressUpdateInterval(progressUpdateIntervalMillis) {\n      this._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n      this.getStatusAsync();\n    }\n  }, {\n    key: \"prepareToRecordAsync\",\n    value: function () {\n      var _prepareToRecordAsync = _asyncToGenerator(function* () {\n        var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : RecordingOptionsPresets.LOW_QUALITY;\n        throwIfAudioIsDisabled();\n        if (_recorderExists) {\n          throw new Error('Only one Recording object can be prepared at a given time.');\n        }\n        if (this._isDoneRecording) {\n          throw new Error('This Recording object is done recording; you must make a new one.');\n        }\n        if (!options || !options.android || !options.ios) {\n          throw new Error('You must provide recording options for android and ios in order to prepare to record.');\n        }\n        var extensionRegex = /^\\.\\w+$/;\n        if (!options.android.extension || !options.ios.extension || !extensionRegex.test(options.android.extension) || !extensionRegex.test(options.ios.extension)) {\n          throw new Error(`Your file extensions must match ${extensionRegex.toString()}.`);\n        }\n        if (!this._canRecord) {\n          if (eventEmitter) {\n            this._subscription = eventEmitter.addListener('Expo.Recording.recorderUnloaded', this._cleanupForUnloadedRecorder);\n          }\n          var _yield$ExponentAV$pre = yield ExponentAV.prepareAudioRecorder(options),\n            uri = _yield$ExponentAV$pre.uri,\n            status = _yield$ExponentAV$pre.status;\n          _recorderExists = true;\n          this._uri = uri;\n          this._options = options;\n          this._canRecord = true;\n          var currentStatus = _objectSpread(_objectSpread({}, status), {}, {\n            canRecord: true\n          });\n          this._callOnRecordingStatusUpdateForNewStatus(currentStatus);\n          this._enablePollingIfNecessaryAndPossible();\n          return currentStatus;\n        } else {\n          throw new Error('This Recording object is already prepared to record.');\n        }\n      });\n      function prepareToRecordAsync() {\n        return _prepareToRecordAsync.apply(this, arguments);\n      }\n      return prepareToRecordAsync;\n    }()\n  }, {\n    key: \"getAvailableInputs\",\n    value: (function () {\n      var _getAvailableInputs = _asyncToGenerator(function* () {\n        return ExponentAV.getAvailableInputs();\n      });\n      function getAvailableInputs() {\n        return _getAvailableInputs.apply(this, arguments);\n      }\n      return getAvailableInputs;\n    }())\n  }, {\n    key: \"getCurrentInput\",\n    value: (function () {\n      var _getCurrentInput = _asyncToGenerator(function* () {\n        return ExponentAV.getCurrentInput();\n      });\n      function getCurrentInput() {\n        return _getCurrentInput.apply(this, arguments);\n      }\n      return getCurrentInput;\n    }())\n  }, {\n    key: \"setInput\",\n    value: (function () {\n      var _setInput = _asyncToGenerator(function* (inputUid) {\n        return ExponentAV.setInput(inputUid);\n      });\n      function setInput(_x3) {\n        return _setInput.apply(this, arguments);\n      }\n      return setInput;\n    }())\n  }, {\n    key: \"startAsync\",\n    value: (function () {\n      var _startAsync = _asyncToGenerator(function* () {\n        return this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.startAudioRecording();\n        });\n      });\n      function startAsync() {\n        return _startAsync.apply(this, arguments);\n      }\n      return startAsync;\n    }())\n  }, {\n    key: \"pauseAsync\",\n    value: (function () {\n      var _pauseAsync = _asyncToGenerator(function* () {\n        return this._performOperationAndHandleStatusAsync(function () {\n          return ExponentAV.pauseAudioRecording();\n        });\n      });\n      function pauseAsync() {\n        return _pauseAsync.apply(this, arguments);\n      }\n      return pauseAsync;\n    }())\n  }, {\n    key: \"stopAndUnloadAsync\",\n    value: (function () {\n      var _stopAndUnloadAsync = _asyncToGenerator(function* () {\n        var _stopResult;\n        if (!this._canRecord) {\n          if (this._isDoneRecording) {\n            throw new Error('Cannot unload a Recording that has already been unloaded.');\n          } else {\n            throw new Error('Cannot unload a Recording that has not been prepared.');\n          }\n        }\n        var stopResult;\n        var stopError;\n        try {\n          stopResult = yield ExponentAV.stopAudioRecording();\n        } catch (err) {\n          stopError = err;\n        }\n        if (Platform.OS === 'web' && ((_stopResult = stopResult) == null ? void 0 : _stopResult.uri) !== undefined) {\n          this._uri = stopResult.uri;\n        }\n        yield ExponentAV.unloadAudioRecorder();\n        var status = yield this._cleanupForUnloadedRecorder(stopResult);\n        return stopError ? Promise.reject(stopError) : status;\n      });\n      function stopAndUnloadAsync() {\n        return _stopAndUnloadAsync.apply(this, arguments);\n      }\n      return stopAndUnloadAsync;\n    }())\n  }, {\n    key: \"getURI\",\n    value: function getURI() {\n      return this._uri;\n    }\n  }, {\n    key: \"createNewLoadedSound\",\n    value: (function () {\n      var _createNewLoadedSound = _asyncToGenerator(function* () {\n        var initialStatus = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var onPlaybackStatusUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        console.warn(`createNewLoadedSound is deprecated in favor of createNewLoadedSoundAsync, which has the same API aside from the method name`);\n        return this.createNewLoadedSoundAsync(initialStatus, onPlaybackStatusUpdate);\n      });\n      function createNewLoadedSound() {\n        return _createNewLoadedSound.apply(this, arguments);\n      }\n      return createNewLoadedSound;\n    }())\n  }, {\n    key: \"createNewLoadedSoundAsync\",\n    value: (function () {\n      var _createNewLoadedSoundAsync = _asyncToGenerator(function* () {\n        var initialStatus = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n        var onPlaybackStatusUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n        if (this._uri == null || !this._isDoneRecording) {\n          throw new Error('Cannot create sound when the Recording has not finished!');\n        }\n        return Sound.createAsync({\n          uri: this._uri\n        }, initialStatus, onPlaybackStatusUpdate, false);\n      });\n      function createNewLoadedSoundAsync() {\n        return _createNewLoadedSoundAsync.apply(this, arguments);\n      }\n      return createNewLoadedSoundAsync;\n    }())\n  }]);\n}();\n_Recording = Recording;\nRecording.createAsync = _asyncToGenerator(function* () {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : RecordingOptionsPresets.LOW_QUALITY;\n  var onRecordingStatusUpdate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  var progressUpdateIntervalMillis = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var recording = new _Recording();\n  if (progressUpdateIntervalMillis) {\n    recording._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n  }\n  recording.setOnRecordingStatusUpdate(onRecordingStatusUpdate);\n  yield recording.prepareToRecordAsync(_objectSpread(_objectSpread({}, options), {}, {\n    keepAudioActiveHint: true\n  }));\n  try {\n    var status = yield recording.startAsync();\n    return {\n      recording: recording,\n      status: status\n    };\n  } catch (err) {\n    recording.stopAndUnloadAsync();\n    throw err;\n  }\n});\nexport { PermissionStatus };\nexport * from \"./RecordingConstants\";\nexport * from \"./Recording.types\";", "map": {"version": 3, "names": ["PermissionStatus", "createPermissionHook", "EventEmitter", "Platform", "_DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS", "ExponentAV", "isAudioEnabled", "throwIfAudioIsDisabled", "RecordingOptionsPresets", "Sound", "_recorderExists", "eventEmitter", "OS", "getPermissionsAsync", "_getPermissionsAsync", "apply", "arguments", "_asyncToGenerator", "requestPermissionsAsync", "_requestPermissionsAsync", "usePermissions", "getMethod", "requestMethod", "Recording", "_this", "_classCallCheck", "_subscription", "_canRecord", "_isDoneRecording", "_finalDuration<PERSON><PERSON><PERSON>", "_uri", "_onRecordingStatusUpdate", "_progressUpdateTimeoutVariable", "_progressUpdateIntervalMillis", "_options", "_cleanupForUnloadedRecorder", "_ref", "finalStatus", "_finalStatus$duration", "<PERSON><PERSON><PERSON><PERSON>", "remove", "_disablePolling", "getStatusAsync", "_x", "_pollingLoop", "setTimeout", "_unused", "_performOperationAndHandleStatusAsync", "getAudioRecordingStatus", "status", "canRecord", "isRecording", "isDoneRecording", "_callOnRecordingStatusUpdateForNewStatus", "_createClass", "key", "value", "clearTimeout", "_enablePollingIfNecessaryAndPossible", "_performOperationAndHandleStatusAsync2", "operation", "Error", "_x2", "setOnRecordingStatusUpdate", "onRecordingStatusUpdate", "setProgressUpdateInterval", "progressUpdateIntervalMillis", "_prepareToRecordAsync", "options", "length", "undefined", "LOW_QUALITY", "android", "ios", "extensionRegex", "extension", "test", "toString", "addListener", "_yield$ExponentAV$pre", "prepareAudioRecorder", "uri", "currentStatus", "_objectSpread", "prepareToRecordAsync", "_getAvailableInputs", "getAvailableInputs", "_getCurrentInput", "getCurrentInput", "_setInput", "inputUid", "setInput", "_x3", "_startAsync", "startAudioRecording", "startAsync", "_pauseAsync", "pauseAudioRecording", "pauseAsync", "_stopAndUnloadAsync", "_stopResult", "stopResult", "stopError", "stopAudioRecording", "err", "unloadAudioRecorder", "Promise", "reject", "stopAndUnloadAsync", "getURI", "_createNewLoadedSound", "initialStatus", "onPlaybackStatusUpdate", "console", "warn", "createNewLoadedSoundAsync", "createNewLoadedSound", "_createNewLoadedSoundAsync", "createAsync", "recording", "keepAudioActiveHint"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/Audio/Recording.ts"], "sourcesContent": ["import {\n  PermissionResponse,\n  PermissionStatus,\n  PermissionHookOptions,\n  createPer<PERSON>Hook,\n  EventEmitter,\n  Subscription,\n  Platform,\n} from 'expo-modules-core';\n\nimport {\n  _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS,\n  AVPlaybackStatus,\n  AVPlaybackStatusToSet,\n} from '../AV';\nimport ExponentAV from '../ExponentAV';\nimport { isAudioEnabled, throwIfAudioIsDisabled } from './AudioAvailability';\nimport {\n  RecordingInput,\n  RecordingObject,\n  RecordingOptions,\n  RecordingStatus,\n} from './Recording.types';\nimport { RecordingOptionsPresets } from './RecordingConstants';\nimport { Sound, SoundObject } from './Sound';\n\nlet _recorderExists: boolean = false;\nconst eventEmitter = Platform.OS === 'android' ? new EventEmitter(ExponentAV) : null;\n\n/**\n * Checks user's permissions for audio recording.\n * @return A promise that resolves to an object of type `PermissionResponse`.\n */\nexport async function getPermissionsAsync(): Promise<PermissionResponse> {\n  return ExponentAV.getPermissionsAsync();\n}\n\n/**\n * Asks the user to grant permissions for audio recording.\n * @return A promise that resolves to an object of type `PermissionResponse`.\n */\nexport async function requestPermissionsAsync(): Promise<PermissionResponse> {\n  return ExponentAV.requestPermissionsAsync();\n}\n\n/**\n * Check or request permissions to record audio.\n * This uses both `requestPermissionAsync` and `getPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [permissionResponse, requestPermission] = Audio.usePermissions();\n * ```\n */\nexport const usePermissions = createPermissionHook({\n  getMethod: getPermissionsAsync,\n  requestMethod: requestPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * This class represents an audio recording. After creating an instance of this class, `prepareToRecordAsync`\n * must be called in order to record audio. Once recording is finished, call `stopAndUnloadAsync`. Note that\n * only one recorder is allowed to exist in the state between `prepareToRecordAsync` and `stopAndUnloadAsync`\n * at any given time.\n *\n * Note that your experience must request audio recording permissions in order for recording to function.\n * See the [`Permissions` module](/guides/permissions) for more details.\n *\n * Additionally, audio recording is [not supported in the iOS Simulator](/workflow/ios-simulator/#limitations).\n *\n * @example\n * ```ts\n * const recording = new Audio.Recording();\n * try {\n *   await recording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);\n *   await recording.startAsync();\n *   // You are now recording!\n * } catch (error) {\n *   // An error occurred!\n * }\n * ```\n *\n * @return A newly constructed instance of `Audio.Recording`.\n */\nexport class Recording {\n  _subscription: Subscription | null = null;\n  _canRecord: boolean = false;\n  _isDoneRecording: boolean = false;\n  _finalDurationMillis: number = 0;\n  _uri: string | null = null;\n  _onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null = null;\n  _progressUpdateTimeoutVariable: number | null = null;\n  _progressUpdateIntervalMillis: number = _DEFAULT_PROGRESS_UPDATE_INTERVAL_MILLIS;\n  _options: RecordingOptions | null = null;\n\n  // Internal methods\n\n  _cleanupForUnloadedRecorder = async (finalStatus?: RecordingStatus) => {\n    this._canRecord = false;\n    this._isDoneRecording = true;\n    this._finalDurationMillis = finalStatus?.durationMillis ?? 0;\n    _recorderExists = false;\n    if (this._subscription) {\n      this._subscription.remove();\n      this._subscription = null;\n    }\n    this._disablePolling();\n    return await this.getStatusAsync(); // Automatically calls onRecordingStatusUpdate for the final state.\n  };\n\n  _pollingLoop = async () => {\n    if (isAudioEnabled() && this._canRecord && this._onRecordingStatusUpdate != null) {\n      this._progressUpdateTimeoutVariable = setTimeout(\n        this._pollingLoop,\n        this._progressUpdateIntervalMillis\n      ) as any;\n      try {\n        await this.getStatusAsync();\n      } catch {\n        this._disablePolling();\n      }\n    }\n  };\n\n  _disablePolling() {\n    if (this._progressUpdateTimeoutVariable != null) {\n      clearTimeout(this._progressUpdateTimeoutVariable);\n      this._progressUpdateTimeoutVariable = null;\n    }\n  }\n\n  _enablePollingIfNecessaryAndPossible() {\n    if (isAudioEnabled() && this._canRecord && this._onRecordingStatusUpdate != null) {\n      this._disablePolling();\n      this._pollingLoop();\n    }\n  }\n\n  _callOnRecordingStatusUpdateForNewStatus(status: RecordingStatus) {\n    if (this._onRecordingStatusUpdate != null) {\n      this._onRecordingStatusUpdate(status);\n    }\n  }\n\n  async _performOperationAndHandleStatusAsync(\n    operation: () => Promise<RecordingStatus>\n  ): Promise<RecordingStatus> {\n    throwIfAudioIsDisabled();\n    if (this._canRecord) {\n      const status = await operation();\n      this._callOnRecordingStatusUpdateForNewStatus(status);\n      return status;\n    } else {\n      throw new Error('Cannot complete operation because this recorder is not ready to record.');\n    }\n  }\n\n  /**\n   * Creates and starts a recording using the given options, with optional `onRecordingStatusUpdate` and `progressUpdateIntervalMillis`.\n   *\n   * ```ts\n   * const { recording, status } = await Audio.Recording.createAsync(\n   *   options,\n   *   onRecordingStatusUpdate,\n   *   progressUpdateIntervalMillis\n   * );\n   *\n   * // Which is equivalent to the following:\n   * const recording = new Audio.Recording();\n   * await recording.prepareToRecordAsync(options);\n   * recording.setOnRecordingStatusUpdate(onRecordingStatusUpdate);\n   * await recording.startAsync();\n   * ```\n   *\n   * @param options Options for the recording, including sample rate, bitrate, channels, format, encoder, and extension. If no options are passed to,\n   * the recorder will be created with options `Audio.RecordingOptionsPresets.LOW_QUALITY`. See below for details on `RecordingOptions`.\n   * @param onRecordingStatusUpdate A function taking a single parameter `status` (a dictionary, described in `getStatusAsync`).\n   * @param progressUpdateIntervalMillis The interval between calls of `onRecordingStatusUpdate`. This value defaults to 500 milliseconds.\n   *\n   * @example\n   * ```ts\n   * try {\n   *   const { recording: recordingObject, status } = await Audio.Recording.createAsync(\n   *     Audio.RecordingOptionsPresets.HIGH_QUALITY\n   *   );\n   *   // You are now recording!\n   * } catch (error) {\n   *   // An error occurred!\n   * }\n   * ```\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the following dictionary if creation succeeded.\n   */\n  static createAsync = async (\n    options: RecordingOptions = RecordingOptionsPresets.LOW_QUALITY,\n    onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null = null,\n    progressUpdateIntervalMillis: number | null = null\n  ): Promise<RecordingObject> => {\n    const recording: Recording = new Recording();\n    if (progressUpdateIntervalMillis) {\n      recording._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n    }\n    recording.setOnRecordingStatusUpdate(onRecordingStatusUpdate);\n    await recording.prepareToRecordAsync({\n      ...options,\n      keepAudioActiveHint: true,\n    });\n    try {\n      const status = await recording.startAsync();\n      return { recording, status };\n    } catch (err) {\n      recording.stopAndUnloadAsync();\n      throw err;\n    }\n  };\n\n  // Get status API\n\n  /**\n   * Gets the `status` of the `Recording`.\n   * @return A `Promise` that is resolved with the `RecordingStatus` object.\n   */\n  getStatusAsync = async (): Promise<RecordingStatus> => {\n    // Automatically calls onRecordingStatusUpdate.\n    if (this._canRecord) {\n      return this._performOperationAndHandleStatusAsync(() => ExponentAV.getAudioRecordingStatus());\n    }\n    const status = {\n      canRecord: false,\n      isRecording: false,\n      isDoneRecording: this._isDoneRecording,\n      durationMillis: this._finalDurationMillis,\n    };\n    this._callOnRecordingStatusUpdateForNewStatus(status);\n    return status;\n  };\n\n  /**\n   * Sets a function to be called regularly with the `RecordingStatus` of the `Recording`.\n   *\n   * `onRecordingStatusUpdate` will be called when another call to the API for this recording completes (such as `prepareToRecordAsync()`,\n   * `startAsync()`, `getStatusAsync()`, or `stopAndUnloadAsync()`), and will also be called at regular intervals while the recording can record.\n   * Call `setProgressUpdateInterval()` to modify the interval with which `onRecordingStatusUpdate` is called while the recording can record.\n   *\n   * @param onRecordingStatusUpdate A function taking a single parameter `RecordingStatus`.\n   */\n  setOnRecordingStatusUpdate(onRecordingStatusUpdate: ((status: RecordingStatus) => void) | null) {\n    this._onRecordingStatusUpdate = onRecordingStatusUpdate;\n    if (onRecordingStatusUpdate == null) {\n      this._disablePolling();\n    } else {\n      this._enablePollingIfNecessaryAndPossible();\n    }\n    this.getStatusAsync();\n  }\n\n  /**\n   * Sets the interval with which `onRecordingStatusUpdate` is called while the recording can record.\n   * See `setOnRecordingStatusUpdate` for details. This value defaults to 500 milliseconds.\n   * @param progressUpdateIntervalMillis The new interval between calls of `onRecordingStatusUpdate`.\n   */\n  setProgressUpdateInterval(progressUpdateIntervalMillis: number) {\n    this._progressUpdateIntervalMillis = progressUpdateIntervalMillis;\n    this.getStatusAsync();\n  }\n\n  // Record API\n\n  /**\n   * Loads the recorder into memory and prepares it for recording. This must be called before calling `startAsync()`.\n   * This method can only be called if the `Recording` instance has never yet been prepared.\n   *\n   * @param options `RecordingOptions` for the recording, including sample rate, bitrate, channels, format, encoder, and extension.\n   * If no options are passed to `prepareToRecordAsync()`, the recorder will be created with options `Audio.RecordingOptionsPresets.LOW_QUALITY`.\n   *\n   * @return A `Promise` that is fulfilled when the recorder is loaded and prepared, or rejects if this failed. If another `Recording` exists\n   * in your experience that is currently prepared to record, the `Promise` will reject. If the `RecordingOptions` provided are invalid,\n   * the `Promise` will also reject. The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async prepareToRecordAsync(\n    options: RecordingOptions = RecordingOptionsPresets.LOW_QUALITY\n  ): Promise<RecordingStatus> {\n    throwIfAudioIsDisabled();\n\n    if (_recorderExists) {\n      throw new Error('Only one Recording object can be prepared at a given time.');\n    }\n\n    if (this._isDoneRecording) {\n      throw new Error('This Recording object is done recording; you must make a new one.');\n    }\n\n    if (!options || !options.android || !options.ios) {\n      throw new Error(\n        'You must provide recording options for android and ios in order to prepare to record.'\n      );\n    }\n\n    const extensionRegex = /^\\.\\w+$/;\n    if (\n      !options.android.extension ||\n      !options.ios.extension ||\n      !extensionRegex.test(options.android.extension) ||\n      !extensionRegex.test(options.ios.extension)\n    ) {\n      throw new Error(`Your file extensions must match ${extensionRegex.toString()}.`);\n    }\n\n    if (!this._canRecord) {\n      if (eventEmitter) {\n        this._subscription = eventEmitter.addListener(\n          'Expo.Recording.recorderUnloaded',\n          this._cleanupForUnloadedRecorder\n        );\n      }\n\n      const {\n        uri,\n        status,\n      }: {\n        uri: string | null;\n        // status is of type RecordingStatus, but without the canRecord field populated\n        status: Pick<RecordingStatus, Exclude<keyof RecordingStatus, 'canRecord'>>;\n      } = await ExponentAV.prepareAudioRecorder(options);\n      _recorderExists = true;\n      this._uri = uri;\n      this._options = options;\n      this._canRecord = true;\n\n      const currentStatus = { ...status, canRecord: true };\n      this._callOnRecordingStatusUpdateForNewStatus(currentStatus);\n      this._enablePollingIfNecessaryAndPossible();\n      return currentStatus;\n    } else {\n      throw new Error('This Recording object is already prepared to record.');\n    }\n  }\n\n  /**\n   * Returns a list of available recording inputs. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled with an array of `RecordingInput` objects.\n   */\n  async getAvailableInputs(): Promise<RecordingInput[]> {\n    return ExponentAV.getAvailableInputs();\n  }\n\n  /**\n   * Returns the currently-selected recording input. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled with a `RecordingInput` object.\n   */\n  async getCurrentInput(): Promise<RecordingInput> {\n    return ExponentAV.getCurrentInput();\n  }\n\n  /**\n   * Sets the current recording input.\n   * @param inputUid The uid of a `RecordingInput`.\n   * @return A `Promise` that is resolved if successful or rejected if not.\n   */\n  async setInput(inputUid: string): Promise<void> {\n    return ExponentAV.setInput(inputUid);\n  }\n\n  /**\n   * Begins recording. This method can only be called if the `Recording` has been prepared.\n   * @return A `Promise` that is fulfilled when recording has begun, or rejects if recording could not be started.\n   * The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async startAsync(): Promise<RecordingStatus> {\n    return this._performOperationAndHandleStatusAsync(() => ExponentAV.startAudioRecording());\n  }\n\n  /**\n   * Pauses recording. This method can only be called if the `Recording` has been prepared.\n   *\n   * > This is only available on Android API version 24 and later.\n   *\n   * @return A `Promise` that is fulfilled when recording has paused, or rejects if recording could not be paused.\n   * If the Android API version is less than 24, the `Promise` will reject. The promise is resolved with the\n   * `RecordingStatus` of the recording.\n   */\n  async pauseAsync(): Promise<RecordingStatus> {\n    return this._performOperationAndHandleStatusAsync(() => ExponentAV.pauseAudioRecording());\n  }\n\n  /**\n   * Stops the recording and deallocates the recorder from memory. This reverts the `Recording` instance\n   * to an unprepared state, and another `Recording` instance must be created in order to record again.\n   * This method can only be called if the `Recording` has been prepared.\n   *\n   * > On Android this method may fail with `E_AUDIO_NODATA` when called too soon after `startAsync` and\n   * > no audio data has been recorded yet. In that case the recorded file will be invalid and should be discarded.\n   *\n   * @return A `Promise` that is fulfilled when recording has stopped, or rejects if recording could not be stopped.\n   * The promise is resolved with the `RecordingStatus` of the recording.\n   */\n  async stopAndUnloadAsync(): Promise<RecordingStatus> {\n    if (!this._canRecord) {\n      if (this._isDoneRecording) {\n        throw new Error('Cannot unload a Recording that has already been unloaded.');\n      } else {\n        throw new Error('Cannot unload a Recording that has not been prepared.');\n      }\n    }\n    // We perform a separate native API call so that the state of the Recording can be updated with\n    // the final duration of the recording. (We cast stopStatus as Object to appease Flow)\n    let stopResult: RecordingStatus | undefined;\n    let stopError: Error | undefined;\n    try {\n      stopResult = await ExponentAV.stopAudioRecording();\n    } catch (err) {\n      stopError = err;\n    }\n\n    // Web has to return the URI at the end of recording, so needs a little destructuring\n    if (Platform.OS === 'web' && stopResult?.uri !== undefined) {\n      this._uri = stopResult.uri;\n    }\n\n    // Clean-up and return status\n    await ExponentAV.unloadAudioRecorder();\n    const status = await this._cleanupForUnloadedRecorder(stopResult);\n    return stopError ? Promise.reject(stopError) : status;\n  }\n\n  // Read API\n\n  /**\n   * Gets the local URI of the `Recording`. Note that this will only succeed once the `Recording` is prepared\n   * to record. On web, this will not return the URI until the recording is finished.\n   * @return A `string` with the local URI of the `Recording`, or `null` if the `Recording` is not prepared\n   * to record (or, on Web, if the recording has not finished).\n   */\n  getURI(): string | null {\n    return this._uri;\n  }\n\n  /**\n   * @deprecated Use `createNewLoadedSoundAsync()` instead.\n   */\n  async createNewLoadedSound(\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null\n  ): Promise<SoundObject> {\n    console.warn(\n      `createNewLoadedSound is deprecated in favor of createNewLoadedSoundAsync, which has the same API aside from the method name`\n    );\n    return this.createNewLoadedSoundAsync(initialStatus, onPlaybackStatusUpdate);\n  }\n\n  /**\n   * Creates and loads a new `Sound` object to play back the `Recording`. Note that this will only succeed once the `Recording`\n   * is done recording and `stopAndUnloadAsync()` has been called.\n   *\n   * @param initialStatus The initial intended `PlaybackStatusToSet` of the sound, whose values will override the default initial playback status.\n   * This value defaults to `{}` if no parameter is passed. See the [AV documentation](/versions/latest/sdk/av) for details on `PlaybackStatusToSet`\n   * and the default initial playback status.\n   * @param onPlaybackStatusUpdate A function taking a single parameter `PlaybackStatus`. This value defaults to `null` if no parameter is passed.\n   * See the [AV documentation](/versions/latest/sdk/av) for details on the functionality provided by `onPlaybackStatusUpdate`\n   *\n   * @return A `Promise` that is rejected if creation failed, or fulfilled with the `SoundObject`.\n   */\n  async createNewLoadedSoundAsync(\n    initialStatus: AVPlaybackStatusToSet = {},\n    onPlaybackStatusUpdate: ((status: AVPlaybackStatus) => void) | null = null\n  ): Promise<SoundObject> {\n    if (this._uri == null || !this._isDoneRecording) {\n      throw new Error('Cannot create sound when the Recording has not finished!');\n    }\n    return Sound.createAsync(\n      // $FlowFixMe: Flow can't distinguish between this literal and Asset\n      { uri: this._uri },\n      initialStatus,\n      onPlaybackStatusUpdate,\n      false\n    );\n  }\n}\n\nexport { PermissionResponse, PermissionStatus, PermissionHookOptions };\n\nexport * from './RecordingConstants';\n\nexport * from './Recording.types';\n"], "mappings": ";;;;;;;AAAA,SAEEA,gBAAgB,EAEhBC,oBAAoB,EACpBC,YAAY,EAEZC,QAAQ,QACH,mBAAmB;AAE1B,SACEC,wCAAwC;AAI1C,OAAOC,UAAU;AACjB,SAASC,cAAc,EAAEC,sBAAsB;AAO/C,SAASC,uBAAuB;AAChC,SAASC,KAAK;AAEd,IAAIC,eAAe,GAAY,KAAK;AACpC,IAAMC,YAAY,GAAGR,QAAQ,CAACS,EAAE,KAAK,SAAS,GAAG,IAAIV,YAAY,CAACG,UAAU,CAAC,GAAG,IAAI;AAMpF,gBAAsBQ,mBAAmBA,CAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAExC,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CAFM,aAAkC;IACvC,OAAOZ,UAAU,CAACQ,mBAAmB,EAAE;EACzC,CAAC;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAMD,gBAAsBE,uBAAuBA,CAAA;EAAA,OAAAC,wBAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAE5C,SAAAG,yBAAA;EAAAA,wBAAA,GAAAF,iBAAA,CAFM,aAAsC;IAC3C,OAAOZ,UAAU,CAACa,uBAAuB,EAAE;EAC7C,CAAC;EAAA,OAAAC,wBAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAWD,OAAO,IAAMI,cAAc,GAAGnB,oBAAoB,CAAC;EACjDoB,SAAS,EAAER,mBAAmB;EAC9BS,aAAa,EAAEJ;CAChB,CAAC;AA4BF,WAAaK,SAAS;EAAA,SAAAA,UAAA;IAAA,IAAAC,KAAA;IAAAC,eAAA,OAAAF,SAAA;IAAA,KACpBG,aAAa,GAAwB,IAAI;IAAA,KACzCC,UAAU,GAAY,KAAK;IAAA,KAC3BC,gBAAgB,GAAY,KAAK;IAAA,KACjCC,oBAAoB,GAAW,CAAC;IAAA,KAChCC,IAAI,GAAkB,IAAI;IAAA,KAC1BC,wBAAwB,GAA+C,IAAI;IAAA,KAC3EC,8BAA8B,GAAkB,IAAI;IAAA,KACpDC,6BAA6B,GAAW7B,wCAAwC;IAAA,KAChF8B,QAAQ,GAA4B,IAAI;IAAA,KAIxCC,2BAA2B;MAAA,IAAAC,IAAA,GAAAnB,iBAAA,CAAG,WAAOoB,WAA6B,EAAI;QAAA,IAAAC,qBAAA;QACpEd,KAAI,CAACG,UAAU,GAAG,KAAK;QACvBH,KAAI,CAACI,gBAAgB,GAAG,IAAI;QAC5BJ,KAAI,CAACK,oBAAoB,IAAAS,qBAAA,GAAGD,WAAW,oBAAXA,WAAW,CAAEE,cAAc,YAAAD,qBAAA,GAAI,CAAC;QAC5D5B,eAAe,GAAG,KAAK;QACvB,IAAIc,KAAI,CAACE,aAAa,EAAE;UACtBF,KAAI,CAACE,aAAa,CAACc,MAAM,EAAE;UAC3BhB,KAAI,CAACE,aAAa,GAAG,IAAI;;QAE3BF,KAAI,CAACiB,eAAe,EAAE;QACtB,aAAajB,KAAI,CAACkB,cAAc,EAAE;MACpC,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAP,IAAA,CAAArB,KAAA,OAAAC,SAAA;MAAA;IAAA;IAAA,KAED4B,YAAY,GAAA3B,iBAAA,CAAG,aAAW;MACxB,IAAIX,cAAc,EAAE,IAAIkB,KAAI,CAACG,UAAU,IAAIH,KAAI,CAACO,wBAAwB,IAAI,IAAI,EAAE;QAChFP,KAAI,CAACQ,8BAA8B,GAAGa,UAAU,CAC9CrB,KAAI,CAACoB,YAAY,EACjBpB,KAAI,CAACS,6BAA6B,CAC5B;QACR,IAAI;UACF,MAAMT,KAAI,CAACkB,cAAc,EAAE;SAC5B,CAAC,OAAAI,OAAA,EAAM;UACNtB,KAAI,CAACiB,eAAe,EAAE;;;IAG5B,CAAC;IAAA,KAoGDC,cAAc,GAAAzB,iBAAA,CAAG,aAAqC;MAEpD,IAAIO,KAAI,CAACG,UAAU,EAAE;QACnB,OAAOH,KAAI,CAACuB,qCAAqC,CAAC;UAAA,OAAM1C,UAAU,CAAC2C,uBAAuB,EAAE;QAAA,EAAC;;MAE/F,IAAMC,MAAM,GAAG;QACbC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE5B,KAAI,CAACI,gBAAgB;QACtCW,cAAc,EAAEf,KAAI,CAACK;OACtB;MACDL,KAAI,CAAC6B,wCAAwC,CAACJ,MAAM,CAAC;MACrD,OAAOA,MAAM;IACf,CAAC;EAAA;EAAA,OAAAK,YAAA,CAAA/B,SAAA;IAAAgC,GAAA;IAAAC,KAAA,EA/GD,SAAAf,eAAeA,CAAA;MACb,IAAI,IAAI,CAACT,8BAA8B,IAAI,IAAI,EAAE;QAC/CyB,YAAY,CAAC,IAAI,CAACzB,8BAA8B,CAAC;QACjD,IAAI,CAACA,8BAA8B,GAAG,IAAI;;IAE9C;EAAC;IAAAuB,GAAA;IAAAC,KAAA,EAED,SAAAE,oCAAoCA,CAAA;MAClC,IAAIpD,cAAc,EAAE,IAAI,IAAI,CAACqB,UAAU,IAAI,IAAI,CAACI,wBAAwB,IAAI,IAAI,EAAE;QAChF,IAAI,CAACU,eAAe,EAAE;QACtB,IAAI,CAACG,YAAY,EAAE;;IAEvB;EAAC;IAAAW,GAAA;IAAAC,KAAA,EAED,SAAAH,wCAAwCA,CAACJ,MAAuB;MAC9D,IAAI,IAAI,CAAClB,wBAAwB,IAAI,IAAI,EAAE;QACzC,IAAI,CAACA,wBAAwB,CAACkB,MAAM,CAAC;;IAEzC;EAAC;IAAAM,GAAA;IAAAC,KAAA;MAAA,IAAAG,sCAAA,GAAA1C,iBAAA,CAED,WACE2C,SAAyC;QAEzCrD,sBAAsB,EAAE;QACxB,IAAI,IAAI,CAACoB,UAAU,EAAE;UACnB,IAAMsB,MAAM,SAASW,SAAS,EAAE;UAChC,IAAI,CAACP,wCAAwC,CAACJ,MAAM,CAAC;UACrD,OAAOA,MAAM;SACd,MAAM;UACL,MAAM,IAAIY,KAAK,CAAC,yEAAyE,CAAC;;MAE9F,CAAC;MAAA,SAXKd,qCAAqCA,CAAAe,GAAA;QAAA,OAAAH,sCAAA,CAAA5C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArC+B,qCAAqC;IAAA;EAAA;IAAAQ,GAAA;IAAAC,KAAA,EAsG3C,SAAAO,0BAA0BA,CAACC,uBAAmE;MAC5F,IAAI,CAACjC,wBAAwB,GAAGiC,uBAAuB;MACvD,IAAIA,uBAAuB,IAAI,IAAI,EAAE;QACnC,IAAI,CAACvB,eAAe,EAAE;OACvB,MAAM;QACL,IAAI,CAACiB,oCAAoC,EAAE;;MAE7C,IAAI,CAAChB,cAAc,EAAE;IACvB;EAAC;IAAAa,GAAA;IAAAC,KAAA,EAOD,SAAAS,yBAAyBA,CAACC,4BAAoC;MAC5D,IAAI,CAACjC,6BAA6B,GAAGiC,4BAA4B;MACjE,IAAI,CAACxB,cAAc,EAAE;IACvB;EAAC;IAAAa,GAAA;IAAAC,KAAA;MAAA,IAAAW,qBAAA,GAAAlD,iBAAA,CAeD,aACiE;QAAA,IAA/DmD,OAAA,GAAApD,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAA4BR,uBAAuB,CAAC+D,WAAW;QAE/DhE,sBAAsB,EAAE;QAExB,IAAIG,eAAe,EAAE;UACnB,MAAM,IAAImD,KAAK,CAAC,4DAA4D,CAAC;;QAG/E,IAAI,IAAI,CAACjC,gBAAgB,EAAE;UACzB,MAAM,IAAIiC,KAAK,CAAC,mEAAmE,CAAC;;QAGtF,IAAI,CAACO,OAAO,IAAI,CAACA,OAAO,CAACI,OAAO,IAAI,CAACJ,OAAO,CAACK,GAAG,EAAE;UAChD,MAAM,IAAIZ,KAAK,CACb,uFAAuF,CACxF;;QAGH,IAAMa,cAAc,GAAG,SAAS;QAChC,IACE,CAACN,OAAO,CAACI,OAAO,CAACG,SAAS,IAC1B,CAACP,OAAO,CAACK,GAAG,CAACE,SAAS,IACtB,CAACD,cAAc,CAACE,IAAI,CAACR,OAAO,CAACI,OAAO,CAACG,SAAS,CAAC,IAC/C,CAACD,cAAc,CAACE,IAAI,CAACR,OAAO,CAACK,GAAG,CAACE,SAAS,CAAC,EAC3C;UACA,MAAM,IAAId,KAAK,CAAC,mCAAmCa,cAAc,CAACG,QAAQ,EAAE,GAAG,CAAC;;QAGlF,IAAI,CAAC,IAAI,CAAClD,UAAU,EAAE;UACpB,IAAIhB,YAAY,EAAE;YAChB,IAAI,CAACe,aAAa,GAAGf,YAAY,CAACmE,WAAW,CAC3C,iCAAiC,EACjC,IAAI,CAAC3C,2BAA2B,CACjC;;UAGH,IAAA4C,qBAAA,SAOU1E,UAAU,CAAC2E,oBAAoB,CAACZ,OAAO,CAAC;YANhDa,GAAG,GAAAF,qBAAA,CAAHE,GAAG;YACHhC,MAAM,GAAA8B,qBAAA,CAAN9B,MAAM;UAMRvC,eAAe,GAAG,IAAI;UACtB,IAAI,CAACoB,IAAI,GAAGmD,GAAG;UACf,IAAI,CAAC/C,QAAQ,GAAGkC,OAAO;UACvB,IAAI,CAACzC,UAAU,GAAG,IAAI;UAEtB,IAAMuD,aAAa,GAAAC,aAAA,CAAAA,aAAA,KAAQlC,MAAM;YAAEC,SAAS,EAAE;UAAI,EAAE;UACpD,IAAI,CAACG,wCAAwC,CAAC6B,aAAa,CAAC;UAC5D,IAAI,CAACxB,oCAAoC,EAAE;UAC3C,OAAOwB,aAAa;SACrB,MAAM;UACL,MAAM,IAAIrB,KAAK,CAAC,sDAAsD,CAAC;;MAE3E,CAAC;MAAA,SAzDKuB,oBAAoBA,CAAA;QAAA,OAAAjB,qBAAA,CAAApD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBoE,oBAAoB;IAAA;EAAA;IAAA7B,GAAA;IAAAC,KAAA;MAAA,IAAA6B,mBAAA,GAAApE,iBAAA,CA+D1B,aAAwB;QACtB,OAAOZ,UAAU,CAACiF,kBAAkB,EAAE;MACxC,CAAC;MAAA,SAFKA,kBAAkBA,CAAA;QAAA,OAAAD,mBAAA,CAAAtE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBsE,kBAAkB;IAAA;EAAA;IAAA/B,GAAA;IAAAC,KAAA;MAAA,IAAA+B,gBAAA,GAAAtE,iBAAA,CAQxB,aAAqB;QACnB,OAAOZ,UAAU,CAACmF,eAAe,EAAE;MACrC,CAAC;MAAA,SAFKA,eAAeA,CAAA;QAAA,OAAAD,gBAAA,CAAAxE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfwE,eAAe;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA;MAAA,IAAAiC,SAAA,GAAAxE,iBAAA,CASrB,WAAeyE,QAAgB;QAC7B,OAAOrF,UAAU,CAACsF,QAAQ,CAACD,QAAQ,CAAC;MACtC,CAAC;MAAA,SAFKC,QAAQA,CAAAC,GAAA;QAAA,OAAAH,SAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAR2E,QAAQ;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAqC,WAAA,GAAA5E,iBAAA,CASd,aAAgB;QACd,OAAO,IAAI,CAAC8B,qCAAqC,CAAC;UAAA,OAAM1C,UAAU,CAACyF,mBAAmB,EAAE;QAAA,EAAC;MAC3F,CAAC;MAAA,SAFKC,UAAUA,CAAA;QAAA,OAAAF,WAAA,CAAA9E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV+E,UAAU;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAwC,WAAA,GAAA/E,iBAAA,CAahB,aAAgB;QACd,OAAO,IAAI,CAAC8B,qCAAqC,CAAC;UAAA,OAAM1C,UAAU,CAAC4F,mBAAmB,EAAE;QAAA,EAAC;MAC3F,CAAC;MAAA,SAFKC,UAAUA,CAAA;QAAA,OAAAF,WAAA,CAAAjF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVkF,UAAU;IAAA;EAAA;IAAA3C,GAAA;IAAAC,KAAA;MAAA,IAAA2C,mBAAA,GAAAlF,iBAAA,CAehB,aAAwB;QAAA,IAAAmF,WAAA;QACtB,IAAI,CAAC,IAAI,CAACzE,UAAU,EAAE;UACpB,IAAI,IAAI,CAACC,gBAAgB,EAAE;YACzB,MAAM,IAAIiC,KAAK,CAAC,2DAA2D,CAAC;WAC7E,MAAM;YACL,MAAM,IAAIA,KAAK,CAAC,uDAAuD,CAAC;;;QAK5E,IAAIwC,UAAuC;QAC3C,IAAIC,SAA4B;QAChC,IAAI;UACFD,UAAU,SAAShG,UAAU,CAACkG,kBAAkB,EAAE;SACnD,CAAC,OAAOC,GAAG,EAAE;UACZF,SAAS,GAAGE,GAAG;;QAIjB,IAAIrG,QAAQ,CAACS,EAAE,KAAK,KAAK,IAAI,EAAAwF,WAAA,GAAAC,UAAU,qBAAVD,WAAA,CAAYnB,GAAG,MAAKX,SAAS,EAAE;UAC1D,IAAI,CAACxC,IAAI,GAAGuE,UAAU,CAACpB,GAAG;;QAI5B,MAAM5E,UAAU,CAACoG,mBAAmB,EAAE;QACtC,IAAMxD,MAAM,SAAS,IAAI,CAACd,2BAA2B,CAACkE,UAAU,CAAC;QACjE,OAAOC,SAAS,GAAGI,OAAO,CAACC,MAAM,CAACL,SAAS,CAAC,GAAGrD,MAAM;MACvD,CAAC;MAAA,SA3BK2D,kBAAkBA,CAAA;QAAA,OAAAT,mBAAA,CAAApF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlB4F,kBAAkB;IAAA;EAAA;IAAArD,GAAA;IAAAC,KAAA,EAqCxB,SAAAqD,MAAMA,CAAA;MACJ,OAAO,IAAI,CAAC/E,IAAI;IAClB;EAAC;IAAAyB,GAAA;IAAAC,KAAA;MAAA,IAAAsD,qBAAA,GAAA7F,iBAAA,CAKD,aAE4E;QAAA,IAD1E8F,aAAA,GAAA/F,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAAuC,EAAE;QAAA,IACzCgG,sBAAA,GAAAhG,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAAsE,IAAI;QAE1EiG,OAAO,CAACC,IAAI,CACV,6HAA6H,CAC9H;QACD,OAAO,IAAI,CAACC,yBAAyB,CAACJ,aAAa,EAAEC,sBAAsB,CAAC;MAC9E,CAAC;MAAA,SARKI,oBAAoBA,CAAA;QAAA,OAAAN,qBAAA,CAAA/F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBoG,oBAAoB;IAAA;EAAA;IAAA7D,GAAA;IAAAC,KAAA;MAAA,IAAA6D,0BAAA,GAAApG,iBAAA,CAsB1B,aAE4E;QAAA,IAD1E8F,aAAA,GAAA/F,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAAuC,EAAE;QAAA,IACzCgG,sBAAA,GAAAhG,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAAsE,IAAI;QAE1E,IAAI,IAAI,CAACc,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAACF,gBAAgB,EAAE;UAC/C,MAAM,IAAIiC,KAAK,CAAC,0DAA0D,CAAC;;QAE7E,OAAOpD,KAAK,CAAC6G,WAAW,CAEtB;UAAErC,GAAG,EAAE,IAAI,CAACnD;QAAI,CAAE,EAClBiF,aAAa,EACbC,sBAAsB,EACtB,KAAK,CACN;MACH,CAAC;MAAA,SAdKG,yBAAyBA,CAAA;QAAA,OAAAE,0BAAA,CAAAtG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBmG,yBAAyB;IAAA;EAAA;AAAA;aA1XpB5F,SAAS;AAATA,SAAS,CA6Gb+F,WAAW,GAAArG,iBAAA,CAAG,aAIS;EAAA,IAH5BmD,OAAA,GAAApD,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAA4BR,uBAAuB,CAAC+D,WAAW;EAAA,IAC/DP,uBAAA,GAAAhD,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAAsE,IAAI;EAAA,IAC1EkD,4BAAA,GAAAlD,SAAA,CAAAqD,MAAA,QAAArD,SAAA,QAAAsD,SAAA,GAAAtD,SAAA,MAA8C,IAAI;EAElD,IAAMuG,SAAS,GAAc,IAAIhG,UAAS,EAAE;EAC5C,IAAI2C,4BAA4B,EAAE;IAChCqD,SAAS,CAACtF,6BAA6B,GAAGiC,4BAA4B;;EAExEqD,SAAS,CAACxD,0BAA0B,CAACC,uBAAuB,CAAC;EAC7D,MAAMuD,SAAS,CAACnC,oBAAoB,CAAAD,aAAA,CAAAA,aAAA,KAC/Bf,OAAO;IACVoD,mBAAmB,EAAE;EAAI,EAC1B,CAAC;EACF,IAAI;IACF,IAAMvE,MAAM,SAASsE,SAAS,CAACxB,UAAU,EAAE;IAC3C,OAAO;MAAEwB,SAAS,EAATA,SAAS;MAAEtE,MAAM,EAANA;IAAM,CAAE;GAC7B,CAAC,OAAOuD,GAAG,EAAE;IACZe,SAAS,CAACX,kBAAkB,EAAE;IAC9B,MAAMJ,GAAG;;AAEb,CAAC;AAyQH,SAA6BxG,gBAAgB;AAE7C;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}