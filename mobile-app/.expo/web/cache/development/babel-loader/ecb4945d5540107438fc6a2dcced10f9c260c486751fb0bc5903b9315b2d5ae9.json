{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as React from 'react';\nimport createElement from 'react-native-web/dist/exports/createElement';\nimport ExponentAV from \"./ExponentAV\";\nimport { addFullscreenListener } from \"./FullscreenUtils.web\";\nimport { VideoFullscreenUpdate } from \"./Video.types\";\nvar Video = React.forwardRef(function (props, ref) {\n  return createElement('video', _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref\n  }));\n});\nvar ExponentVideo = function (_React$Component) {\n  function ExponentVideo() {\n    var _this;\n    _classCallCheck(this, ExponentVideo);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, ExponentVideo, [].concat(args));\n    _this.onFullscreenChange = function (isFullscreen) {\n      if (!_this.props.onFullscreenUpdate) return;\n      if (isFullscreen) {\n        _this.props.onFullscreenUpdate({\n          nativeEvent: {\n            fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_PRESENT\n          }\n        });\n      } else {\n        _this.props.onFullscreenUpdate({\n          nativeEvent: {\n            fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_DISMISS\n          }\n        });\n      }\n    };\n    _this.onStatusUpdate = _asyncToGenerator(function* () {\n      if (!_this.props.onStatusUpdate) {\n        return;\n      }\n      var nativeEvent = yield ExponentAV.getStatusForVideo(_this._video);\n      _this.props.onStatusUpdate({\n        nativeEvent: nativeEvent\n      });\n    });\n    _this.onLoadStart = function () {\n      if (!_this.props.onLoadStart) {\n        return;\n      }\n      _this.props.onLoadStart();\n      _this.onStatusUpdate();\n    };\n    _this.onLoadedData = function (event) {\n      if (!_this.props.onLoad) {\n        return;\n      }\n      _this.props.onLoad(event);\n      _this.onStatusUpdate();\n    };\n    _this.onError = function (event) {\n      if (!_this.props.onError) {\n        return;\n      }\n      _this.props.onError(event);\n      _this.onStatusUpdate();\n    };\n    _this.onProgress = function () {\n      _this.onStatusUpdate();\n    };\n    _this.onSeeking = function () {\n      _this.onStatusUpdate();\n    };\n    _this.onEnded = function () {\n      _this.onStatusUpdate();\n    };\n    _this.onLoadedMetadata = function () {\n      _this.onStatusUpdate();\n    };\n    _this.onCanPlay = function (event) {\n      if (!_this.props.onReadyForDisplay) {\n        return;\n      }\n      _this.props.onReadyForDisplay(event);\n      _this.onStatusUpdate();\n    };\n    _this.onStalled = function () {\n      _this.onStatusUpdate();\n    };\n    _this.onRef = function (ref) {\n      _this._removeFullscreenListener == null ? void 0 : _this._removeFullscreenListener();\n      if (ref) {\n        _this._video = ref;\n        _this._removeFullscreenListener = addFullscreenListener(_this._video, _this.onFullscreenChange);\n        _this.onStatusUpdate();\n      } else {\n        _this._removeFullscreenListener = undefined;\n      }\n    };\n    return _this;\n  }\n  _inherits(ExponentVideo, _React$Component);\n  return _createClass(ExponentVideo, [{\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      var _this$_removeFullscre;\n      (_this$_removeFullscre = this._removeFullscreenListener) == null ? void 0 : _this$_removeFullscre.call(this);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n        source = _this$props.source,\n        _this$props$status = _this$props.status,\n        status = _this$props$status === void 0 ? {} : _this$props$status,\n        objectFit = _this$props.resizeMode,\n        useNativeControls = _this$props.useNativeControls,\n        style = _this$props.style;\n      var customStyle = {\n        position: undefined,\n        objectFit: objectFit,\n        overflow: 'hidden'\n      };\n      return React.createElement(Video, {\n        ref: this.onRef,\n        onLoadStart: this.onLoadStart,\n        onLoadedData: this.onLoadedData,\n        onError: this.onError,\n        onTimeUpdate: this.onProgress,\n        onSeeking: this.onSeeking,\n        onEnded: this.onEnded,\n        onLoadedMetadata: this.onLoadedMetadata,\n        onCanPlay: this.onCanPlay,\n        onStalled: this.onStalled,\n        src: (source == null ? void 0 : source.uri) || undefined,\n        muted: status.isMuted,\n        loop: status.isLooping,\n        autoPlay: status.shouldPlay,\n        controls: useNativeControls,\n        style: [style, customStyle],\n        playsInline: true\n      });\n    }\n  }]);\n}(React.Component);\nexport { ExponentVideo as default };", "map": {"version": 3, "names": ["React", "createElement", "ExponentAV", "addFullscreenListener", "VideoFullscreenUpdate", "Video", "forwardRef", "props", "ref", "_objectSpread", "ExponentVideo", "_React$Component", "_this", "_classCallCheck", "_len", "arguments", "length", "args", "Array", "_key", "_callSuper", "concat", "onFullscreenChange", "isFullscreen", "onFullscreenUpdate", "nativeEvent", "fullscreenUpdate", "PLAYER_DID_PRESENT", "PLAYER_DID_DISMISS", "onStatusUpdate", "_asyncToGenerator", "getStatusForVideo", "_video", "onLoadStart", "onLoadedData", "event", "onLoad", "onError", "onProgress", "onSeeking", "onEnded", "onLoadedMetadata", "onCanPlay", "onReadyForDisplay", "onStalled", "onRef", "_removeFullscreenListener", "undefined", "_inherits", "_createClass", "key", "value", "componentWillUnmount", "_this$_removeFullscre", "call", "render", "_this$props", "source", "_this$props$status", "status", "objectFit", "resizeMode", "useNativeControls", "style", "customStyle", "position", "overflow", "onTimeUpdate", "src", "uri", "muted", "isMuted", "loop", "isLooping", "autoPlay", "shouldPlay", "controls", "playsInline", "Component", "default"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-av/src/ExponentVideo.web.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { ViewProps } from 'react-native';\nimport createElement from 'react-native-web/dist/exports/createElement';\n\nimport { AVPlaybackNativeSource, AVPlaybackStatus, AVPlaybackStatusToSet } from './AV';\nimport ExponentAV from './ExponentAV';\nimport { addFullscreenListener } from './FullscreenUtils.web';\nimport {\n  VideoFullscreenUpdate,\n  VideoFullscreenUpdateEvent,\n  VideoReadyForDisplayEvent,\n} from './Video.types';\n\ntype ExponentVideoProps = {\n  source: AVPlaybackNativeSource | null;\n  resizeMode?: object;\n  status?: AVPlaybackStatusToSet;\n  useNativeControls?: boolean;\n  onStatusUpdate?: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onReadyForDisplay?: (event: { nativeEvent: VideoReadyForDisplayEvent }) => void;\n  onFullscreenUpdate?: (event: { nativeEvent: VideoFullscreenUpdateEvent }) => void;\n  onLoadStart: () => void;\n  onLoad: (event: { nativeEvent: AVPlaybackStatus }) => void;\n  onError: (event: { nativeEvent: { error: string } }) => void;\n  // Required by react-native\n  scaleX?: number;\n  scaleY?: number;\n  translateX?: number;\n  translateY?: number;\n  rotation?: number;\n} & ViewProps;\n\nexport type NaturalSize = {\n  width: number;\n  height: number;\n  orientation: 'portrait' | 'landscape';\n};\n\nconst Video: any = React.forwardRef<HTMLVideoElement, ExponentVideoProps>((props, ref) =>\n  createElement('video', { ...props, ref })\n);\n\nexport default class ExponentVideo extends React.Component<ExponentVideoProps> {\n  _video?: HTMLVideoElement;\n  _removeFullscreenListener?: () => any;\n\n  componentWillUnmount() {\n    this._removeFullscreenListener?.();\n  }\n\n  onFullscreenChange = (isFullscreen: boolean) => {\n    if (!this.props.onFullscreenUpdate) return;\n    if (isFullscreen) {\n      this.props.onFullscreenUpdate({\n        nativeEvent: { fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_PRESENT },\n      });\n    } else {\n      this.props.onFullscreenUpdate({\n        nativeEvent: { fullscreenUpdate: VideoFullscreenUpdate.PLAYER_DID_DISMISS },\n      });\n    }\n  };\n\n  onStatusUpdate = async () => {\n    if (!this.props.onStatusUpdate) {\n      return;\n    }\n    const nativeEvent = await ExponentAV.getStatusForVideo(this._video);\n    this.props.onStatusUpdate({ nativeEvent });\n  };\n\n  onLoadStart = () => {\n    if (!this.props.onLoadStart) {\n      return;\n    }\n    this.props.onLoadStart();\n    this.onStatusUpdate();\n  };\n\n  onLoadedData = (event) => {\n    if (!this.props.onLoad) {\n      return;\n    }\n    this.props.onLoad(event);\n    this.onStatusUpdate();\n  };\n\n  onError = (event) => {\n    if (!this.props.onError) {\n      return;\n    }\n    this.props.onError(event);\n    this.onStatusUpdate();\n  };\n\n  onProgress = () => {\n    this.onStatusUpdate();\n  };\n\n  onSeeking = () => {\n    this.onStatusUpdate();\n  };\n\n  onEnded = () => {\n    this.onStatusUpdate();\n  };\n\n  onLoadedMetadata = () => {\n    this.onStatusUpdate();\n  };\n\n  onCanPlay = (event) => {\n    if (!this.props.onReadyForDisplay) {\n      return;\n    }\n    this.props.onReadyForDisplay(event);\n    this.onStatusUpdate();\n  };\n\n  onStalled = () => {\n    this.onStatusUpdate();\n  };\n\n  onRef = (ref: HTMLVideoElement | null) => {\n    this._removeFullscreenListener?.();\n    if (ref) {\n      this._video = ref;\n      this._removeFullscreenListener = addFullscreenListener(this._video, this.onFullscreenChange);\n      this.onStatusUpdate();\n    } else {\n      this._removeFullscreenListener = undefined;\n    }\n  };\n\n  render() {\n    const { source, status = {}, resizeMode: objectFit, useNativeControls, style } = this.props;\n\n    const customStyle = {\n      position: undefined,\n      objectFit,\n      overflow: 'hidden',\n    };\n    return (\n      <Video\n        ref={this.onRef}\n        onLoadStart={this.onLoadStart}\n        onLoadedData={this.onLoadedData}\n        onError={this.onError}\n        onTimeUpdate={this.onProgress}\n        onSeeking={this.onSeeking}\n        onEnded={this.onEnded}\n        onLoadedMetadata={this.onLoadedMetadata}\n        onCanPlay={this.onCanPlay}\n        onStalled={this.onStalled}\n        src={source?.uri || undefined}\n        muted={status.isMuted}\n        loop={status.isLooping}\n        autoPlay={status.shouldPlay}\n        controls={useNativeControls}\n        style={[style, customStyle]}\n        playsInline\n      />\n    );\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,aAAa,MAAM,6CAA6C;AAGvE,OAAOC,UAAU;AACjB,SAASC,qBAAqB;AAC9B,SACEC,qBAAqB;AA8BvB,IAAMC,KAAK,GAAQL,KAAK,CAACM,UAAU,CAAuC,UAACC,KAAK,EAAEC,GAAG;EAAA,OACnFP,aAAa,CAAC,OAAO,EAAAQ,aAAA,CAAAA,aAAA,KAAOF,KAAK;IAAEC,GAAG,EAAHA;EAAG,EAAE,CAAC;AAAA,EAC1C;AAAC,IAEmBE,aAAc,aAAAC,gBAAA;EAAA,SAAAD,cAAA;IAAA,IAAAE,KAAA;IAAAC,eAAA,OAAAH,aAAA;IAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAC,IAAA,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAAF,IAAA,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAAAP,KAAA,GAAAQ,UAAA,OAAAV,aAAA,KAAAW,MAAA,CAAAJ,IAAA;IAAAL,KAAA,CAQjCU,kBAAkB,GAAG,UAACC,YAAqB,EAAI;MAC7C,IAAI,CAACX,KAAA,CAAKL,KAAK,CAACiB,kBAAkB,EAAE;MACpC,IAAID,YAAY,EAAE;QAChBX,KAAA,CAAKL,KAAK,CAACiB,kBAAkB,CAAC;UAC5BC,WAAW,EAAE;YAAEC,gBAAgB,EAAEtB,qBAAqB,CAACuB;UAAkB;SAC1E,CAAC;OACH,MAAM;QACLf,KAAA,CAAKL,KAAK,CAACiB,kBAAkB,CAAC;UAC5BC,WAAW,EAAE;YAAEC,gBAAgB,EAAEtB,qBAAqB,CAACwB;UAAkB;SAC1E,CAAC;;IAEN,CAAC;IAAAhB,KAAA,CAEDiB,cAAc,GAAAC,iBAAA,CAAG,aAAW;MAC1B,IAAI,CAAClB,KAAA,CAAKL,KAAK,CAACsB,cAAc,EAAE;QAC9B;;MAEF,IAAMJ,WAAW,SAASvB,UAAU,CAAC6B,iBAAiB,CAACnB,KAAA,CAAKoB,MAAM,CAAC;MACnEpB,KAAA,CAAKL,KAAK,CAACsB,cAAc,CAAC;QAAEJ,WAAW,EAAXA;MAAW,CAAE,CAAC;IAC5C,CAAC;IAAAb,KAAA,CAEDqB,WAAW,GAAG,YAAK;MACjB,IAAI,CAACrB,KAAA,CAAKL,KAAK,CAAC0B,WAAW,EAAE;QAC3B;;MAEFrB,KAAA,CAAKL,KAAK,CAAC0B,WAAW,EAAE;MACxBrB,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAEDsB,YAAY,GAAG,UAACC,KAAK,EAAI;MACvB,IAAI,CAACvB,KAAA,CAAKL,KAAK,CAAC6B,MAAM,EAAE;QACtB;;MAEFxB,KAAA,CAAKL,KAAK,CAAC6B,MAAM,CAACD,KAAK,CAAC;MACxBvB,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAEDyB,OAAO,GAAG,UAACF,KAAK,EAAI;MAClB,IAAI,CAACvB,KAAA,CAAKL,KAAK,CAAC8B,OAAO,EAAE;QACvB;;MAEFzB,KAAA,CAAKL,KAAK,CAAC8B,OAAO,CAACF,KAAK,CAAC;MACzBvB,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAED0B,UAAU,GAAG,YAAK;MAChB1B,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAED2B,SAAS,GAAG,YAAK;MACf3B,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAED4B,OAAO,GAAG,YAAK;MACb5B,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAED6B,gBAAgB,GAAG,YAAK;MACtB7B,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAED8B,SAAS,GAAG,UAACP,KAAK,EAAI;MACpB,IAAI,CAACvB,KAAA,CAAKL,KAAK,CAACoC,iBAAiB,EAAE;QACjC;;MAEF/B,KAAA,CAAKL,KAAK,CAACoC,iBAAiB,CAACR,KAAK,CAAC;MACnCvB,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAEDgC,SAAS,GAAG,YAAK;MACfhC,KAAA,CAAKiB,cAAc,EAAE;IACvB,CAAC;IAAAjB,KAAA,CAEDiC,KAAK,GAAG,UAACrC,GAA4B,EAAI;MACvCI,KAAA,CAAKkC,yBAAyB,oBAA9BlC,KAAA,CAAKkC,yBAAyB,CAAE,CAAE;MAClC,IAAItC,GAAG,EAAE;QACPI,KAAA,CAAKoB,MAAM,GAAGxB,GAAG;QACjBI,KAAA,CAAKkC,yBAAyB,GAAG3C,qBAAqB,CAACS,KAAA,CAAKoB,MAAM,EAAEpB,KAAA,CAAKU,kBAAkB,CAAC;QAC5FV,KAAA,CAAKiB,cAAc,EAAE;OACtB,MAAM;QACLjB,KAAA,CAAKkC,yBAAyB,GAAGC,SAAS;;IAE9C,CAAC;IAAA,OAAAnC,KAAA;EAAA;EAAAoC,SAAA,CAAAtC,aAAA,EAAAC,gBAAA;EAAA,OAAAsC,YAAA,CAAAvC,aAAA;IAAAwC,GAAA;IAAAC,KAAA,EAtFD,SAAAC,oBAAoBA,CAAA;MAAA,IAAAC,qBAAA;MAClB,CAAAA,qBAAA,OAAI,CAACP,yBAAyB,qBAA9BO,qBAAA,CAAAC,IAAA,KAAgC,CAAE;IACpC;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EAsFD,SAAAI,MAAMA,CAAA;MACJ,IAAAC,WAAA,GAAiF,IAAI,CAACjD,KAAK;QAAnFkD,MAAM,GAAAD,WAAA,CAANC,MAAM;QAAAC,kBAAA,GAAAF,WAAA,CAAEG,MAAM;QAANA,MAAM,GAAAD,kBAAA,cAAG,EAAE,GAAAA,kBAAA;QAAcE,SAAS,GAAAJ,WAAA,CAArBK,UAAU;QAAaC,iBAAiB,GAAAN,WAAA,CAAjBM,iBAAiB;QAAEC,KAAK,GAAAP,WAAA,CAALO,KAAK;MAE5E,IAAMC,WAAW,GAAG;QAClBC,QAAQ,EAAElB,SAAS;QACnBa,SAAS,EAATA,SAAS;QACTM,QAAQ,EAAE;OACX;MACD,OACElE,KAAA,CAAAC,aAAA,CAACI,KAAK;QACJG,GAAG,EAAE,IAAI,CAACqC,KAAK;QACfZ,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,YAAY,EAAE,IAAI,CAACA,YAAY;QAC/BG,OAAO,EAAE,IAAI,CAACA,OAAO;QACrB8B,YAAY,EAAE,IAAI,CAAC7B,UAAU;QAC7BC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;QACvCC,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBE,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBwB,GAAG,EAAE,CAAAX,MAAM,oBAANA,MAAM,CAAEY,GAAG,KAAItB,SAAS;QAC7BuB,KAAK,EAAEX,MAAM,CAACY,OAAO;QACrBC,IAAI,EAAEb,MAAM,CAACc,SAAS;QACtBC,QAAQ,EAAEf,MAAM,CAACgB,UAAU;QAC3BC,QAAQ,EAAEd,iBAAiB;QAC3BC,KAAK,EAAE,CAACA,KAAK,EAAEC,WAAW,CAAC;QAC3Ba,WAAW;MAAA,EACX;IAEN;EAAC;AAAA,EAzHwC7E,KAAK,CAAC8E,SAA6B;AAAA,SAAzDpE,aAAc,IAAAqE,OAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}