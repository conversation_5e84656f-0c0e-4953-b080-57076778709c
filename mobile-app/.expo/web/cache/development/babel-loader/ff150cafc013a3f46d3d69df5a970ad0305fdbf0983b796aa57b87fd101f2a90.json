{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-status-bar/src/StatusBar.types.ts"], "sourcesContent": ["// @docsMissing\nexport type StatusBarStyle = 'auto' | 'inverted' | 'light' | 'dark';\n// @docsMissing\nexport type StatusBarAnimation = 'none' | 'fade' | 'slide';\n\n// @needsAudit\nexport type StatusBarProps = {\n  /**\n   * Sets the color of the status bar text. Default value is `\"auto\"` which\n   * picks the appropriate value according to the active color scheme, eg:\n   * if your app is dark mode, the style will be `\"light\"`.\n   * @default 'auto'\n   */\n  style?: StatusBarStyle;\n\n  /**\n   * If the transition between status bar property changes should be\n   * animated. Supported for `backgroundColor`, `barStyle` and `hidden`.\n   */\n  animated?: boolean;\n\n  /**\n   * If the status bar is hidden.\n   */\n  hidden?: boolean;\n\n  /**\n   * The transition effect when showing and hiding the status bar using\n   * the hidden prop.\n   * @default 'fade'\n   * @platform ios\n   */\n  hideTransitionAnimation?: StatusBarAnimation;\n\n  /**\n   * If the network activity indicator should be visible.\n   * @platform ios\n   */\n  networkActivityIndicatorVisible?: boolean;\n\n  /**\n   * The background color of the status bar.\n   * @platform android\n   */\n  backgroundColor?: string;\n\n  /**\n   * If the status bar is translucent. When translucent is set to `true`,\n   * the app will draw under the status bar. This is the default behaviour in\n   * projects created with Expo tools because it is consistent with iOS.\n   * @platform android\n   */\n  translucent?: boolean;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}