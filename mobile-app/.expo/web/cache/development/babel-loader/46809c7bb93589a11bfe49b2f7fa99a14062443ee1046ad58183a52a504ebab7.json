{"ast": null, "code": "import * as React from 'react';\nimport StyleSheet from \"../StyleSheet\";\nimport View from \"../View\";\nvar RootTagContext = React.createContext(null);\nvar AppContainer = React.forwardRef(function (props, forwardedRef) {\n  var children = props.children,\n    WrapperComponent = props.WrapperComponent;\n  var innerView = React.createElement(View, {\n    children: children,\n    key: 1,\n    style: styles.appContainer\n  });\n  if (WrapperComponent) {\n    innerView = React.createElement(WrapperComponent, null, innerView);\n  }\n  return React.createElement(RootTagContext.Provider, {\n    value: props.rootTag\n  }, React.createElement(View, {\n    ref: forwardedRef,\n    style: styles.appContainer\n  }, innerView));\n});\nAppContainer.displayName = 'AppContainer';\nexport default AppContainer;\nvar styles = StyleSheet.create({\n  appContainer: {\n    flex: 1,\n    pointerEvents: 'box-none'\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "RootTagContext", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "children", "WrapperComponent", "innerView", "createElement", "key", "style", "styles", "appContainer", "Provider", "value", "rootTag", "ref", "displayName", "create", "flex", "pointerEvents"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/react-native-web/dist/exports/AppRegistry/AppContainer.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport * as React from 'react';\nimport StyleSheet from '../StyleSheet';\nimport View from '../View';\nvar RootTagContext = /*#__PURE__*/React.createContext(null);\nvar AppContainer = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var children = props.children,\n    WrapperComponent = props.WrapperComponent;\n  var innerView = /*#__PURE__*/React.createElement(View, {\n    children: children,\n    key: 1,\n    style: styles.appContainer\n  });\n  if (WrapperComponent) {\n    innerView = /*#__PURE__*/React.createElement(WrapperComponent, null, innerView);\n  }\n  return /*#__PURE__*/React.createElement(RootTagContext.Provider, {\n    value: props.rootTag\n  }, /*#__PURE__*/React.createElement(View, {\n    ref: forwardedRef,\n    style: styles.appContainer\n  }, innerView));\n});\nAppContainer.displayName = 'AppContainer';\nexport default AppContainer;\nvar styles = StyleSheet.create({\n  appContainer: {\n    flex: 1,\n    pointerEvents: 'box-none'\n  }\n});"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU;AACjB,OAAOC,IAAI;AACX,IAAIC,cAAc,GAAgBH,KAAK,CAACI,aAAa,CAAC,IAAI,CAAC;AAC3D,IAAIC,YAAY,GAAgBL,KAAK,CAACM,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACxE,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,gBAAgB,GAAGH,KAAK,CAACG,gBAAgB;EAC3C,IAAIC,SAAS,GAAgBX,KAAK,CAACY,aAAa,CAACV,IAAI,EAAE;IACrDO,QAAQ,EAAEA,QAAQ;IAClBI,GAAG,EAAE,CAAC;IACNC,KAAK,EAAEC,MAAM,CAACC;EAChB,CAAC,CAAC;EACF,IAAIN,gBAAgB,EAAE;IACpBC,SAAS,GAAgBX,KAAK,CAACY,aAAa,CAACF,gBAAgB,EAAE,IAAI,EAAEC,SAAS,CAAC;EACjF;EACA,OAAoBX,KAAK,CAACY,aAAa,CAACT,cAAc,CAACc,QAAQ,EAAE;IAC/DC,KAAK,EAAEX,KAAK,CAACY;EACf,CAAC,EAAenB,KAAK,CAACY,aAAa,CAACV,IAAI,EAAE;IACxCkB,GAAG,EAAEZ,YAAY;IACjBM,KAAK,EAAEC,MAAM,CAACC;EAChB,CAAC,EAAEL,SAAS,CAAC,CAAC;AAChB,CAAC,CAAC;AACFN,YAAY,CAACgB,WAAW,GAAG,cAAc;AACzC,eAAehB,YAAY;AAC3B,IAAIU,MAAM,GAAGd,UAAU,CAACqB,MAAM,CAAC;EAC7BN,YAAY,EAAE;IACZO,IAAI,EAAE,CAAC;IACPC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}