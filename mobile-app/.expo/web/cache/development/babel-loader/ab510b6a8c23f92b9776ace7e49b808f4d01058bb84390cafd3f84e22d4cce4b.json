{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { PermissionStatus, createPermissionHook, UnavailabilityError, CodedError } from 'expo-modules-core';\nimport ExponentImagePicker from \"./ExponentImagePicker\";\nfunction validateOptions(options) {\n  var aspect = options.aspect,\n    quality = options.quality,\n    videoMaxDuration = options.videoMaxDuration;\n  if (aspect != null) {\n    var _aspect = _slicedToArray(aspect, 2),\n      x = _aspect[0],\n      y = _aspect[1];\n    if (x <= 0 || y <= 0) {\n      throw new CodedError('ERR_INVALID_ARGUMENT', `Invalid aspect ratio values ${x}:${y}. Provide positive numbers.`);\n    }\n  }\n  if (quality && (quality < 0 || quality > 1)) {\n    throw new CodedError('ERR_INVALID_ARGUMENT', `Invalid 'quality' value ${quality}. Provide a value between 0 and 1.`);\n  }\n  if (videoMaxDuration && videoMaxDuration < 0) {\n    throw new CodedError('ERR_INVALID_ARGUMENT', `Invalid 'videoMaxDuration' value ${videoMaxDuration}. Provide a non-negative number.`);\n  }\n  return options;\n}\nvar DEPRECATED_RESULT_KEYS = ['uri', 'assetId', 'width', 'height', 'type', 'exif', 'base64', 'duration', 'fileName', 'fileSize'];\nfunction mergeDeprecatedResult(result) {\n  var _result$assets;\n  var firstAsset = result == null ? void 0 : (_result$assets = result.assets) == null ? void 0 : _result$assets[0];\n  var deprecatedResult = _objectSpread(_objectSpread({}, result), {}, {\n    get cancelled() {\n      console.warn('Key \"cancelled\" in the image picker result is deprecated and will be removed in SDK 48, use \"canceled\" instead');\n      return this.canceled;\n    }\n  });\n  var _loop = function _loop(key) {\n    Object.defineProperty(deprecatedResult, key, {\n      get: function get() {\n        console.warn(`Key \"${key}\" in the image picker result is deprecated and will be removed in SDK 48, you can access selected assets through the \"assets\" array instead`);\n        return firstAsset == null ? void 0 : firstAsset[key];\n      }\n    });\n  };\n  for (var key of DEPRECATED_RESULT_KEYS) {\n    _loop(key);\n  }\n  return deprecatedResult;\n}\nexport function getCameraPermissionsAsync() {\n  return _getCameraPermissionsAsync.apply(this, arguments);\n}\nfunction _getCameraPermissionsAsync() {\n  _getCameraPermissionsAsync = _asyncToGenerator(function* () {\n    return ExponentImagePicker.getCameraPermissionsAsync();\n  });\n  return _getCameraPermissionsAsync.apply(this, arguments);\n}\nexport function getMediaLibraryPermissionsAsync() {\n  return _getMediaLibraryPermissionsAsync.apply(this, arguments);\n}\nfunction _getMediaLibraryPermissionsAsync() {\n  _getMediaLibraryPermissionsAsync = _asyncToGenerator(function* () {\n    var writeOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    return ExponentImagePicker.getMediaLibraryPermissionsAsync(writeOnly);\n  });\n  return _getMediaLibraryPermissionsAsync.apply(this, arguments);\n}\nexport function requestCameraPermissionsAsync() {\n  return _requestCameraPermissionsAsync.apply(this, arguments);\n}\nfunction _requestCameraPermissionsAsync() {\n  _requestCameraPermissionsAsync = _asyncToGenerator(function* () {\n    return ExponentImagePicker.requestCameraPermissionsAsync();\n  });\n  return _requestCameraPermissionsAsync.apply(this, arguments);\n}\nexport function requestMediaLibraryPermissionsAsync() {\n  return _requestMediaLibraryPermissionsAsync.apply(this, arguments);\n}\nfunction _requestMediaLibraryPermissionsAsync() {\n  _requestMediaLibraryPermissionsAsync = _asyncToGenerator(function* () {\n    var writeOnly = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var imagePickerMethod = ExponentImagePicker.requestMediaLibraryPermissionsAsync;\n    return imagePickerMethod(writeOnly);\n  });\n  return _requestMediaLibraryPermissionsAsync.apply(this, arguments);\n}\nexport var useMediaLibraryPermissions = createPermissionHook({\n  getMethod: function getMethod(options) {\n    return getMediaLibraryPermissionsAsync(options == null ? void 0 : options.writeOnly);\n  },\n  requestMethod: function requestMethod(options) {\n    return requestMediaLibraryPermissionsAsync(options == null ? void 0 : options.writeOnly);\n  }\n});\nexport var useCameraPermissions = createPermissionHook({\n  getMethod: getCameraPermissionsAsync,\n  requestMethod: requestCameraPermissionsAsync\n});\nexport function getPendingResultAsync() {\n  return _getPendingResultAsync.apply(this, arguments);\n}\nfunction _getPendingResultAsync() {\n  _getPendingResultAsync = _asyncToGenerator(function* () {\n    if (ExponentImagePicker.getPendingResultAsync) {\n      return ExponentImagePicker.getPendingResultAsync();\n    }\n    return [];\n  });\n  return _getPendingResultAsync.apply(this, arguments);\n}\nexport function launchCameraAsync() {\n  return _launchCameraAsync.apply(this, arguments);\n}\nfunction _launchCameraAsync() {\n  _launchCameraAsync = _asyncToGenerator(function* () {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    if (!ExponentImagePicker.launchCameraAsync) {\n      throw new UnavailabilityError('ImagePicker', 'launchCameraAsync');\n    }\n    var result = yield ExponentImagePicker.launchCameraAsync(validateOptions(options));\n    return mergeDeprecatedResult(result);\n  });\n  return _launchCameraAsync.apply(this, arguments);\n}\nexport function launchImageLibraryAsync(_x) {\n  return _launchImageLibraryAsync.apply(this, arguments);\n}\nfunction _launchImageLibraryAsync() {\n  _launchImageLibraryAsync = _asyncToGenerator(function* (options) {\n    if (!ExponentImagePicker.launchImageLibraryAsync) {\n      throw new UnavailabilityError('ImagePicker', 'launchImageLibraryAsync');\n    }\n    if (options != null && options.allowsEditing && options.allowsMultipleSelection) {\n      console.warn('[expo-image-picker] `allowsEditing` is not supported when `allowsMultipleSelection` is enabled and will be ignored.' + \"Disable either 'allowsEditing' or 'allowsMultipleSelection' in 'launchImageLibraryAsync' \" + 'to fix this warning.');\n    }\n    var result = yield ExponentImagePicker.launchImageLibraryAsync(options != null ? options : {});\n    return mergeDeprecatedResult(result);\n  });\n  return _launchImageLibraryAsync.apply(this, arguments);\n}\nexport * from \"./ImagePicker.types\";\nexport { PermissionStatus };", "map": {"version": 3, "names": ["PermissionStatus", "createPermissionHook", "UnavailabilityError", "CodedError", "ExponentImagePicker", "validateOptions", "options", "aspect", "quality", "videoMaxDuration", "_aspect", "_slicedToArray", "x", "y", "DEPRECATED_RESULT_KEYS", "mergeDeprecatedResult", "result", "_result$assets", "firstAsset", "assets", "deprecatedResult", "_objectSpread", "cancelled", "console", "warn", "canceled", "_loop", "key", "Object", "defineProperty", "get", "getCameraPermissionsAsync", "_getCameraPermissionsAsync", "apply", "arguments", "_asyncToGenerator", "getMediaLibraryPermissionsAsync", "_getMediaLibraryPermissionsAsync", "writeOnly", "length", "undefined", "requestCameraPermissionsAsync", "_requestCameraPermissionsAsync", "requestMediaLibraryPermissionsAsync", "_requestMediaLibraryPermissionsAsync", "imagePickerMethod", "useMediaLibraryPermissions", "getMethod", "requestMethod", "useCameraPermissions", "getPendingResultAsync", "_getPendingResultAsync", "launchCameraAsync", "_launchCameraAsync", "launchImageLibraryAsync", "_x", "_launchImageLibraryAsync", "allowsEditing", "allowsMultipleSelection"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/expo-image-picker/src/ImagePicker.ts"], "sourcesContent": ["import {\n  PermissionStatus,\n  PermissionExpiration,\n  PermissionHookOptions,\n  PermissionResponse,\n  createPermissionHook,\n  UnavailabilityError,\n  CodedError,\n} from 'expo-modules-core';\n\nimport ExponentImagePicker from './ExponentImagePicker';\nimport {\n  CameraPermissionResponse,\n  MediaLibraryPermissionResponse,\n  ImagePickerResult,\n  ImagePickerErrorResult,\n  ImagePickerOptions,\n} from './ImagePicker.types';\n\nfunction validateOptions(options: ImagePickerOptions) {\n  const { aspect, quality, videoMaxDuration } = options;\n\n  if (aspect != null) {\n    const [x, y] = aspect;\n\n    if (x <= 0 || y <= 0) {\n      throw new CodedError(\n        'ERR_INVALID_ARGUMENT',\n        `Invalid aspect ratio values ${x}:${y}. Provide positive numbers.`\n      );\n    }\n  }\n\n  if (quality && (quality < 0 || quality > 1)) {\n    throw new CodedError(\n      'ERR_INVALID_ARGUMENT',\n      `Invalid 'quality' value ${quality}. Provide a value between 0 and 1.`\n    );\n  }\n\n  if (videoMaxDuration && videoMaxDuration < 0) {\n    throw new CodedError(\n      'ERR_INVALID_ARGUMENT',\n      `Invalid 'videoMaxDuration' value ${videoMaxDuration}. Provide a non-negative number.`\n    );\n  }\n\n  return options;\n}\n\nconst DEPRECATED_RESULT_KEYS = [\n  'uri',\n  'assetId',\n  'width',\n  'height',\n  'type',\n  'exif',\n  'base64',\n  'duration',\n  'fileName',\n  'fileSize',\n];\nfunction mergeDeprecatedResult(result: ImagePickerResult): ImagePickerResult {\n  const firstAsset = result?.assets?.[0];\n  const deprecatedResult = {\n    ...result,\n    get cancelled() {\n      console.warn(\n        'Key \"cancelled\" in the image picker result is deprecated and will be removed in SDK 48, use \"canceled\" instead'\n      );\n      return this.canceled;\n    },\n  };\n  for (const key of DEPRECATED_RESULT_KEYS) {\n    Object.defineProperty(deprecatedResult, key, {\n      get() {\n        console.warn(\n          `Key \"${key}\" in the image picker result is deprecated and will be removed in SDK 48, you can access selected assets through the \"assets\" array instead`\n        );\n        return firstAsset?.[key];\n      },\n    });\n  }\n  return deprecatedResult;\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing camera.\n * @return A promise that fulfills with an object of type [CameraPermissionResponse](#camerapermissionresponse).\n */\nexport async function getCameraPermissionsAsync(): Promise<CameraPermissionResponse> {\n  return ExponentImagePicker.getCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing photos.\n * @param writeOnly Whether to request write or read and write permissions. Defaults to `false`\n * @return A promise that fulfills with an object of type [MediaLibraryPermissionResponse](#medialibrarypermissionresponse).\n */\nexport async function getMediaLibraryPermissionsAsync(\n  writeOnly: boolean = false\n): Promise<MediaLibraryPermissionResponse> {\n  return ExponentImagePicker.getMediaLibraryPermissionsAsync(writeOnly);\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing camera. This does nothing on web because the\n * browser camera is not used.\n * @return A promise that fulfills with an object of type [CameraPermissionResponse](#camerarollpermissionresponse).\n */\nexport async function requestCameraPermissionsAsync(): Promise<CameraPermissionResponse> {\n  return ExponentImagePicker.requestCameraPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for accessing user's photo. This method does nothing on web.\n * @param writeOnly Whether to request write or read and write permissions. Defaults to `false`\n * @return A promise that fulfills with an object of type [MediaLibraryPermissionResponse](#medialibrarypermissionresponse).\n */\nexport async function requestMediaLibraryPermissionsAsync(\n  writeOnly: boolean = false\n): Promise<MediaLibraryPermissionResponse> {\n  const imagePickerMethod = ExponentImagePicker.requestMediaLibraryPermissionsAsync;\n  return imagePickerMethod(writeOnly);\n}\n\n// @needsAudit\n/**\n * Check or request permissions to access the media library.\n * This uses both `requestMediaLibraryPermissionsAsync` and `getMediaLibraryPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = ImagePicker.useMediaLibraryPermissions();\n * ```\n */\nexport const useMediaLibraryPermissions = createPermissionHook<\n  MediaLibraryPermissionResponse,\n  { writeOnly?: boolean }\n>({\n  // TODO(cedric): permission requesters should have an options param or a different requester\n  getMethod: (options) => getMediaLibraryPermissionsAsync(options?.writeOnly),\n  requestMethod: (options) => requestMediaLibraryPermissionsAsync(options?.writeOnly),\n});\n\n// @needsAudit\n/**\n * Check or request permissions to access the camera.\n * This uses both `requestCameraPermissionsAsync` and `getCameraPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = ImagePicker.useCameraPermissions();\n * ```\n */\nexport const useCameraPermissions = createPermissionHook({\n  getMethod: getCameraPermissionsAsync,\n  requestMethod: requestCameraPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * Android system sometimes kills the `MainActivity` after the `ImagePicker` finishes. When this\n * happens, we lost the data selected from the `ImagePicker`. However, you can retrieve the lost\n * data by calling `getPendingResultAsync`. You can test this functionality by turning on\n * `Don't keep activities` in the developer options.\n * @return\n * - **On Android:** a promise that resolves to an array of objects of exactly same type as in\n * `ImagePicker.launchImageLibraryAsync` or `ImagePicker.launchCameraAsync` if the `ImagePicker`\n * finished successfully. Otherwise, to the array of [`ImagePickerErrorResult`](#imagepickerimagepickererrorresult).\n * - **On other platforms:** an empty array.\n */\nexport async function getPendingResultAsync(): Promise<\n  (ImagePickerResult | ImagePickerErrorResult)[]\n> {\n  if (ExponentImagePicker.getPendingResultAsync) {\n    return ExponentImagePicker.getPendingResultAsync();\n  }\n  return [];\n}\n\n// @needsAudit\n/**\n * Display the system UI for taking a photo with the camera. Requires `Permissions.CAMERA`.\n * On Android and iOS 10 `Permissions.CAMERA_ROLL` is also required. On mobile web, this must be\n * called immediately in a user interaction like a button press, otherwise the browser will block\n * the request without a warning.\n * > **Note:** Make sure that you handle `MainActivity` destruction on **Android**. See [ImagePicker.getPendingResultAsync](#imagepickergetpendingresultasync).\n * > **Notes for Web:** The system UI can only be shown after user activation (e.g. a `Button` press).\n * Therefore, calling `launchCameraAsync` in `componentDidMount`, for example, will **not** work as\n * intended. The `cancelled` event will not be returned in the browser due to platform restrictions\n * and inconsistencies across browsers.\n * @param options An `ImagePickerOptions` object.\n * @return A promise that resolves to an object with `canceled` and `assets` fields.\n * When the user canceled the action the `assets` is always `null`, otherwise it's an array of\n * the selected media assets which have a form of [`ImagePickerAsset`](#imagepickerasset).\n */\nexport async function launchCameraAsync(\n  options: ImagePickerOptions = {}\n): Promise<ImagePickerResult> {\n  if (!ExponentImagePicker.launchCameraAsync) {\n    throw new UnavailabilityError('ImagePicker', 'launchCameraAsync');\n  }\n  const result = await ExponentImagePicker.launchCameraAsync(validateOptions(options));\n  return mergeDeprecatedResult(result);\n}\n\n// @needsAudit\n/**\n * Display the system UI for choosing an image or a video from the phone's library.\n * Requires `Permissions.MEDIA_LIBRARY` on iOS 10 only. On mobile web, this must be     called\n * immediately in a user interaction like a button press, otherwise the browser will block the\n * request without a warning.\n *\n * **Animated GIFs support:** On Android, if the selected image is an animated GIF, the result image will be an\n * animated GIF too if and only if `quality` is explicitly set to `1.0` and `allowsEditing` is set to `false`.\n * Otherwise compression and/or cropper will pick the first frame of the GIF and return it as the\n * result (on Android the result will be a PNG). On iOS, both quality and cropping are supported.\n *\n * > **Notes for Web:** The system UI can only be shown after user activation (e.g. a `Button` press).\n * Therefore, calling `launchImageLibraryAsync` in `componentDidMount`, for example, will **not**\n * work as intended. The `cancelled` event will not be returned in the browser due to platform\n * restrictions and inconsistencies across browsers.\n * @param options An object extended by [`ImagePickerOptions`](#imagepickeroptions).\n * @return A promise that resolves to an object with `canceled` and `assets` fields.\n * When the user canceled the action the `assets` is always `null`, otherwise it's an array of\n * the selected media assets which have a form of [`ImagePickerAsset`](#imagepickerasset).\n */\nexport async function launchImageLibraryAsync(\n  options?: ImagePickerOptions\n): Promise<ImagePickerResult> {\n  if (!ExponentImagePicker.launchImageLibraryAsync) {\n    throw new UnavailabilityError('ImagePicker', 'launchImageLibraryAsync');\n  }\n  if (options?.allowsEditing && options.allowsMultipleSelection) {\n    console.warn(\n      '[expo-image-picker] `allowsEditing` is not supported when `allowsMultipleSelection` is enabled and will be ignored.' +\n        \"Disable either 'allowsEditing' or 'allowsMultipleSelection' in 'launchImageLibraryAsync' \" +\n        'to fix this warning.'\n    );\n  }\n  const result = await ExponentImagePicker.launchImageLibraryAsync(options ?? {});\n  return mergeDeprecatedResult(result);\n}\n\nexport * from './ImagePicker.types';\n\nexport { PermissionStatus, PermissionExpiration, PermissionHookOptions, PermissionResponse };\n"], "mappings": ";;;;;AAAA,SACEA,gBAAgB,EAIhBC,oBAAoB,EACpBC,mBAAmB,EACnBC,UAAU,QACL,mBAAmB;AAE1B,OAAOC,mBAAmB;AAS1B,SAASC,eAAeA,CAACC,OAA2B;EAClD,IAAQC,MAAM,GAAgCD,OAAO,CAA7CC,MAAM;IAAEC,OAAO,GAAuBF,OAAO,CAArCE,OAAO;IAAEC,gBAAgB,GAAKH,OAAO,CAA5BG,gBAAgB;EAEzC,IAAIF,MAAM,IAAI,IAAI,EAAE;IAClB,IAAAG,OAAA,GAAAC,cAAA,CAAeJ,MAAM;MAAdK,CAAC,GAAAF,OAAA;MAAEG,CAAC,GAAAH,OAAA;IAEX,IAAIE,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,EAAE;MACpB,MAAM,IAAIV,UAAU,CAClB,sBAAsB,EACtB,+BAA+BS,CAAC,IAAIC,CAAC,6BAA6B,CACnE;;;EAIL,IAAIL,OAAO,KAAKA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,MAAM,IAAIL,UAAU,CAClB,sBAAsB,EACtB,2BAA2BK,OAAO,oCAAoC,CACvE;;EAGH,IAAIC,gBAAgB,IAAIA,gBAAgB,GAAG,CAAC,EAAE;IAC5C,MAAM,IAAIN,UAAU,CAClB,sBAAsB,EACtB,oCAAoCM,gBAAgB,kCAAkC,CACvF;;EAGH,OAAOH,OAAO;AAChB;AAEA,IAAMQ,sBAAsB,GAAG,CAC7B,KAAK,EACL,SAAS,EACT,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,QAAQ,EACR,UAAU,EACV,UAAU,EACV,UAAU,CACX;AACD,SAASC,qBAAqBA,CAACC,MAAyB;EAAA,IAAAC,cAAA;EACtD,IAAMC,UAAU,GAAGF,MAAM,qBAAAC,cAAA,GAAND,MAAM,CAAEG,MAAM,qBAAdF,cAAA,CAAiB,CAAC,CAAC;EACtC,IAAMG,gBAAgB,GAAAC,aAAA,CAAAA,aAAA,KACjBL,MAAM;IACT,IAAIM,SAASA,CAAA;MACXC,OAAO,CAACC,IAAI,CACV,gHAAgH,CACjH;MACD,OAAO,IAAI,CAACC,QAAQ;IACtB;EAAC,EACF;EAAC,IAAAC,KAAA,YAAAA,MAAAC,GAAA,EACwC;IACxCC,MAAM,CAACC,cAAc,CAACT,gBAAgB,EAAEO,GAAG,EAAE;MAC3CG,GAAG,WAAHA,GAAGA,CAAA;QACDP,OAAO,CAACC,IAAI,CACV,QAAQG,GAAG,6IAA6I,CACzJ;QACD,OAAOT,UAAU,oBAAVA,UAAU,CAAGS,GAAG,CAAC;MAC1B;KACD,CAAC;GACH;EATD,KAAK,IAAMA,GAAG,IAAIb,sBAAsB;IAAAY,KAAA,CAAAC,GAAA;EAAA;EAUxC,OAAOP,gBAAgB;AACzB;AAOA,gBAAsBW,yBAAyBA,CAAA;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAE9C,SAAAF,2BAAA;EAAAA,0BAAA,GAAAG,iBAAA,CAFM,aAAwC;IAC7C,OAAO/B,mBAAmB,CAAC2B,yBAAyB,EAAE;EACxD,CAAC;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAQD,gBAAsBE,+BAA+BA,CAAA;EAAA,OAAAC,gCAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAIpD,SAAAG,iCAAA;EAAAA,gCAAA,GAAAF,iBAAA,CAJM,aACqB;IAAA,IAA1BG,SAAA,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,MAAqB,KAAK;IAE1B,OAAO9B,mBAAmB,CAACgC,+BAA+B,CAACE,SAAS,CAAC;EACvE,CAAC;EAAA,OAAAD,gCAAA,CAAAJ,KAAA,OAAAC,SAAA;AAAA;AAQD,gBAAsBO,6BAA6BA,CAAA;EAAA,OAAAC,8BAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAElD,SAAAQ,+BAAA;EAAAA,8BAAA,GAAAP,iBAAA,CAFM,aAA4C;IACjD,OAAO/B,mBAAmB,CAACqC,6BAA6B,EAAE;EAC5D,CAAC;EAAA,OAAAC,8BAAA,CAAAT,KAAA,OAAAC,SAAA;AAAA;AAQD,gBAAsBS,mCAAmCA,CAAA;EAAA,OAAAC,oCAAA,CAAAX,KAAA,OAAAC,SAAA;AAAA;AAKxD,SAAAU,qCAAA;EAAAA,oCAAA,GAAAT,iBAAA,CALM,aACqB;IAAA,IAA1BG,SAAA,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,MAAqB,KAAK;IAE1B,IAAMW,iBAAiB,GAAGzC,mBAAmB,CAACuC,mCAAmC;IACjF,OAAOE,iBAAiB,CAACP,SAAS,CAAC;EACrC,CAAC;EAAA,OAAAM,oCAAA,CAAAX,KAAA,OAAAC,SAAA;AAAA;AAYD,OAAO,IAAMY,0BAA0B,GAAG7C,oBAAoB,CAG5D;EAEA8C,SAAS,EAAE,SAAXA,SAASA,CAAGzC,OAAO;IAAA,OAAK8B,+BAA+B,CAAC9B,OAAO,oBAAPA,OAAO,CAAEgC,SAAS,CAAC;EAAA;EAC3EU,aAAa,EAAE,SAAfA,aAAaA,CAAG1C,OAAO;IAAA,OAAKqC,mCAAmC,CAACrC,OAAO,oBAAPA,OAAO,CAAEgC,SAAS,CAAC;EAAA;CACpF,CAAC;AAYF,OAAO,IAAMW,oBAAoB,GAAGhD,oBAAoB,CAAC;EACvD8C,SAAS,EAAEhB,yBAAyB;EACpCiB,aAAa,EAAEP;CAChB,CAAC;AAcF,gBAAsBS,qBAAqBA,CAAA;EAAA,OAAAC,sBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAO1C,SAAAiB,uBAAA;EAAAA,sBAAA,GAAAhB,iBAAA,CAPM,aAAoC;IAGzC,IAAI/B,mBAAmB,CAAC8C,qBAAqB,EAAE;MAC7C,OAAO9C,mBAAmB,CAAC8C,qBAAqB,EAAE;;IAEpD,OAAO,EAAE;EACX,CAAC;EAAA,OAAAC,sBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAkBD,gBAAsBkB,iBAAiBA,CAAA;EAAA,OAAAC,kBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAQtC,SAAAmB,mBAAA;EAAAA,kBAAA,GAAAlB,iBAAA,CARM,aAC2B;IAAA,IAAhC7B,OAAA,GAAA4B,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,MAA8B,EAAE;IAEhC,IAAI,CAAC9B,mBAAmB,CAACgD,iBAAiB,EAAE;MAC1C,MAAM,IAAIlD,mBAAmB,CAAC,aAAa,EAAE,mBAAmB,CAAC;;IAEnE,IAAMc,MAAM,SAASZ,mBAAmB,CAACgD,iBAAiB,CAAC/C,eAAe,CAACC,OAAO,CAAC,CAAC;IACpF,OAAOS,qBAAqB,CAACC,MAAM,CAAC;EACtC,CAAC;EAAA,OAAAqC,kBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAuBD,gBAAsBoB,uBAAuBA,CAAAC,EAAA;EAAA,OAAAC,wBAAA,CAAAvB,KAAA,OAAAC,SAAA;AAAA;AAe5C,SAAAsB,yBAAA;EAAAA,wBAAA,GAAArB,iBAAA,CAfM,WACL7B,OAA4B;IAE5B,IAAI,CAACF,mBAAmB,CAACkD,uBAAuB,EAAE;MAChD,MAAM,IAAIpD,mBAAmB,CAAC,aAAa,EAAE,yBAAyB,CAAC;;IAEzE,IAAII,OAAO,YAAPA,OAAO,CAAEmD,aAAa,IAAInD,OAAO,CAACoD,uBAAuB,EAAE;MAC7DnC,OAAO,CAACC,IAAI,CACV,qHAAqH,GACnH,2FAA2F,GAC3F,sBAAsB,CACzB;;IAEH,IAAMR,MAAM,SAASZ,mBAAmB,CAACkD,uBAAuB,CAAChD,OAAO,WAAPA,OAAO,GAAI,EAAE,CAAC;IAC/E,OAAOS,qBAAqB,CAACC,MAAM,CAAC;EACtC,CAAC;EAAA,OAAAwC,wBAAA,CAAAvB,KAAA,OAAAC,SAAA;AAAA;AAED;AAEA,SAASlC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}