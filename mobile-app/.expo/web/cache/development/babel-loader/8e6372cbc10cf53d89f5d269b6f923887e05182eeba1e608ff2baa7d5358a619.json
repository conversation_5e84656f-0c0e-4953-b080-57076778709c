{"ast": null, "code": "import createIconSet from \"./createIconSet\";\nimport font from \"./vendor/react-native-vector-icons/Fonts/Feather.ttf\";\nimport glyphMap from \"./vendor/react-native-vector-icons/glyphmaps/Feather.json\";\nexport default createIconSet(glyphMap, 'feather', font);", "map": {"version": 3, "names": ["createIconSet", "font", "glyphMap"], "sources": ["/Users/<USER>/Documents/augment-projects/symptomosMvp/mobile-app/node_modules/@expo/vector-icons/src/Feather.ts"], "sourcesContent": ["import createIconSet from './createIconSet';\nimport font from './vendor/react-native-vector-icons/Fonts/Feather.ttf';\nimport glyphMap from './vendor/react-native-vector-icons/glyphmaps/Feather.json';\n\nexport default createIconSet(glyphMap, 'feather', font);\n"], "mappings": "AAAA,OAAOA,aAAa;AACpB,OAAOC,IAAI;AACX,OAAOC,QAAQ;AAEf,eAAeF,aAAa,CAACE,QAAQ,EAAE,SAAS,EAAED,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}