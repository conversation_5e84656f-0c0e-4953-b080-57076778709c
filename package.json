{"name": "symptom-os-mvp", "version": "1.0.0", "description": "24-hour MVP demo for Symptom-OS patient-doctor communication", "main": "server/index.js", "scripts": {"server": "cd server && npm start", "mobile": "cd mobile-app && npm start", "web": "cd doctor-web && npm run dev", "seed": "node scripts/seed.js", "demo": "npm run seed && concurrently \"npm run server\" \"npm run web\" \"npm run mobile\"", "install-all": "npm install && cd server && npm install && cd ../mobile-app && npm install && cd ../doctor-web && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["symptom-os", "mvp", "demo", "patient", "doctor"], "author": "Symptom-OS Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "uuid": "^11.1.0"}}