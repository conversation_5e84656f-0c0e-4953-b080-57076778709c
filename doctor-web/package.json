{"name": "symptom-os-doctor-web", "version": "1.0.0", "description": "Doctor web interface for Symptom-OS MVP", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"p5": "^2.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}}