import React, { useState, useRef, useEffect } from 'react';

const ChatPane = ({ messages, onSendMessage, onCollapse, isCollapsed = false }) => {
  const [inputText, setInputText] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const messagesEndRef = useRef(null);

  // Filter messages based on search
  const filteredMessages = messages.filter(message => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    const messageText = getMessageText(message).toLowerCase();
    return messageText.includes(searchLower);
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [filteredMessages]);

  const generateSummary = () => {
    const recentMessages = messages.slice(-5);
    const symptoms = recentMessages
      .filter(m => m.subtype === 'symptom')
      .map(m => m.payload.text)
      .join('; ');

    return symptoms || 'No recent symptoms reported';
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputText.trim()) {
      onSendMessage(inputText.trim());
      setInputText('');
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getMessageText = (message) => {
    switch (message.subtype) {
      case 'symptom':
        return `Symptom: "${message.payload.text}"`;
      case 'image':
        return `Shared image: ${message.payload.caption}`;
      case 'text':
        return message.payload.text;
      case 'recipe':
        return `Prescribed: ${message.payload.medication} ${message.payload.dosage}`;
      default:
        return 'Unknown message type';
    }
  };

  if (isCollapsed) {
    return (
      <div className="w-12 bg-white border-l border-gray-200 flex flex-col items-center py-4">
        <button
          onClick={onCollapse}
          className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Expand Chat"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 20l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
          </svg>
        </button>
        <div className="mt-2 text-xs text-gray-500 writing-mode-vertical">
          {messages.length} msgs
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Enhanced Header */}
      <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <h3 className="font-semibold text-gray-800">Patient Communication</h3>
            <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium">
              {messages.length} messages
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-white rounded transition-colors"
              title={isMinimized ? "Expand" : "Minimize"}
            >
              <svg className={`w-4 h-4 transition-transform ${isMinimized ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <button
              onClick={onCollapse}
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-white rounded transition-colors"
              title="Collapse Chat"
            >
              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Chat Search */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search conversation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
        </div>

        {/* Quick Summary */}
        {!isMinimized && (
          <div className="mt-3 p-2 bg-white rounded-lg border border-gray-200">
            <p className="text-xs font-medium text-gray-600 mb-1">Recent Summary:</p>
            <p className="text-xs text-gray-700 line-clamp-2">{generateSummary()}</p>
          </div>
        )}
      </div>

      {/* Messages */}
      {!isMinimized && (
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.from === 'doctor' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
                message.from === 'doctor'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              <p>{getMessageText(message)}</p>
              <p className={`text-xs mt-1 ${
                message.from === 'doctor' ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {formatTime(message.timestamp)}
              </p>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      )}

      {/* Enhanced Input Section */}
      {!isMinimized && (
        <div className="border-t bg-gray-50 p-4">
          {/* Quick Actions Bar */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex space-x-2">
              <button
                onClick={() => setShowQuickActions(!showQuickActions)}
                className="px-3 py-1 bg-blue-100 text-blue-700 text-xs rounded-full hover:bg-blue-200 transition-colors"
              >
                Quick Actions
              </button>
              <button className="px-3 py-1 bg-green-100 text-green-700 text-xs rounded-full hover:bg-green-200 transition-colors">
                📋 Templates
              </button>
              <button className="px-3 py-1 bg-purple-100 text-purple-700 text-xs rounded-full hover:bg-purple-200 transition-colors">
                🎤 Voice Note
              </button>
            </div>
            <span className="text-xs text-gray-500">
              {filteredMessages.length !== messages.length && `${filteredMessages.length}/${messages.length} shown`}
            </span>
          </div>

          {/* Quick Actions Panel */}
          {showQuickActions && (
            <div className="mb-3 p-3 bg-white rounded-lg border border-gray-200">
              <p className="text-xs font-medium text-gray-600 mb-2">Quick Actions:</p>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { text: '✅ Approve treatment plan', action: 'approve' },
                  { text: '📅 Schedule follow-up', action: 'schedule' },
                  { text: '💊 Adjust medication', action: 'medication' },
                  { text: '🚨 Mark as urgent', action: 'urgent' }
                ].map((action) => (
                  <button
                    key={action.action}
                    onClick={() => {
                      setInputText(action.text);
                      setShowQuickActions(false);
                    }}
                    className="p-2 text-xs text-left bg-gray-50 hover:bg-gray-100 rounded border transition-colors"
                  >
                    {action.text}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Message Input */}
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="flex space-x-2">
              <textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Type your response to the patient..."
                rows={2}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <button
                type="submit"
                disabled={!inputText.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                <span>Send</span>
              </button>
            </div>

            {/* Smart Quick Responses */}
            <div className="flex flex-wrap gap-1">
              {[
                '💊 Take with food and monitor symptoms',
                '📊 Please track your symptoms daily',
                '⏰ Schedule follow-up in 1 week',
                '🩺 Continue current treatment plan',
                '⚠️ Contact me if symptoms worsen'
              ].map((quickResponse) => (
                <button
                  key={quickResponse}
                  onClick={() => setInputText(quickResponse)}
                  className="px-2 py-1 text-xs bg-white text-gray-600 rounded border hover:bg-gray-50 transition-colors"
                >
                  {quickResponse}
                </button>
              ))}
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default ChatPane;
