/**
 * JOY DIVISION STYLE AUDIO VISUALIZER - WEB VERSION
 * "Atmosphere" - Creating the perfect ambiance for medical AI
 * 
 * P5.js powered reactive visualization with concentric frequency rings
 */

import React, { useRef, useEffect } from 'react';
import p5 from 'p5';

const JoyDivisionVisualizer = ({ 
  agentState = 'idle', // 'idle', 'listening', 'thinking', 'speaking'
  audioLevel = 0,
  width = 400,
  height = 400,
  className = ''
}) => {
  const containerRef = useRef();
  const p5Instance = useRef();

  useEffect(() => {
    if (!containerRef.current) return;

    const sketch = (p) => {
      let frequencyData = new Array(64).fill(0);
      let smoothedData = new Array(64).fill(0);
      let time = 0;
      let centerX, centerY, maxRadius;
      const numRings = 10;
      const numPoints = 120;

      p.setup = () => {
        p.createCanvas(width, height);
        centerX = width / 2;
        centerY = height / 2;
        maxRadius = Math.min(width, height) * 0.35;
        p.strokeCap(p.ROUND);
        p.strokeJoin(p.ROUND);
      };

      p.draw = () => {
        // Dark background with subtle gradient
        drawBackground(p);
        
        time += 0.016;
        
        // Generate frequency data based on agent state
        generateFrequencyData(p);
        
        // Draw the Joy Division style rings
        drawFrequencyRings(p);
        
        // Draw center element
        drawCenterElement(p);
        
        // Draw state indicator
        drawStateIndicator(p);
      };

      const drawBackground = (p) => {
        // Create radial gradient background
        for (let r = maxRadius * 2; r > 0; r--) {
          const alpha = p.map(r, 0, maxRadius * 2, 15, 5);
          p.fill(10, 10, 15, alpha);
          p.noStroke();
          p.ellipse(centerX, centerY, r * 2);
        }
      };

      const generateFrequencyData = (p) => {
        const baseAmplitude = getBaseAmplitude();
        const noiseLevel = getNoiseLevel();
        
        for (let i = 0; i < frequencyData.length; i++) {
          let value = 0;
          
          switch (agentState) {
            case 'listening':
              // Reactive spikes with audio input
              value = (audioLevel * 0.9 + p.random(0.1)) * 
                      (1 + p.sin(time * 4 + i * 0.15) * 0.4) *
                      p.pow(i / frequencyData.length, 0.7);
              break;
              
            case 'thinking':
              // Complex interference patterns
              const wave1 = p.sin(time * 2.3 + i * 0.18);
              const wave2 = p.sin(time * 3.7 + i * 0.12);
              const wave3 = p.sin(time * 1.1 + i * 0.25);
              value = baseAmplitude * 
                      (0.6 + 0.4 * wave1) *
                      (0.7 + 0.3 * wave2) *
                      (0.8 + 0.2 * wave3) *
                      p.exp(-p.pow(i - 32, 2) / 400);
              break;
              
            case 'speaking':
              // Speech formant simulation
              const f1 = p.exp(-p.pow(i - 12, 2) / 80);  // Low formant
              const f2 = p.exp(-p.pow(i - 28, 2) / 120); // Mid formant
              const f3 = p.exp(-p.pow(i - 48, 2) / 160); // High formant
              value = baseAmplitude * 0.9 * 
                      (f1 + f2 * 0.8 + f3 * 0.6) *
                      (0.7 + 0.3 * p.sin(time * 12 + i * 0.4));
              break;
              
            default: // idle
              value = noiseLevel * 0.15 * 
                      (0.5 + 0.5 * p.sin(time * 0.8 + i * 0.08));
          }
          
          // Add organic noise
          value += (p.random() - 0.5) * noiseLevel * 0.08;
          
          // Smooth interpolation
          smoothedData[i] = p.lerp(smoothedData[i], value, 0.12);
          frequencyData[i] = p.constrain(smoothedData[i], 0, 1);
        }
      };

      const getBaseAmplitude = () => {
        switch (agentState) {
          case 'listening': return 0.7 + audioLevel * 0.3;
          case 'thinking': return 0.8;
          case 'speaking': return 0.9;
          default: return 0.25;
        }
      };

      const getNoiseLevel = () => {
        switch (agentState) {
          case 'listening': return 0.4;
          case 'thinking': return 0.3;
          case 'speaking': return 0.5;
          default: return 0.15;
        }
      };

      const getEmotionalColor = (ring) => {
        const ringPhase = (ring / numRings) * p.TWO_PI;
        const timePhase = time * 0.5;

        switch (agentState) {
          case 'idle':
            // Soft, calming blues and purples
            return {
              r: p.floor(100 + 80 * p.sin(timePhase + ringPhase)),
              g: p.floor(150 + 60 * p.sin(timePhase + ringPhase + 1)),
              b: p.floor(200 + 55 * p.sin(timePhase + ringPhase + 2))
            };

          case 'listening':
            // Warm greens and teals
            return {
              r: p.floor(80 + 60 * p.sin(timePhase + ringPhase + 1)),
              g: p.floor(180 + 75 * p.sin(timePhase + ringPhase)),
              b: p.floor(140 + 70 * p.sin(timePhase + ringPhase + 0.5))
            };

          case 'thinking':
            // Gentle oranges and yellows
            return {
              r: p.floor(200 + 55 * p.sin(timePhase + ringPhase)),
              g: p.floor(160 + 70 * p.sin(timePhase + ringPhase + 0.8)),
              b: p.floor(100 + 50 * p.sin(timePhase + ringPhase + 1.5))
            };

          case 'speaking':
            // Soft pinks and lavenders
            return {
              r: p.floor(180 + 60 * p.sin(timePhase + ringPhase)),
              g: p.floor(130 + 70 * p.sin(timePhase + ringPhase + 1)),
              b: p.floor(200 + 55 * p.sin(timePhase + ringPhase + 0.5))
            };

          case 'emergency':
            // Urgent but caring - warm reds with golden highlights
            return {
              r: p.floor(220 + 35 * p.sin(timePhase * 3 + ringPhase)),
              g: p.floor(100 + 80 * p.sin(timePhase * 2 + ringPhase + 1)),
              b: p.floor(80 + 60 * p.sin(timePhase + ringPhase + 2))
            };

          default:
            return { r: 255, g: 255, b: 255 }; // Fallback white
        }
      };

      const drawFrequencyRings = (p) => {
        const ringSpacing = maxRadius / numRings;
        
        for (let ring = 0; ring < numRings; ring++) {
          const baseRadius = ringSpacing * (ring + 1);
          const opacity = 255 * (1 - (ring / numRings) * 0.6);
          const strokeWeight = p.max(0.8, 2.5 - ring * 0.15);
          
          // Emotional color with fade
          const color = getEmotionalColor(ring);
          p.stroke(color.r, color.g, color.b, opacity);
          p.strokeWeight(strokeWeight);
          p.noFill();
          
          p.beginShape();
          
          for (let i = 0; i <= numPoints; i++) {
            const angle = (i / numPoints) * p.TWO_PI;
            const freqIndex = p.floor((i / numPoints) * frequencyData.length);
            const amplitude = frequencyData[freqIndex] || 0;
            
            // Ring-specific modulation
            const ringMod = 1 + p.sin(time * 2 + ring * 0.7) * 0.08;
            const radiusVar = amplitude * 35 * ringMod;
            
            // Add some ring-specific phase offset
            const phaseOffset = ring * 0.3;
            const finalRadius = baseRadius + radiusVar + 
                               p.sin(time * 1.5 + angle * 3 + phaseOffset) * 2;
            
            const x = centerX + p.cos(angle) * finalRadius;
            const y = centerY + p.sin(angle) * finalRadius;
            
            if (i === 0) {
              p.vertex(x, y);
            } else {
              p.curveVertex(x, y);
            }
          }
          
          p.endShape(p.CLOSE);
        }
      };

      const drawCenterElement = (p) => {
        const pulseSize = 6 + getBaseAmplitude() * 10;
        const glowSize = pulseSize * 3;
        
        // Outer glow
        for (let r = glowSize; r > 0; r--) {
          const alpha = p.map(r, 0, glowSize, 120, 0);
          p.fill(255, alpha);
          p.noStroke();
          p.ellipse(centerX, centerY, r * 2);
        }
        
        // Center dot
        p.fill(255, 200 + getBaseAmplitude() * 55);
        p.noStroke();
        p.ellipse(centerX, centerY, pulseSize * 2);
        
        // Inner core
        p.fill(255);
        p.ellipse(centerX, centerY, pulseSize);
      };

      const drawStateIndicator = (p) => {
        const indicatorY = centerY + maxRadius + 30;
        
        // State text
        p.fill(255, 180);
        p.textAlign(p.CENTER, p.CENTER);
        p.textSize(12);
        p.textFont('monospace');
        
        const stateTexts = {
          idle: '◦ READY',
          listening: '● LISTENING',
          thinking: '◐ PROCESSING',
          speaking: '◑ RESPONDING'
        };
        
        p.text(stateTexts[agentState] || '◦ READY', centerX, indicatorY);
        
        // Mini frequency display
        const barWidth = 1.5;
        const barSpacing = 2.5;
        const numBars = 24;
        const barsWidth = numBars * (barWidth + barSpacing);
        const startX = centerX - barsWidth / 2;
        
        for (let i = 0; i < numBars; i++) {
          const freqIndex = p.floor((i / numBars) * frequencyData.length);
          const amplitude = frequencyData[freqIndex] || 0;
          const barHeight = amplitude * 12;
          
          const x = startX + i * (barWidth + barSpacing);
          const y = indicatorY + 15;
          
          p.fill(255, 80 + amplitude * 120);
          p.noStroke();
          p.rect(x, y - barHeight, barWidth, barHeight);
        }
      };

      // Update function for external state changes
      p.updateState = (newState, newAudioLevel) => {
        agentState = newState;
        audioLevel = newAudioLevel;
      };
    };

    p5Instance.current = new p5(sketch, containerRef.current);

    return () => {
      if (p5Instance.current) {
        p5Instance.current.remove();
      }
    };
  }, [width, height]);

  // Update state when props change
  useEffect(() => {
    if (p5Instance.current && p5Instance.current.updateState) {
      p5Instance.current.updateState(agentState, audioLevel);
    }
  }, [agentState, audioLevel]);

  return (
    <div 
      ref={containerRef} 
      className={`joy-division-visualizer ${className}`}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        backgroundColor: '#0a0a0f',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
    />
  );
};

export default JoyDivisionVisualizer;
