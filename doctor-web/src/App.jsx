import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';
import Timeline from './components/Timeline';
import ChatPane from './components/ChatPane';
import JoyDivisionVisualizer from './components/JoyDivisionVisualizer';

const SERVER_URL = 'http://localhost:3000';

function App() {
  const [messages, setMessages] = useState([]);
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [temporalContext, setTemporalContext] = useState(null);
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [agentState, setAgentState] = useState('idle');
  const [audioLevel, setAudioLevel] = useState(0);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setConnectionStatus('connected');
      console.log('Connected to server');
    });

    newSocket.on('disconnect', () => {
      setConnectionStatus('disconnected');
      console.log('Disconnected from server');
    });

    newSocket.on('newMessage', (data) => {
      if (data.message) {
        setMessages(prev => [...prev, data.message]);

        // Update agent state based on message type
        if (data.message.from === 'ai_companion') {
          setAgentState('speaking');
          setTimeout(() => setAgentState('idle'), 3000);
        } else if (data.message.from === 'patient') {
          setAgentState('thinking');
          setAudioLevel(Math.random() * 0.8 + 0.2); // Simulate audio activity
        }
      }
      if (data.temporalLayer) {
        console.log('Temporal layer received:', data.temporalLayer);
      }
      if (data.doctorSummary) {
        console.log('Doctor summary:', data.doctorSummary);
        // You could show notifications here for urgent cases
      }
    });

    newSocket.on('safetyAlert', (alert) => {
      console.log('Safety alert:', alert);
    });

    // Load initial messages
    fetch(`${SERVER_URL}/messages`)
      .then(res => res.json())
      .then(data => setMessages(data))
      .catch(err => console.error('Failed to load messages:', err));

    return () => newSocket.close();
  }, []);

  const sendMessage = async (text) => {
    try {
      const response = await fetch(`${SERVER_URL}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'doctor',
          subtype: 'text',
          payload: { text }
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 flex">
      {/* Enhanced Header with Medical Gradient */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 shadow-lg z-10 p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
              <span className="text-2xl">🏥</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white tracking-tight">Symptom-OS</h1>
              <p className="text-blue-100 text-sm font-medium">Intelligent Medical Assistant Dashboard</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className={`px-4 py-2 rounded-full text-sm font-semibold shadow-lg backdrop-blur-sm ${
              connectionStatus === 'connected' 
                ? 'bg-green-500/20 text-green-100 border border-green-400/30' 
                : 'bg-red-500/20 text-red-100 border border-red-400/30'
            }`}>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  connectionStatus === 'connected' ? 'bg-green-400' : 'bg-red-400'
                } animate-pulse`}></div>
                <span className="capitalize">{connectionStatus}</span>
              </div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 border border-white/30">
              <div className="flex items-center space-x-2 text-white">
                <span className="text-lg">💬</span>
                <span className="font-semibold">{messages.length}</span>
                <span className="text-blue-100 text-sm">messages</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="flex-1 pt-24 flex">
        {/* Timeline Section with Glass Effect */}
        <div className={`transition-all duration-500 ${isChatCollapsed ? 'w-full' : 'w-2/3'} p-6`}>
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 h-full">
            <div className="p-6 border-b border-gray-200/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white text-lg">⏱️</span>
                  </div>
                  <div>
                    <h2 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                      Patient Timeline
                    </h2>
                    <p className="text-sm text-gray-600">Chronological medical interactions</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  {/* AI Agent Visualizer */}
                  <div className="flex items-center space-x-3">
                    <JoyDivisionVisualizer
                      agentState={agentState}
                      audioLevel={audioLevel}
                      width={120}
                      height={80}
                      className="rounded-lg"
                    />
                    <div className="text-xs text-gray-500">
                      <div className="font-medium">AI Agent</div>
                      <div className="capitalize">{agentState}</div>
                    </div>
                  </div>

                  {searchTerm && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg px-3 py-1">
                      <span className="text-sm text-blue-700 font-medium">
                        Searching: "{searchTerm}"
                      </span>
                    </div>
                  )}
                  <button
                    onClick={() => setIsChatCollapsed(!isChatCollapsed)}
                    className="p-3 text-gray-500 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg"
                    title={isChatCollapsed ? "Show Chat" : "Hide Chat"}
                  >
                    <svg className={`w-5 h-5 transition-transform ${isChatCollapsed ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div className="h-full overflow-hidden">
              <Timeline
                messages={messages}
                temporalContext={temporalContext}
                onSearchChange={setSearchTerm}
              />
            </div>
          </div>
        </div>

        {/* Enhanced Chat Section */}
        <div className={`transition-all duration-500 ${isChatCollapsed ? 'w-0' : 'w-1/3'} ${isChatCollapsed ? 'p-0' : 'p-6 pl-0'}`}>
          <div className="bg-white/70 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 h-full">
            <ChatPane
              messages={messages}
              onSendMessage={sendMessage}
              onCollapse={() => setIsChatCollapsed(!isChatCollapsed)}
              isCollapsed={isChatCollapsed}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
