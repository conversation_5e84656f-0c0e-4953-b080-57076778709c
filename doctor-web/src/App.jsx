import React, { useState, useEffect } from 'react';
import io from 'socket.io-client';
import Timeline from './components/Timeline';
import ChatPane from './components/ChatPane';

const SERVER_URL = 'http://localhost:3000';

function App() {
  const [messages, setMessages] = useState([]);
  const [socket, setSocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [temporalContext, setTemporalContext] = useState(null);
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(SERVER_URL);
    setSocket(newSocket);

    newSocket.on('connect', () => {
      setConnectionStatus('connected');
      console.log('Connected to server');
    });

    newSocket.on('disconnect', () => {
      setConnectionStatus('disconnected');
      console.log('Disconnected from server');
    });

    newSocket.on('newMessage', (data) => {
      if (data.message) {
        setMessages(prev => [...prev, data.message]);
      }
      if (data.temporalLayer) {
        console.log('Temporal layer received:', data.temporalLayer);
      }
      if (data.doctorSummary) {
        console.log('Doctor summary:', data.doctorSummary);
        // You could show notifications here for urgent cases
      }
    });

    newSocket.on('safetyAlert', (alert) => {
      console.log('Safety alert:', alert);
    });

    // Load initial messages
    fetch(`${SERVER_URL}/messages`)
      .then(res => res.json())
      .then(data => setMessages(data))
      .catch(err => console.error('Failed to load messages:', err));

    return () => newSocket.close();
  }, []);

  const sendMessage = async (text) => {
    try {
      const response = await fetch(`${SERVER_URL}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'doctor',
          subtype: 'text',
          payload: { text }
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="h-screen bg-gray-100 flex">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 bg-white shadow-sm z-10 p-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-800">Symptom-OS Doctor Dashboard</h1>
          <div className="flex items-center space-x-4">
            <div className={`px-3 py-1 rounded-full text-sm ${
              connectionStatus === 'connected' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {connectionStatus}
            </div>
            <div className="text-sm text-gray-600">
              {messages.length} messages
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 pt-20 flex">
        {/* Timeline Section */}
        <div className={`transition-all duration-300 ${isChatCollapsed ? 'w-full' : 'w-2/3'} p-6`}>
          <div className="bg-white rounded-lg shadow-sm h-full">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">Patient Timeline</h2>
                <div className="flex items-center space-x-3">
                  {searchTerm && (
                    <span className="text-sm text-blue-600">
                      Searching: "{searchTerm}"
                    </span>
                  )}
                  <button
                    onClick={() => setIsChatCollapsed(!isChatCollapsed)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    title={isChatCollapsed ? "Show Chat" : "Hide Chat"}
                  >
                    <svg className={`w-5 h-5 transition-transform ${isChatCollapsed ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
            <div className="h-full overflow-hidden">
              <Timeline
                messages={messages}
                temporalContext={temporalContext}
                onSearchChange={setSearchTerm}
              />
            </div>
          </div>
        </div>

        {/* Chat Section */}
        <div className={`transition-all duration-300 ${isChatCollapsed ? 'w-0' : 'w-1/3'} ${isChatCollapsed ? 'p-0' : 'p-6 pl-0'}`}>
          <div className="bg-white rounded-lg shadow-sm h-full">
            <ChatPane
              messages={messages}
              onSendMessage={sendMessage}
              onCollapse={() => setIsChatCollapsed(!isChatCollapsed)}
              isCollapsed={isChatCollapsed}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
