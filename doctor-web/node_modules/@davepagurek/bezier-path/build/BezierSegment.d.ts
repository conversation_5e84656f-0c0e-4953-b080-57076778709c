import type { Point } from "./types";
export declare class BezierSegment {
    A: Point;
    B: Point;
    C: Point;
    D: Point;
    constructor(A: Point, B: Point, C: Point, D: Point);
    static sampleSpacing(): number;
    tangentAtParameter(parameter: number): {
        x: number;
        y: number;
    };
    isLinear(): boolean;
    pointAtParameter(parameter: number): {
        x: number;
        y: number;
    };
    private _totalLength;
    getTotalLength(): number;
}
