import { d as files } from '../rendering-CvUVN-Vb.js';
import table from './p5.Table.js';
import tableRow from './p5.TableRow.js';
import xml from './p5.XML.js';
import '../constants-BRcElHU3.js';
import '../creating_reading-Cr8L2Jnm.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../dom/p5.Element.js';
import '../dom/p5.File.js';
import '../p5.Renderer-R23xoC7s.js';
import '../image/filters.js';
import '../math/p5.Vector.js';
import '../shape/custom_shapes.js';
import '../core/States.js';
import './utilities.js';
import 'file-saver';
import '../dom/p5.MediaElement.js';
import '../shape/2d_primitives.js';
import '../core/helpers.js';
import '../shape/attributes.js';
import '../shape/curves.js';
import '../shape/vertex.js';
import '../color/setting.js';
import 'omggif';
import './csv.js';
import 'gifenc';
import '../image/pixels.js';
import '../core/transform.js';
import '../webgl/GeometryBuilder.js';
import '../math/p5.Matrix.js';
import '../math/Matrices/Matrix.js';
import '../math/Matrices/MatrixInterface.js';
import '../webgl/p5.Geometry.js';
import '../webgl/p5.DataArray.js';
import '../webgl/p5.Quat.js';
import '../webgl/p5.RenderBuffer.js';
import '../webgl/ShapeBuilder.js';
import 'libtess';
import '../webgl/GeometryBufferCache.js';
import '../image/const.js';
import '../math/trigonometry.js';

function io(p5){
  p5.registerAddon(files);
  p5.registerAddon(table);
  p5.registerAddon(tableRow);
  p5.registerAddon(xml);
}

export { io as default };
