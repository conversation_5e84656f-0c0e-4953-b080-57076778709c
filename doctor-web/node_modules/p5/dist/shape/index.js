import primitives from './2d_primitives.js';
import attributes from './attributes.js';
import curves from './curves.js';
import vertex from './vertex.js';
import customShapes from './custom_shapes.js';
import '../constants-BRcElHU3.js';
import '../core/helpers.js';
import '../creating_reading-Cr8L2Jnm.js';
import 'colorjs.io/fn';
import '../color/color_spaces/hsb.js';
import '../math/p5.Vector.js';

function shape(p5){
  p5.registerAddon(primitives);
  p5.registerAddon(attributes);
  p5.registerAddon(curves);
  p5.registerAddon(vertex);
  p5.registerAddon(customShapes);
}

export { shape as default };
