{"name": "p5", "repository": "processing/p5.js", "scripts": {"build": "rollup -c", "dev": "vite preview/", "dev:global": "concurrently -n build,server \"rollup -c -w\" \"npx vite preview/global/\"", "docs": "documentation build ./src/**/*.js ./src/**/**/*.js -o ./docs/data.json && node ./utils/convert.mjs", "bench": "vitest bench", "bench:report": "vitest bench --reporter=verbose", "test": "vitest", "lint": "eslint .", "lint:fix": "eslint --fix .", "generate-types": "npm run docs && node utils/generate-types.mjs && node utils/patch.mjs"}, "lint-staged": {"Gruntfile.js": "eslint", "docs/preprocessor.js": "eslint", "utils/**/*.js": "eslint", "tasks/**/*.js": "eslint", "src/**/*.js": ["eslint", "node ./utils/sample-linter.mjs"]}, "version": "2.0.3", "dependencies": {"@davepagurek/bezier-path": "^0.0.2", "@japont/unicode-range": "^1.0.0", "acorn": "^8.12.1", "acorn-walk": "^8.3.4", "colorjs.io": "^0.5.2", "escodegen": "^2.1.0", "file-saver": "^1.3.8", "gifenc": "^1.0.3", "i18next": "^19.0.2", "i18next-browser-languagedetector": "^4.0.1", "libtess": "^1.2.2", "omggif": "^1.0.10", "pako": "^2.1.0", "pixelmatch": "^7.1.0", "zod": "^3.23.8"}, "devDependencies": {"@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.4", "@vitest/browser": "^2.1.5", "all-contributors-cli": "^6.19.0", "concurrently": "^8.2.2", "documentation": "^14.0.3", "eslint": "^8.54.0", "glob": "^11.0.1", "husky": "^4.2.3", "lint-staged": "^15.1.0", "msw": "^2.6.3", "rollup": "^4.9.6", "rollup-plugin-string": "^3.0.0", "rollup-plugin-visualizer": "^5.12.0", "vite": "^5.0.2", "vite-plugin-string": "^1.2.2", "vitest": "^2.1.5", "webdriverio": "^9.0.7"}, "license": "LGPL-2.1", "browser": "./lib/p5.min.js", "exports": {".": "./dist/app.js", "./core": "./dist/core/main.js", "./shape": "./dist/shape/index.js", "./accessibility": "./dist/accessibility/index.js", "./friendlyErrors": "./dist/core/friendlyErrors/index.js", "./data": "./dist/data/index.js", "./dom": "./dist/dom/index.js", "./events": "./dist/events/index.js", "./image": "./dist/image/index.js", "./io": "./dist/io/index.js", "./math": "./dist/math/index.js", "./utilities": "./dist/utilities/index.js", "./webgl": "./dist/webgl/index.js", "./type": "./dist/type/index.js"}, "files": ["dist/**", "license.txt", "lib/p5.min.js", "lib/p5.js", "lib/p5.esm.js", "translations/**", "types/**"], "description": "[![npm version](https://badge.fury.io/js/p5.svg)](https://www.npmjs.com/package/p5)", "bugs": {"url": "https://github.com/processing/p5.js/issues"}, "homepage": "https://p5js.org", "directories": {"doc": "docs", "test": "test"}, "author": "", "husky": {"hooks": {}}, "msw": {"workerDirectory": ["test"]}, "types": "./types/p5.d.ts"}