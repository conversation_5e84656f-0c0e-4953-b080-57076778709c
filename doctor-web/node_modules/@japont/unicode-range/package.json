{"name": "@japont/unicode-range", "description": "Unicode-range parser/builder.", "license": "MIT", "author": "3846masa <3846mas<PERSON><EMAIL>>", "repository": "**************:Japont/unicode-range.git", "version": "1.0.0", "main": "lib/index.js", "files": ["lib"], "scripts": {"build": "tsc", "test": "nyc ava"}, "dependencies": {}, "devDependencies": {"ava": "^1.0.0-beta.6", "nyc": "^12.0.2", "source-map-support": "^0.5.6", "ts-node": "^7.0.0", "typescript": "^2.9.2"}, "keywords": ["css", "font-face", "unicode", "unicode-range"], "ava": {"compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register", "source-map-support/register"]}, "nyc": {"all": true, "include": ["src/**/*.ts"], "reporter": ["html", "json", "text", "text-summary"], "extension": [".ts"]}, "types": "lib/index.d.ts"}