{"name": "symptom-os-server", "version": "1.0.0", "description": "Backend server for Symptom-OS MVP", "main": "index.js", "scripts": {"start": "nodemon --ignore demo_store.json index.js", "dev": "nodemon --ignore demo_store.json index.js"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^4.18.2", "lowdb": "^6.1.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.5", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}