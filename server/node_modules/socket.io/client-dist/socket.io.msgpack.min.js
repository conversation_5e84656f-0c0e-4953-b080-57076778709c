/*!
 * Socket.IO v4.8.1
 * (c) 2014-2024 <PERSON>
 * Released under the MIT License.
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(t="undefined"!=typeof globalThis?globalThis:t||self).io=i()}(this,(function(){"use strict";function t(t,i){return i.forEach((function(i){i&&"string"!=typeof i&&!Array.isArray(i)&&Object.keys(i).forEach((function(n){if("default"!==n&&!(n in t)){var r=Object.getOwnPropertyDescriptor(i,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return i[n]}})}}))})),Object.freeze(t)}function i(t,i){(null==i||i>t.length)&&(i=t.length);for(var n=0,r=Array(i);n<i;n++)r[n]=t[n];return r}function n(t,i){for(var n=0;n<i.length;n++){var r=i[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,c(r.key),r)}}function r(t,i,r){return i&&n(t.prototype,i),r&&n(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function e(t,n){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,n){if(t){if("string"==typeof t)return i(t,n);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,n):void 0}}(t))||n&&t&&"number"==typeof t.length){r&&(t=r);var e=0,s=function(){};return{s:s,n:function(){return e>=t.length?{done:!0}:{done:!1,value:t[e++]}},e:function(t){throw t},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,h=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return h=t.done,t},e:function(t){u=!0,o=t},f:function(){try{h||null==r.return||r.return()}finally{if(u)throw o}}}}function s(){return s=Object.assign?Object.assign.bind():function(t){for(var i=1;i<arguments.length;i++){var n=arguments[i];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},s.apply(null,arguments)}function o(t){return o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},o(t)}function h(t,i){t.prototype=Object.create(i.prototype),t.prototype.constructor=t,f(t,i)}function u(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(u=function(){return!!t})()}function f(t,i){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,i){return t.__proto__=i,t},f(t,i)}function c(t){var i=function(t,i){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,i||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}(t,"string");return"symbol"==typeof i?i:i+""}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function v(t){var i="function"==typeof Map?new Map:void 0;return v=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch(i){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==i){if(i.has(t))return i.get(t);i.set(t,n)}function n(){return function(t,i,n){if(u())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,i);var e=new(t.bind.apply(t,r));return n&&f(e,n.prototype),e}(t,arguments,o(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),f(n,t)},v(t)}var l=Object.create(null);l.open="0",l.close="1",l.ping="2",l.pong="3",l.message="4",l.upgrade="5",l.noop="6";var d=Object.create(null);Object.keys(l).forEach((function(t){d[l[t]]=t}));var p,y={type:"error",data:"parser error"},w="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),b="function"==typeof ArrayBuffer,g=function(t){return"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer},m=function(t,i,n){var r=t.type,e=t.data;return w&&e instanceof Blob?i?n(e):k(e,n):b&&(e instanceof ArrayBuffer||g(e))?i?n(e):k(new Blob([e]),n):n(l[r]+(e||""))},k=function(t,i){var n=new FileReader;return n.onload=function(){var t=n.result.split(",")[1];i("b"+(t||""))},n.readAsDataURL(t)};function A(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}for(var E="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j="undefined"==typeof Uint8Array?[]:new Uint8Array(256),O=0;O<64;O++)j[E.charCodeAt(O)]=O;var M,S="function"==typeof ArrayBuffer,B=function(t,i){if("string"!=typeof t)return{type:"message",data:T(t,i)};var n=t.charAt(0);return"b"===n?{type:"message",data:C(t.substring(1),i)}:d[n]?t.length>1?{type:d[n],data:t.substring(1)}:{type:d[n]}:y},C=function(t,i){if(S){var n=function(t){var i,n,r,e,s,o=.75*t.length,h=t.length,u=0;"="===t[t.length-1]&&(o--,"="===t[t.length-2]&&o--);var f=new ArrayBuffer(o),c=new Uint8Array(f);for(i=0;i<h;i+=4)n=j[t.charCodeAt(i)],r=j[t.charCodeAt(i+1)],e=j[t.charCodeAt(i+2)],s=j[t.charCodeAt(i+3)],c[u++]=n<<2|r>>4,c[u++]=(15&r)<<4|e>>2,c[u++]=(3&e)<<6|63&s;return f}(t);return T(n,i)}return{base64:!0,data:t}},T=function(t,i){return"blob"===i?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer},U=String.fromCharCode(30);function _(){return new TransformStream({transform:function(t,i){!function(t,i){w&&t.data instanceof Blob?t.data.arrayBuffer().then(A).then(i):b&&(t.data instanceof ArrayBuffer||g(t.data))?i(A(t.data)):m(t,!1,(function(t){p||(p=new TextEncoder),i(p.encode(t))}))}(t,(function(n){var r,e=n.length;if(e<126)r=new Uint8Array(1),new DataView(r.buffer).setUint8(0,e);else if(e<65536){r=new Uint8Array(3);var s=new DataView(r.buffer);s.setUint8(0,126),s.setUint16(1,e)}else{r=new Uint8Array(9);var o=new DataView(r.buffer);o.setUint8(0,127),o.setBigUint64(1,BigInt(e))}t.data&&"string"!=typeof t.data&&(r[0]|=128),i.enqueue(r),i.enqueue(n)}))}})}function x(t){return t.reduce((function(t,i){return t+i.length}),0)}function D(t,i){if(t[0].length===i)return t.shift();for(var n=new Uint8Array(i),r=0,e=0;e<i;e++)n[e]=t[0][r++],r===t[0].length&&(t.shift(),r=0);return t.length&&r<t[0].length&&(t[0]=t[0].slice(r)),n}function $(t){if(t)return function(t){for(var i in $.prototype)t[i]=$.prototype[i];return t}(t)}$.prototype.on=$.prototype.addEventListener=function(t,i){return this.t=this.t||{},(this.t["$"+t]=this.t["$"+t]||[]).push(i),this},$.prototype.once=function(t,i){function n(){this.off(t,n),i.apply(this,arguments)}return n.fn=i,this.on(t,n),this},$.prototype.off=$.prototype.removeListener=$.prototype.removeAllListeners=$.prototype.removeEventListener=function(t,i){if(this.t=this.t||{},0==arguments.length)return this.t={},this;var n,r=this.t["$"+t];if(!r)return this;if(1==arguments.length)return delete this.t["$"+t],this;for(var e=0;e<r.length;e++)if((n=r[e])===i||n.fn===i){r.splice(e,1);break}return 0===r.length&&delete this.t["$"+t],this},$.prototype.emit=function(t){this.t=this.t||{};for(var i=new Array(arguments.length-1),n=this.t["$"+t],r=1;r<arguments.length;r++)i[r-1]=arguments[r];if(n){r=0;for(var e=(n=n.slice(0)).length;r<e;++r)n[r].apply(this,i)}return this},$.prototype.emitReserved=$.prototype.emit,$.prototype.listeners=function(t){return this.t=this.t||{},this.t["$"+t]||[]},$.prototype.hasListeners=function(t){return!!this.listeners(t).length};var I="function"==typeof Promise&&"function"==typeof Promise.resolve?function(t){return Promise.resolve().then(t)}:function(t,i){return i(t,0)},R="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function L(t){for(var i=arguments.length,n=new Array(i>1?i-1:0),r=1;r<i;r++)n[r-1]=arguments[r];return n.reduce((function(i,n){return t.hasOwnProperty(n)&&(i[n]=t[n]),i}),{})}var N=R.setTimeout,P=R.clearTimeout;function V(t,i){i.useNativeTimers?(t.setTimeoutFn=N.bind(R),t.clearTimeoutFn=P.bind(R)):(t.setTimeoutFn=R.setTimeout.bind(R),t.clearTimeoutFn=R.clearTimeout.bind(R))}function q(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var F=function(t){function i(i,n,r){var e;return(e=t.call(this,i)||this).description=n,e.context=r,e.type="TransportError",e}return h(i,t),i}(v(Error)),X=function(t){function i(i){var n;return(n=t.call(this)||this).writable=!1,V(n,i),n.opts=i,n.query=i.query,n.socket=i.socket,n.supportsBinary=!i.forceBase64,n}h(i,t);var n=i.prototype;return n.onError=function(i,n,r){return t.prototype.emitReserved.call(this,"error",new F(i,n,r)),this},n.open=function(){return this.readyState="opening",this.doOpen(),this},n.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},n.send=function(t){"open"===this.readyState&&this.write(t)},n.onOpen=function(){this.readyState="open",this.writable=!0,t.prototype.emitReserved.call(this,"open")},n.onData=function(t){var i=B(t,this.socket.binaryType);this.onPacket(i)},n.onPacket=function(i){t.prototype.emitReserved.call(this,"packet",i)},n.onClose=function(i){this.readyState="closed",t.prototype.emitReserved.call(this,"close",i)},n.pause=function(t){},n.createUri=function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t+"://"+this.i()+this.o()+this.opts.path+this.h(i)},n.i=function(){var t=this.opts.hostname;return-1===t.indexOf(":")?t:"["+t+"]"},n.o=function(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""},n.h=function(t){var i=function(t){var i="";for(var n in t)t.hasOwnProperty(n)&&(i.length&&(i+="&"),i+=encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return i}(t);return i.length?"?"+i:""},i}($),H=function(t){function i(){var i;return(i=t.apply(this,arguments)||this).u=!1,i}h(i,t);var n=i.prototype;return n.doOpen=function(){this.v()},n.pause=function(t){var i=this;this.readyState="pausing";var n=function(){i.readyState="paused",t()};if(this.u||!this.writable){var r=0;this.u&&(r++,this.once("pollComplete",(function(){--r||n()}))),this.writable||(r++,this.once("drain",(function(){--r||n()})))}else n()},n.v=function(){this.u=!0,this.doPoll(),this.emitReserved("poll")},n.onData=function(t){var i=this;(function(t,i){for(var n=t.split(U),r=[],e=0;e<n.length;e++){var s=B(n[e],i);if(r.push(s),"error"===s.type)break}return r})(t,this.socket.binaryType).forEach((function(t){if("opening"===i.readyState&&"open"===t.type&&i.onOpen(),"close"===t.type)return i.onClose({description:"transport closed by the server"}),!1;i.onPacket(t)})),"closed"!==this.readyState&&(this.u=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this.v())},n.doClose=function(){var t=this,i=function(){t.write([{type:"close"}])};"open"===this.readyState?i():this.once("open",i)},n.write=function(t){var i=this;this.writable=!1,function(t,i){var n=t.length,r=new Array(n),e=0;t.forEach((function(t,s){m(t,!1,(function(t){r[s]=t,++e===n&&i(r.join(U))}))}))}(t,(function(t){i.doWrite(t,(function(){i.writable=!0,i.emitReserved("drain")}))}))},n.uri=function(){var t=this.opts.secure?"https":"http",i=this.query||{};return!1!==this.opts.timestampRequests&&(i[this.opts.timestampParam]=q()),this.supportsBinary||i.sid||(i.b64=1),this.createUri(t,i)},r(i,[{key:"name",get:function(){return"polling"}}])}(X),z=!1;try{z="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}var G=z;function J(){}var K=function(t){function i(i){var n;if(n=t.call(this,i)||this,"undefined"!=typeof location){var r="https:"===location.protocol,e=location.port;e||(e=r?"443":"80"),n.xd="undefined"!=typeof location&&i.hostname!==location.hostname||e!==i.port}return n}h(i,t);var n=i.prototype;return n.doWrite=function(t,i){var n=this,r=this.request({method:"POST",data:t});r.on("success",i),r.on("error",(function(t,i){n.onError("xhr post error",t,i)}))},n.doPoll=function(){var t=this,i=this.request();i.on("data",this.onData.bind(this)),i.on("error",(function(i,n){t.onError("xhr poll error",i,n)})),this.pollXhr=i},i}(H),Q=function(t){function i(i,n,r){var e;return(e=t.call(this)||this).createRequest=i,V(e,r),e.l=r,e.p=r.method||"GET",e.m=n,e.k=void 0!==r.data?r.data:null,e.A(),e}h(i,t);var n=i.prototype;return n.A=function(){var t,n=this,r=L(this.l,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this.l.xd;var e=this.j=this.createRequest(r);try{e.open(this.p,this.m,!0);try{if(this.l.extraHeaders)for(var s in e.setDisableHeaderCheck&&e.setDisableHeaderCheck(!0),this.l.extraHeaders)this.l.extraHeaders.hasOwnProperty(s)&&e.setRequestHeader(s,this.l.extraHeaders[s])}catch(t){}if("POST"===this.p)try{e.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{e.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this.l.cookieJar)||void 0===t||t.addCookies(e),"withCredentials"in e&&(e.withCredentials=this.l.withCredentials),this.l.requestTimeout&&(e.timeout=this.l.requestTimeout),e.onreadystatechange=function(){var t;3===e.readyState&&(null===(t=n.l.cookieJar)||void 0===t||t.parseCookies(e.getResponseHeader("set-cookie"))),4===e.readyState&&(200===e.status||1223===e.status?n.O():n.setTimeoutFn((function(){n.M("number"==typeof e.status?e.status:0)}),0))},e.send(this.k)}catch(t){return void this.setTimeoutFn((function(){n.M(t)}),0)}"undefined"!=typeof document&&(this.S=i.requestsCount++,i.requests[this.S]=this)},n.M=function(t){this.emitReserved("error",t,this.j),this.B(!0)},n.B=function(t){if(void 0!==this.j&&null!==this.j){if(this.j.onreadystatechange=J,t)try{this.j.abort()}catch(t){}"undefined"!=typeof document&&delete i.requests[this.S],this.j=null}},n.O=function(){var t=this.j.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this.B())},n.abort=function(){this.B()},i}($);if(Q.requestsCount=0,Q.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",W);else if("function"==typeof addEventListener){addEventListener("onpagehide"in R?"pagehide":"unload",W,!1)}function W(){for(var t in Q.requests)Q.requests.hasOwnProperty(t)&&Q.requests[t].abort()}var Y,Z=(Y=it({xdomain:!1}))&&null!==Y.responseType,tt=function(t){function i(i){var n;n=t.call(this,i)||this;var r=i&&i.forceBase64;return n.supportsBinary=Z&&!r,n}return h(i,t),i.prototype.request=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return s(t,{xd:this.xd},this.opts),new Q(it,this.uri(),t)},i}(K);function it(t){var i=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!i||G))return new XMLHttpRequest}catch(t){}if(!i)try{return new(R[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(t){}}var nt="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase(),rt=function(t){function i(){return t.apply(this,arguments)||this}h(i,t);var n=i.prototype;return n.doOpen=function(){var t=this.uri(),i=this.opts.protocols,n=nt?{}:L(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,i,n)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()},n.addEventListeners=function(){var t=this;this.ws.onopen=function(){t.opts.autoUnref&&t.ws.C.unref(),t.onOpen()},this.ws.onclose=function(i){return t.onClose({description:"websocket connection closed",context:i})},this.ws.onmessage=function(i){return t.onData(i.data)},this.ws.onerror=function(i){return t.onError("websocket error",i)}},n.write=function(t){var i=this;this.writable=!1;for(var n=function(){var n=t[r],e=r===t.length-1;m(n,i.supportsBinary,(function(t){try{i.doWrite(n,t)}catch(t){}e&&I((function(){i.writable=!0,i.emitReserved("drain")}),i.setTimeoutFn)}))},r=0;r<t.length;r++)n()},n.doClose=function(){void 0!==this.ws&&(this.ws.onerror=function(){},this.ws.close(),this.ws=null)},n.uri=function(){var t=this.opts.secure?"wss":"ws",i=this.query||{};return this.opts.timestampRequests&&(i[this.opts.timestampParam]=q()),this.supportsBinary||(i.b64=1),this.createUri(t,i)},r(i,[{key:"name",get:function(){return"websocket"}}])}(X),et=R.WebSocket||R.MozWebSocket,st=function(t){function i(){return t.apply(this,arguments)||this}h(i,t);var n=i.prototype;return n.createSocket=function(t,i,n){return nt?new et(t,i,n):i?new et(t,i):new et(t)},n.doWrite=function(t,i){this.ws.send(i)},i}(rt),ot=function(t){function i(){return t.apply(this,arguments)||this}h(i,t);var n=i.prototype;return n.doOpen=function(){var t=this;try{this.T=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this.T.closed.then((function(){t.onClose()})).catch((function(i){t.onError("webtransport error",i)})),this.T.ready.then((function(){t.T.createBidirectionalStream().then((function(i){var n=function(t,i){M||(M=new TextDecoder);var n=[],r=0,e=-1,s=!1;return new TransformStream({transform:function(o,h){for(n.push(o);;){if(0===r){if(x(n)<1)break;var u=D(n,1);s=!(128&~u[0]),e=127&u[0],r=e<126?3:126===e?1:2}else if(1===r){if(x(n)<2)break;var f=D(n,2);e=new DataView(f.buffer,f.byteOffset,f.length).getUint16(0),r=3}else if(2===r){if(x(n)<8)break;var c=D(n,8),a=new DataView(c.buffer,c.byteOffset,c.length),v=a.getUint32(0);if(v>Math.pow(2,21)-1){h.enqueue(y);break}e=v*Math.pow(2,32)+a.getUint32(4),r=3}else{if(x(n)<e)break;var l=D(n,e);h.enqueue(B(s?l:M.decode(l),i)),r=0}if(0===e||e>t){h.enqueue(y);break}}}})}(Number.MAX_SAFE_INTEGER,t.socket.binaryType),r=i.readable.pipeThrough(n).getReader(),e=_();e.readable.pipeTo(i.writable),t.U=e.writable.getWriter();!function i(){r.read().then((function(n){var r=n.done,e=n.value;r||(t.onPacket(e),i())})).catch((function(t){}))}();var s={type:"open"};t.query.sid&&(s.data='{"sid":"'.concat(t.query.sid,'"}')),t.U.write(s).then((function(){return t.onOpen()}))}))}))},n.write=function(t){var i=this;this.writable=!1;for(var n=function(){var n=t[r],e=r===t.length-1;i.U.write(n).then((function(){e&&I((function(){i.writable=!0,i.emitReserved("drain")}),i.setTimeoutFn)}))},r=0;r<t.length;r++)n()},n.doClose=function(){var t;null===(t=this.T)||void 0===t||t.close()},r(i,[{key:"name",get:function(){return"webtransport"}}])}(X),ht={websocket:st,webtransport:ot,polling:tt},ut=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,ft=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ct(t){if(t.length>8e3)throw"URI too long";var i=t,n=t.indexOf("["),r=t.indexOf("]");-1!=n&&-1!=r&&(t=t.substring(0,n)+t.substring(n,r).replace(/:/g,";")+t.substring(r,t.length));for(var e,s,o=ut.exec(t||""),h={},u=14;u--;)h[ft[u]]=o[u]||"";return-1!=n&&-1!=r&&(h.source=i,h.host=h.host.substring(1,h.host.length-1).replace(/;/g,":"),h.authority=h.authority.replace("[","").replace("]","").replace(/;/g,":"),h.ipv6uri=!0),h.pathNames=function(t,i){var n=/\/{2,9}/g,r=i.replace(n,"/").split("/");"/"!=i.slice(0,1)&&0!==i.length||r.splice(0,1);"/"==i.slice(-1)&&r.splice(r.length-1,1);return r}(0,h.path),h.queryKey=(e=h.query,s={},e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(t,i,n){i&&(s[i]=n)})),s),h}var at="function"==typeof addEventListener&&"function"==typeof removeEventListener,vt=[];at&&addEventListener("offline",(function(){vt.forEach((function(t){return t()}))}),!1);var lt=function(t){function i(i,n){var r;if((r=t.call(this)||this).binaryType="arraybuffer",r.writeBuffer=[],r._=0,r.D=-1,r.$=-1,r.I=-1,r.R=1/0,i&&"object"===a(i)&&(n=i,i=null),i){var e=ct(i);n.hostname=e.host,n.secure="https"===e.protocol||"wss"===e.protocol,n.port=e.port,e.query&&(n.query=e.query)}else n.host&&(n.hostname=ct(n.host).host);return V(r,n),r.secure=null!=n.secure?n.secure:"undefined"!=typeof location&&"https:"===location.protocol,n.hostname&&!n.port&&(n.port=r.secure?"443":"80"),r.hostname=n.hostname||("undefined"!=typeof location?location.hostname:"localhost"),r.port=n.port||("undefined"!=typeof location&&location.port?location.port:r.secure?"443":"80"),r.transports=[],r.L={},n.transports.forEach((function(t){var i=t.prototype.name;r.transports.push(i),r.L[i]=t})),r.opts=s({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),r.opts.path=r.opts.path.replace(/\/$/,"")+(r.opts.addTrailingSlash?"/":""),"string"==typeof r.opts.query&&(r.opts.query=function(t){for(var i={},n=t.split("&"),r=0,e=n.length;r<e;r++){var s=n[r].split("=");i[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return i}(r.opts.query)),at&&(r.opts.closeOnBeforeunload&&(r.N=function(){r.transport&&(r.transport.removeAllListeners(),r.transport.close())},addEventListener("beforeunload",r.N,!1)),"localhost"!==r.hostname&&(r.P=function(){r.V("transport close",{description:"network connection lost"})},vt.push(r.P))),r.opts.withCredentials&&(r.q=void 0),r.F(),r}h(i,t);var n=i.prototype;return n.createTransport=function(t){var i=s({},this.opts.query);i.EIO=4,i.transport=t,this.id&&(i.sid=this.id);var n=s({},this.opts,{query:i,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this.L[t](n)},n.F=function(){var t=this;if(0!==this.transports.length){var n=this.opts.rememberUpgrade&&i.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";var r=this.createTransport(n);r.open(),this.setTransport(r)}else this.setTimeoutFn((function(){t.emitReserved("error","No transports available")}),0)},n.setTransport=function(t){var i=this;this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this.X.bind(this)).on("packet",this.H.bind(this)).on("error",this.M.bind(this)).on("close",(function(t){return i.V("transport close",t)}))},n.onOpen=function(){this.readyState="open",i.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()},n.H=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this.G("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this.J();break;case"error":var i=new Error("server error");i.code=t.data,this.M(i);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}},n.onHandshake=function(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.D=t.pingInterval,this.$=t.pingTimeout,this.I=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this.J()},n.J=function(){var t=this;this.clearTimeoutFn(this.K);var i=this.D+this.$;this.R=Date.now()+i,this.K=this.setTimeoutFn((function(){t.V("ping timeout")}),i),this.opts.autoUnref&&this.K.unref()},n.X=function(){this.writeBuffer.splice(0,this._),this._=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()},n.flush=function(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){var t=this.W();this.transport.send(t),this._=t.length,this.emitReserved("flush")}},n.W=function(){if(!(this.I&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;for(var t,i=1,n=0;n<this.writeBuffer.length;n++){var r=this.writeBuffer[n].data;if(r&&(i+="string"==typeof(t=r)?function(t){for(var i=0,n=0,r=0,e=t.length;r<e;r++)(i=t.charCodeAt(r))<128?n+=1:i<2048?n+=2:i<55296||i>=57344?n+=3:(r++,n+=4);return n}(t):Math.ceil(1.33*(t.byteLength||t.size))),n>0&&i>this.I)return this.writeBuffer.slice(0,n);i+=2}return this.writeBuffer},n.Y=function(){var t=this;if(!this.R)return!0;var i=Date.now()>this.R;return i&&(this.R=0,I((function(){t.V("ping timeout")}),this.setTimeoutFn)),i},n.write=function(t,i,n){return this.G("message",t,i,n),this},n.send=function(t,i,n){return this.G("message",t,i,n),this},n.G=function(t,i,n,r){if("function"==typeof i&&(r=i,i=void 0),"function"==typeof n&&(r=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState){(n=n||{}).compress=!1!==n.compress;var e={type:t,data:i,options:n};this.emitReserved("packetCreate",e),this.writeBuffer.push(e),r&&this.once("flush",r),this.flush()}},n.close=function(){var t=this,i=function(){t.V("forced close"),t.transport.close()},n=function n(){t.off("upgrade",n),t.off("upgradeError",n),i()},r=function(){t.once("upgrade",n),t.once("upgradeError",n)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(function(){t.upgrading?r():i()})):this.upgrading?r():i()),this},n.M=function(t){if(i.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this.F();this.emitReserved("error",t),this.V("transport error",t)},n.V=function(t,i){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this.K),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),at&&(this.N&&removeEventListener("beforeunload",this.N,!1),this.P)){var n=vt.indexOf(this.P);-1!==n&&vt.splice(n,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,i),this.writeBuffer=[],this._=0}},i}($);lt.protocol=4;var dt=function(t){function i(){var i;return(i=t.apply(this,arguments)||this).Z=[],i}h(i,t);var n=i.prototype;return n.onOpen=function(){if(t.prototype.onOpen.call(this),"open"===this.readyState&&this.opts.upgrade)for(var i=0;i<this.Z.length;i++)this.tt(this.Z[i])},n.tt=function(t){var i=this,n=this.createTransport(t),r=!1;lt.priorWebsocketSuccess=!1;var e=function(){r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",(function(t){if(!r)if("pong"===t.type&&"probe"===t.data){if(i.upgrading=!0,i.emitReserved("upgrading",n),!n)return;lt.priorWebsocketSuccess="websocket"===n.name,i.transport.pause((function(){r||"closed"!==i.readyState&&(c(),i.setTransport(n),n.send([{type:"upgrade"}]),i.emitReserved("upgrade",n),n=null,i.upgrading=!1,i.flush())}))}else{var e=new Error("probe error");e.transport=n.name,i.emitReserved("upgradeError",e)}})))};function s(){r||(r=!0,c(),n.close(),n=null)}var o=function(t){var r=new Error("probe error: "+t);r.transport=n.name,s(),i.emitReserved("upgradeError",r)};function h(){o("transport closed")}function u(){o("socket closed")}function f(t){n&&t.name!==n.name&&s()}var c=function(){n.removeListener("open",e),n.removeListener("error",o),n.removeListener("close",h),i.off("close",u),i.off("upgrading",f)};n.once("open",e),n.once("error",o),n.once("close",h),this.once("close",u),this.once("upgrading",f),-1!==this.Z.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn((function(){r||n.open()}),200):n.open()},n.onHandshake=function(i){this.Z=this.it(i.upgrades),t.prototype.onHandshake.call(this,i)},n.it=function(t){for(var i=[],n=0;n<t.length;n++)~this.transports.indexOf(t[n])&&i.push(t[n]);return i},i}(lt),pt=function(t){function i(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="object"===a(i)?i:n;return(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map((function(t){return ht[t]})).filter((function(t){return!!t}))),t.call(this,i,r)||this}return h(i,t),i}(dt);pt.protocol;var yt={},wt={};function bt(t,i,n){for(var r=0,e=0,s=n.length;e<s;e++)(r=n.charCodeAt(e))<128?t.setUint8(i++,r):r<2048?(t.setUint8(i++,192|r>>6),t.setUint8(i++,128|63&r)):r<55296||r>=57344?(t.setUint8(i++,224|r>>12),t.setUint8(i++,128|r>>6&63),t.setUint8(i++,128|63&r)):(e++,r=65536+((1023&r)<<10|1023&n.charCodeAt(e)),t.setUint8(i++,240|r>>18),t.setUint8(i++,128|r>>12&63),t.setUint8(i++,128|r>>6&63),t.setUint8(i++,128|63&r))}function gt(t,i,n){var r=a(n),e=0,s=0,o=0,h=0,u=0,f=0;if("string"===r){if(u=function(t){for(var i=0,n=0,r=0,e=t.length;r<e;r++)(i=t.charCodeAt(r))<128?n+=1:i<2048?n+=2:i<55296||i>=57344?n+=3:(r++,n+=4);return n}(n),u<32)t.push(160|u),f=1;else if(u<256)t.push(217,u),f=2;else if(u<65536)t.push(218,u>>8,u),f=3;else{if(!(u<4294967296))throw new Error("String too long");t.push(219,u>>24,u>>16,u>>8,u),f=5}return i.push({nt:n,rt:u,et:t.length}),f+u}if("number"===r)return Math.floor(n)===n&&isFinite(n)?n>=0?n<128?(t.push(n),1):n<256?(t.push(204,n),2):n<65536?(t.push(205,n>>8,n),3):n<4294967296?(t.push(206,n>>24,n>>16,n>>8,n),5):(o=n/Math.pow(2,32)|0,h=n>>>0,t.push(207,o>>24,o>>16,o>>8,o,h>>24,h>>16,h>>8,h),9):n>=-32?(t.push(n),1):n>=-128?(t.push(208,n),2):n>=-32768?(t.push(209,n>>8,n),3):n>=-2147483648?(t.push(210,n>>24,n>>16,n>>8,n),5):(o=Math.floor(n/Math.pow(2,32)),h=n>>>0,t.push(211,o>>24,o>>16,o>>8,o,h>>24,h>>16,h>>8,h),9):(t.push(203),i.push({st:n,rt:8,et:t.length}),9);if("object"===r){if(null===n)return t.push(192),1;if(Array.isArray(n)){if((u=n.length)<16)t.push(144|u),f=1;else if(u<65536)t.push(220,u>>8,u),f=3;else{if(!(u<4294967296))throw new Error("Array too large");t.push(221,u>>24,u>>16,u>>8,u),f=5}for(e=0;e<u;e++)f+=gt(t,i,n[e]);return f}if(n instanceof Date){var c=n.getTime();return o=Math.floor(c/Math.pow(2,32)),h=c>>>0,t.push(215,0,o>>24,o>>16,o>>8,o,h>>24,h>>16,h>>8,h),10}if(n instanceof ArrayBuffer){if((u=n.byteLength)<256)t.push(196,u),f=2;else if(u<65536)t.push(197,u>>8,u),f=3;else{if(!(u<4294967296))throw new Error("Buffer too large");t.push(198,u>>24,u>>16,u>>8,u),f=5}return i.push({ot:n,rt:u,et:t.length}),f+u}if("function"==typeof n.toJSON)return gt(t,i,n.toJSON());var v=[],l="",d=Object.keys(n);for(e=0,s=d.length;e<s;e++)"function"!=typeof n[l=d[e]]&&v.push(l);if((u=v.length)<16)t.push(128|u),f=1;else if(u<65536)t.push(222,u>>8,u),f=3;else{if(!(u<4294967296))throw new Error("Object too large");t.push(223,u>>24,u>>16,u>>8,u),f=5}for(e=0;e<u;e++)f+=gt(t,i,l=v[e]),f+=gt(t,i,n[l]);return f}if("boolean"===r)return t.push(n?195:194),1;if("undefined"===r)return t.push(212,0,0),3;throw new Error("Could not encode")}var mt=function(t){var i=[],n=[],r=gt(i,n,t),e=new ArrayBuffer(r),s=new DataView(e),o=0,h=0,u=-1;n.length>0&&(u=n[0].et);for(var f,c=0,a=0,v=0,l=i.length;v<l;v++)if(s.setUint8(h+v,i[v]),v+1===u){if(c=(f=n[o]).rt,a=h+u,f.ot)for(var d=new Uint8Array(f.ot),p=0;p<c;p++)s.setUint8(a+p,d[p]);else f.nt?bt(s,a,f.nt):void 0!==f.st&&s.setFloat64(a,f.st);h+=c,n[++o]&&(u=n[o].et)}return e};function kt(t){if(this.et=0,t instanceof ArrayBuffer)this.ht=t,this.ut=new DataView(this.ht);else{if(!ArrayBuffer.isView(t))throw new Error("Invalid argument");this.ht=t.buffer,this.ut=new DataView(this.ht,t.byteOffset,t.byteLength)}}kt.prototype.ft=function(t){for(var i=new Array(t),n=0;n<t;n++)i[n]=this.ct();return i},kt.prototype.vt=function(t){for(var i={},n=0;n<t;n++)i[this.ct()]=this.ct();return i},kt.prototype.nt=function(t){var i=function(t,i,n){for(var r="",e=0,s=i,o=i+n;s<o;s++){var h=t.getUint8(s);if(128&h)if(192!=(224&h))if(224!=(240&h)){if(240!=(248&h))throw new Error("Invalid byte "+h.toString(16));(e=(7&h)<<18|(63&t.getUint8(++s))<<12|(63&t.getUint8(++s))<<6|63&t.getUint8(++s))>=65536?(e-=65536,r+=String.fromCharCode(55296+(e>>>10),56320+(1023&e))):r+=String.fromCharCode(e)}else r+=String.fromCharCode((15&h)<<12|(63&t.getUint8(++s))<<6|63&t.getUint8(++s));else r+=String.fromCharCode((31&h)<<6|63&t.getUint8(++s));else r+=String.fromCharCode(h)}return r}(this.ut,this.et,t);return this.et+=t,i},kt.prototype.ot=function(t){var i=this.ht.slice(this.et,this.et+t);return this.et+=t,i},kt.prototype.ct=function(){var t,i=this.ut.getUint8(this.et++),n=0,r=0,e=0,s=0;if(i<192)return i<128?i:i<144?this.vt(15&i):i<160?this.ft(15&i):this.nt(31&i);if(i>223)return-1*(255-i+1);switch(i){case 192:return null;case 194:return!1;case 195:return!0;case 196:return n=this.ut.getUint8(this.et),this.et+=1,this.ot(n);case 197:return n=this.ut.getUint16(this.et),this.et+=2,this.ot(n);case 198:return n=this.ut.getUint32(this.et),this.et+=4,this.ot(n);case 199:return n=this.ut.getUint8(this.et),r=this.ut.getInt8(this.et+1),this.et+=2,[r,this.ot(n)];case 200:return n=this.ut.getUint16(this.et),r=this.ut.getInt8(this.et+2),this.et+=3,[r,this.ot(n)];case 201:return n=this.ut.getUint32(this.et),r=this.ut.getInt8(this.et+4),this.et+=5,[r,this.ot(n)];case 202:return t=this.ut.getFloat32(this.et),this.et+=4,t;case 203:return t=this.ut.getFloat64(this.et),this.et+=8,t;case 204:return t=this.ut.getUint8(this.et),this.et+=1,t;case 205:return t=this.ut.getUint16(this.et),this.et+=2,t;case 206:return t=this.ut.getUint32(this.et),this.et+=4,t;case 207:return e=this.ut.getUint32(this.et)*Math.pow(2,32),s=this.ut.getUint32(this.et+4),this.et+=8,e+s;case 208:return t=this.ut.getInt8(this.et),this.et+=1,t;case 209:return t=this.ut.getInt16(this.et),this.et+=2,t;case 210:return t=this.ut.getInt32(this.et),this.et+=4,t;case 211:return e=this.ut.getInt32(this.et)*Math.pow(2,32),s=this.ut.getUint32(this.et+4),this.et+=8,e+s;case 212:return r=this.ut.getInt8(this.et),this.et+=1,0===r?void(this.et+=1):[r,this.ot(1)];case 213:return r=this.ut.getInt8(this.et),this.et+=1,[r,this.ot(2)];case 214:return r=this.ut.getInt8(this.et),this.et+=1,[r,this.ot(4)];case 215:return r=this.ut.getInt8(this.et),this.et+=1,0===r?(e=this.ut.getInt32(this.et)*Math.pow(2,32),s=this.ut.getUint32(this.et+4),this.et+=8,new Date(e+s)):[r,this.ot(8)];case 216:return r=this.ut.getInt8(this.et),this.et+=1,[r,this.ot(16)];case 217:return n=this.ut.getUint8(this.et),this.et+=1,this.nt(n);case 218:return n=this.ut.getUint16(this.et),this.et+=2,this.nt(n);case 219:return n=this.ut.getUint32(this.et),this.et+=4,this.nt(n);case 220:return n=this.ut.getUint16(this.et),this.et+=2,this.ft(n);case 221:return n=this.ut.getUint32(this.et),this.et+=4,this.ft(n);case 222:return n=this.ut.getUint16(this.et),this.et+=2,this.vt(n);case 223:return n=this.ut.getUint32(this.et),this.et+=4,this.vt(n)}throw new Error("Could not parse")};var At=function(t){var i=new kt(t),n=i.ct();if(i.et!==t.byteLength)throw new Error(t.byteLength-i.et+" trailing bytes");return n};wt.encode=mt,wt.decode=At;var Et={exports:{}};!function(t){function i(t){if(t)return function(t){for(var n in i.prototype)t[n]=i.prototype[n];return t}(t)}t.exports=i,i.prototype.on=i.prototype.addEventListener=function(t,i){return this.t=this.t||{},(this.t["$"+t]=this.t["$"+t]||[]).push(i),this},i.prototype.once=function(t,i){function n(){this.off(t,n),i.apply(this,arguments)}return n.fn=i,this.on(t,n),this},i.prototype.off=i.prototype.removeListener=i.prototype.removeAllListeners=i.prototype.removeEventListener=function(t,i){if(this.t=this.t||{},0==arguments.length)return this.t={},this;var n,r=this.t["$"+t];if(!r)return this;if(1==arguments.length)return delete this.t["$"+t],this;for(var e=0;e<r.length;e++)if((n=r[e])===i||n.fn===i){r.splice(e,1);break}return 0===r.length&&delete this.t["$"+t],this},i.prototype.emit=function(t){this.t=this.t||{};for(var i=new Array(arguments.length-1),n=this.t["$"+t],r=1;r<arguments.length;r++)i[r-1]=arguments[r];if(n){r=0;for(var e=(n=n.slice(0)).length;r<e;++r)n[r].apply(this,i)}return this},i.prototype.listeners=function(t){return this.t=this.t||{},this.t["$"+t]||[]},i.prototype.hasListeners=function(t){return!!this.listeners(t).length}}(Et);var jt,Ot=wt,Mt=Et.exports,St=yt.protocol=5,Bt=jt=yt.PacketType={CONNECT:0,DISCONNECT:1,EVENT:2,ACK:3,CONNECT_ERROR:4},Ct=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},Tt=function(t){return"string"==typeof t},Ut=function(t){return"[object Object]"===Object.prototype.toString.call(t)};function _t(){}function xt(){}_t.prototype.encode=function(t){return[Ot.encode(t)]},Mt(xt.prototype),xt.prototype.add=function(t){var i=Ot.decode(t);this.checkPacket(i),this.emit("decoded",i)},xt.prototype.checkPacket=function(t){if(!(Ct(t.type)&&t.type>=Bt.CONNECT&&t.type<=Bt.CONNECT_ERROR))throw new Error("invalid packet type");if(!Tt(t.nsp))throw new Error("invalid namespace");if(!function(t){switch(t.type){case Bt.CONNECT:return void 0===t.data||Ut(t.data);case Bt.DISCONNECT:return void 0===t.data;case Bt.CONNECT_ERROR:return Tt(t.data)||Ut(t.data);default:return Array.isArray(t.data)}}(t))throw new Error("invalid payload");if(!(void 0===t.id||Ct(t.id)))throw new Error("invalid packet id")},xt.prototype.destroy=function(){};var Dt=yt.Encoder=_t,$t=yt.Decoder=xt,It=t({__proto__:null,protocol:St,get PacketType(){return jt},Encoder:Dt,Decoder:$t,default:yt},[yt]);function Rt(t,i,n){return t.on(i,n),function(){t.off(i,n)}}var Lt=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1}),Nt=function(t){function i(i,n,r){var e;return(e=t.call(this)||this).connected=!1,e.recovered=!1,e.receiveBuffer=[],e.sendBuffer=[],e.lt=[],e.dt=0,e.ids=0,e.acks={},e.flags={},e.io=i,e.nsp=n,r&&r.auth&&(e.auth=r.auth),e.l=s({},r),e.io.yt&&e.open(),e}h(i,t);var n=i.prototype;return n.subEvents=function(){if(!this.subs){var t=this.io;this.subs=[Rt(t,"open",this.onopen.bind(this)),Rt(t,"packet",this.onpacket.bind(this)),Rt(t,"error",this.onerror.bind(this)),Rt(t,"close",this.onclose.bind(this))]}},n.connect=function(){return this.connected||(this.subEvents(),this.io.wt||this.io.open(),"open"===this.io.bt&&this.onopen()),this},n.open=function(){return this.connect()},n.send=function(){for(var t=arguments.length,i=new Array(t),n=0;n<t;n++)i[n]=arguments[n];return i.unshift("message"),this.emit.apply(this,i),this},n.emit=function(t){var i,n,r;if(Lt.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');for(var e=arguments.length,s=new Array(e>1?e-1:0),o=1;o<e;o++)s[o-1]=arguments[o];if(s.unshift(t),this.l.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this.gt(s),this;var h={type:jt.EVENT,data:s,options:{}};if(h.options.compress=!1!==this.flags.compress,"function"==typeof s[s.length-1]){var u=this.ids++,f=s.pop();this.kt(u,f),h.id=u}var c=null===(n=null===(i=this.io.engine)||void 0===i?void 0:i.transport)||void 0===n?void 0:n.writable,a=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r.Y());return this.flags.volatile&&!c||(a?(this.notifyOutgoingListeners(h),this.packet(h)):this.sendBuffer.push(h)),this.flags={},this},n.kt=function(t,i){var n,r=this,e=null!==(n=this.flags.timeout)&&void 0!==n?n:this.l.ackTimeout;if(void 0!==e){var s=this.io.setTimeoutFn((function(){delete r.acks[t];for(var n=0;n<r.sendBuffer.length;n++)r.sendBuffer[n].id===t&&r.sendBuffer.splice(n,1);i.call(r,new Error("operation has timed out"))}),e),o=function(){r.io.clearTimeoutFn(s);for(var t=arguments.length,n=new Array(t),e=0;e<t;e++)n[e]=arguments[e];i.apply(r,n)};o.withError=!0,this.acks[t]=o}else this.acks[t]=i},n.emitWithAck=function(t){for(var i=this,n=arguments.length,r=new Array(n>1?n-1:0),e=1;e<n;e++)r[e-1]=arguments[e];return new Promise((function(n,e){var s=function(t,i){return t?e(t):n(i)};s.withError=!0,r.push(s),i.emit.apply(i,[t].concat(r))}))},n.gt=function(t){var i,n=this;"function"==typeof t[t.length-1]&&(i=t.pop());var r={id:this.dt++,tryCount:0,pending:!1,args:t,flags:s({fromQueue:!0},this.flags)};t.push((function(t){if(r===n.lt[0]){if(null!==t)r.tryCount>n.l.retries&&(n.lt.shift(),i&&i(t));else if(n.lt.shift(),i){for(var e=arguments.length,s=new Array(e>1?e-1:0),o=1;o<e;o++)s[o-1]=arguments[o];i.apply(void 0,[null].concat(s))}return r.pending=!1,n.At()}})),this.lt.push(r),this.At()},n.At=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.connected&&0!==this.lt.length){var i=this.lt[0];i.pending&&!t||(i.pending=!0,i.tryCount++,this.flags=i.flags,this.emit.apply(this,i.args))}},n.packet=function(t){t.nsp=this.nsp,this.io.Et(t)},n.onopen=function(){var t=this;"function"==typeof this.auth?this.auth((function(i){t.jt(i)})):this.jt(this.auth)},n.jt=function(t){this.packet({type:jt.CONNECT,data:this.Ot?s({pid:this.Ot,offset:this.Mt},t):t})},n.onerror=function(t){this.connected||this.emitReserved("connect_error",t)},n.onclose=function(t,i){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,i),this.St()},n.St=function(){var t=this;Object.keys(this.acks).forEach((function(i){if(!t.sendBuffer.some((function(t){return String(t.id)===i}))){var n=t.acks[i];delete t.acks[i],n.withError&&n.call(t,new Error("socket has been disconnected"))}}))},n.onpacket=function(t){if(t.nsp===this.nsp)switch(t.type){case jt.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case jt.EVENT:case jt.BINARY_EVENT:this.onevent(t);break;case jt.ACK:case jt.BINARY_ACK:this.onack(t);break;case jt.DISCONNECT:this.ondisconnect();break;case jt.CONNECT_ERROR:this.destroy();var i=new Error(t.data.message);i.data=t.data.data,this.emitReserved("connect_error",i)}},n.onevent=function(t){var i=t.data||[];null!=t.id&&i.push(this.ack(t.id)),this.connected?this.emitEvent(i):this.receiveBuffer.push(Object.freeze(i))},n.emitEvent=function(i){if(this.Bt&&this.Bt.length){var n,r=e(this.Bt.slice());try{for(r.s();!(n=r.n()).done;){n.value.apply(this,i)}}catch(t){r.e(t)}finally{r.f()}}t.prototype.emit.apply(this,i),this.Ot&&i.length&&"string"==typeof i[i.length-1]&&(this.Mt=i[i.length-1])},n.ack=function(t){var i=this,n=!1;return function(){if(!n){n=!0;for(var r=arguments.length,e=new Array(r),s=0;s<r;s++)e[s]=arguments[s];i.packet({type:jt.ACK,id:t,data:e})}}},n.onack=function(t){var i=this.acks[t.id];"function"==typeof i&&(delete this.acks[t.id],i.withError&&t.data.unshift(null),i.apply(this,t.data))},n.onconnect=function(t,i){this.id=t,this.recovered=i&&this.Ot===i,this.Ot=i,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this.At(!0)},n.emitBuffered=function(){var t=this;this.receiveBuffer.forEach((function(i){return t.emitEvent(i)})),this.receiveBuffer=[],this.sendBuffer.forEach((function(i){t.notifyOutgoingListeners(i),t.packet(i)})),this.sendBuffer=[]},n.ondisconnect=function(){this.destroy(),this.onclose("io server disconnect")},n.destroy=function(){this.subs&&(this.subs.forEach((function(t){return t()})),this.subs=void 0),this.io.Ct(this)},n.disconnect=function(){return this.connected&&this.packet({type:jt.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},n.close=function(){return this.disconnect()},n.compress=function(t){return this.flags.compress=t,this},n.timeout=function(t){return this.flags.timeout=t,this},n.onAny=function(t){return this.Bt=this.Bt||[],this.Bt.push(t),this},n.prependAny=function(t){return this.Bt=this.Bt||[],this.Bt.unshift(t),this},n.offAny=function(t){if(!this.Bt)return this;if(t){for(var i=this.Bt,n=0;n<i.length;n++)if(t===i[n])return i.splice(n,1),this}else this.Bt=[];return this},n.listenersAny=function(){return this.Bt||[]},n.onAnyOutgoing=function(t){return this.Tt=this.Tt||[],this.Tt.push(t),this},n.prependAnyOutgoing=function(t){return this.Tt=this.Tt||[],this.Tt.unshift(t),this},n.offAnyOutgoing=function(t){if(!this.Tt)return this;if(t){for(var i=this.Tt,n=0;n<i.length;n++)if(t===i[n])return i.splice(n,1),this}else this.Tt=[];return this},n.listenersAnyOutgoing=function(){return this.Tt||[]},n.notifyOutgoingListeners=function(t){if(this.Tt&&this.Tt.length){var i,n=e(this.Tt.slice());try{for(n.s();!(i=n.n()).done;){i.value.apply(this,t.data)}}catch(t){n.e(t)}finally{n.f()}}},r(i,[{key:"disconnected",get:function(){return!this.connected}},{key:"active",get:function(){return!!this.subs}},{key:"volatile",get:function(){return this.flags.volatile=!0,this}}])}($);function Pt(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Pt.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var i=Math.random(),n=Math.floor(i*this.jitter*t);t=1&Math.floor(10*i)?t+n:t-n}return 0|Math.min(t,this.max)},Pt.prototype.reset=function(){this.attempts=0},Pt.prototype.setMin=function(t){this.ms=t},Pt.prototype.setMax=function(t){this.max=t},Pt.prototype.setJitter=function(t){this.jitter=t};var Vt=function(t){function i(i,n){var r,e;(r=t.call(this)||this).nsps={},r.subs=[],i&&"object"===a(i)&&(n=i,i=void 0),(n=n||{}).path=n.path||"/socket.io",r.opts=n,V(r,n),r.reconnection(!1!==n.reconnection),r.reconnectionAttempts(n.reconnectionAttempts||1/0),r.reconnectionDelay(n.reconnectionDelay||1e3),r.reconnectionDelayMax(n.reconnectionDelayMax||5e3),r.randomizationFactor(null!==(e=n.randomizationFactor)&&void 0!==e?e:.5),r.backoff=new Pt({min:r.reconnectionDelay(),max:r.reconnectionDelayMax(),jitter:r.randomizationFactor()}),r.timeout(null==n.timeout?2e4:n.timeout),r.bt="closed",r.uri=i;var s=n.parser||It;return r.encoder=new s.Encoder,r.decoder=new s.Decoder,r.yt=!1!==n.autoConnect,r.yt&&r.open(),r}h(i,t);var n=i.prototype;return n.reconnection=function(t){return arguments.length?(this.Ut=!!t,t||(this.skipReconnect=!0),this):this.Ut},n.reconnectionAttempts=function(t){return void 0===t?this._t:(this._t=t,this)},n.reconnectionDelay=function(t){var i;return void 0===t?this.xt:(this.xt=t,null===(i=this.backoff)||void 0===i||i.setMin(t),this)},n.randomizationFactor=function(t){var i;return void 0===t?this.Dt:(this.Dt=t,null===(i=this.backoff)||void 0===i||i.setJitter(t),this)},n.reconnectionDelayMax=function(t){var i;return void 0===t?this.$t:(this.$t=t,null===(i=this.backoff)||void 0===i||i.setMax(t),this)},n.timeout=function(t){return arguments.length?(this.It=t,this):this.It},n.maybeReconnectOnOpen=function(){!this.wt&&this.Ut&&0===this.backoff.attempts&&this.reconnect()},n.open=function(t){var i=this;if(~this.bt.indexOf("open"))return this;this.engine=new pt(this.uri,this.opts);var n=this.engine,r=this;this.bt="opening",this.skipReconnect=!1;var e=Rt(n,"open",(function(){r.onopen(),t&&t()})),s=function(n){i.cleanup(),i.bt="closed",i.emitReserved("error",n),t?t(n):i.maybeReconnectOnOpen()},o=Rt(n,"error",s);if(!1!==this.It){var h=this.It,u=this.setTimeoutFn((function(){e(),s(new Error("timeout")),n.close()}),h);this.opts.autoUnref&&u.unref(),this.subs.push((function(){i.clearTimeoutFn(u)}))}return this.subs.push(e),this.subs.push(o),this},n.connect=function(t){return this.open(t)},n.onopen=function(){this.cleanup(),this.bt="open",this.emitReserved("open");var t=this.engine;this.subs.push(Rt(t,"ping",this.onping.bind(this)),Rt(t,"data",this.ondata.bind(this)),Rt(t,"error",this.onerror.bind(this)),Rt(t,"close",this.onclose.bind(this)),Rt(this.decoder,"decoded",this.ondecoded.bind(this)))},n.onping=function(){this.emitReserved("ping")},n.ondata=function(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}},n.ondecoded=function(t){var i=this;I((function(){i.emitReserved("packet",t)}),this.setTimeoutFn)},n.onerror=function(t){this.emitReserved("error",t)},n.socket=function(t,i){var n=this.nsps[t];return n?this.yt&&!n.active&&n.connect():(n=new Nt(this,t,i),this.nsps[t]=n),n},n.Ct=function(t){for(var i=0,n=Object.keys(this.nsps);i<n.length;i++){var r=n[i];if(this.nsps[r].active)return}this.Rt()},n.Et=function(t){for(var i=this.encoder.encode(t),n=0;n<i.length;n++)this.engine.write(i[n],t.options)},n.cleanup=function(){this.subs.forEach((function(t){return t()})),this.subs.length=0,this.decoder.destroy()},n.Rt=function(){this.skipReconnect=!0,this.wt=!1,this.onclose("forced close")},n.disconnect=function(){return this.Rt()},n.onclose=function(t,i){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this.bt="closed",this.emitReserved("close",t,i),this.Ut&&!this.skipReconnect&&this.reconnect()},n.reconnect=function(){var t=this;if(this.wt||this.skipReconnect)return this;var i=this;if(this.backoff.attempts>=this._t)this.backoff.reset(),this.emitReserved("reconnect_failed"),this.wt=!1;else{var n=this.backoff.duration();this.wt=!0;var r=this.setTimeoutFn((function(){i.skipReconnect||(t.emitReserved("reconnect_attempt",i.backoff.attempts),i.skipReconnect||i.open((function(n){n?(i.wt=!1,i.reconnect(),t.emitReserved("reconnect_error",n)):i.onreconnect()})))}),n);this.opts.autoUnref&&r.unref(),this.subs.push((function(){t.clearTimeoutFn(r)}))}},n.onreconnect=function(){var t=this.backoff.attempts;this.wt=!1,this.backoff.reset(),this.emitReserved("reconnect",t)},i}($),qt={};function Ft(t,i){"object"===a(t)&&(i=t,t=void 0);var n,r=function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=t;n=n||"undefined"!=typeof location&&location,null==t&&(t=n.protocol+"//"+n.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?n.protocol+t:n.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==n?n.protocol+"//"+t:"https://"+t),r=ct(t)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";var e=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+e+":"+r.port+i,r.href=r.protocol+"://"+e+(n&&n.port===r.port?"":":"+r.port),r}(t,(i=i||{}).path||"/socket.io"),e=r.source,s=r.id,o=r.path,h=qt[s]&&o in qt[s].nsps;return i.forceNew||i["force new connection"]||!1===i.multiplex||h?n=new Vt(e,i):(qt[s]||(qt[s]=new Vt(e,i)),n=qt[s]),r.query&&!i.query&&(i.query=r.queryKey),n.socket(r.path,i)}return s(Ft,{Manager:Vt,Socket:Nt,io:Ft,connect:Ft}),Ft}));
//# sourceMappingURL=socket.io.msgpack.min.js.map
