const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { Low } = require('lowdb');
const { JSONFile } = require('lowdb/node');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Database setup
const adapter = new JSONFile('demo_store.json');
const defaultData = { messages: [], patients: [], doctors: [] };
const db = new Low(adapter, defaultData);

// Initialize database
async function initDB() {
  await db.read();
  db.data ||= { messages: [], patients: [], doctors: [] };
  await db.write();
}

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Multer setup for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ storage });

// Mock AI analysis functions
function mockAudioAnalysis(filename) {
  const mockResponses = [
    { text: "I have stomach pain level 8", confidence: 0.87 },
    { text: "My headache is getting worse", confidence: 0.92 },
    { text: "I feel nauseous and dizzy", confidence: 0.85 },
    { text: "The medication isn't helping", confidence: 0.89 }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

function mockImageAnalysis(filename) {
  const mockResponses = [
    { caption: "Blister on left arm", ocr: "Amoxicillin 500 mg" },
    { caption: "Rash on skin", ocr: "Apply twice daily" },
    { caption: "Pill bottle", ocr: "Ibuprofen 400mg" },
    { caption: "Medical prescription", ocr: "Take with food" }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

// Routes
app.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { filename, mimetype } = req.file;
    const fileUrl = `/uploads/${filename}`;
    
    let analysis;
    let type;

    if (mimetype.startsWith('audio/')) {
      type = 'audio';
      analysis = mockAudioAnalysis(filename);
    } else if (mimetype.startsWith('image/')) {
      type = 'image';
      analysis = mockImageAnalysis(filename);
    } else {
      return res.status(400).json({ error: 'Unsupported file type' });
    }

    // Create message from analysis
    const message = {
      id: uuidv4(),
      from: 'patient',
      subtype: type === 'audio' ? 'symptom' : 'image',
      payload: {
        ...analysis,
        filename,
        url: fileUrl,
        mock: true
      },
      timestamp: new Date().toISOString()
    };

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Broadcast to all connected clients
    io.emit('newMessage', message);

    res.json({
      type,
      filename,
      url: fileUrl,
      analysis,
      message
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

app.post('/message', async (req, res) => {
  try {
    const { from, subtype, payload } = req.body;
    
    const message = {
      id: uuidv4(),
      from,
      subtype,
      payload,
      timestamp: new Date().toISOString()
    };

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Broadcast to all connected clients
    io.emit('newMessage', message);

    res.json(message);
  } catch (error) {
    console.error('Message error:', error);
    res.status(500).json({ error: 'Message failed' });
  }
});

app.get('/messages', async (req, res) => {
  try {
    await db.read();
    res.json(db.data.messages || []);
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  // Send safety alert (mock)
  setTimeout(() => {
    socket.emit('safetyAlert', { 
      level: 'green', 
      message: 'All systems normal' 
    });
  }, 2000);
});

const PORT = process.env.PORT || 3000;

// Initialize and start server
initDB().then(() => {
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
});
