const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { Low } = require('lowdb');
const { JSONFile } = require('lowdb/node');
const TemporalContextEngine = require('./temporal-context');
const LLMService = require('./llm-service');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Database setup
const adapter = new JSONFile('demo_store.json');
const defaultData = { messages: [], patients: [], doctors: [] };
const db = new Low(adapter, defaultData);

// Initialize Temporal Context Engine and LLM Service
const temporalEngine = new TemporalContextEngine();
const llmService = new LLMService();

// Initialize database and temporal context
async function initDB() {
  await db.read();
  db.data ||= { messages: [], patients: [], doctors: [] };
  await db.write();

  // Initialize default patient context
  temporalEngine.initializePatient('default-patient', {
    coreSymptoms: [],
    chronicConditions: ['hypertension'],
    medications: ['lisinopril']
  });

  // Check LLM service health
  const llmHealth = await llmService.healthCheck();
  console.log('LLM Service Status:', llmHealth);
}

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Multer setup for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ storage });

// Mock AI analysis functions
function mockAudioAnalysis(filename) {
  const mockResponses = [
    { text: "I have stomach pain level 8", confidence: 0.87 },
    { text: "My headache is getting worse", confidence: 0.92 },
    { text: "I feel nauseous and dizzy", confidence: 0.85 },
    { text: "The medication isn't helping", confidence: 0.89 }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

function mockImageAnalysis(filename) {
  const mockResponses = [
    { caption: "Blister on left arm", ocr: "Amoxicillin 500 mg" },
    { caption: "Rash on skin", ocr: "Apply twice daily" },
    { caption: "Pill bottle", ocr: "Ibuprofen 400mg" },
    { caption: "Medical prescription", ocr: "Take with food" }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

// Routes
app.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { filename, mimetype } = req.file;
    const fileUrl = `/uploads/${filename}`;
    
    let analysis;
    let type;

    if (mimetype.startsWith('audio/')) {
      type = 'audio';
      analysis = mockAudioAnalysis(filename);
    } else if (mimetype.startsWith('image/')) {
      type = 'image';
      analysis = mockImageAnalysis(filename);
    } else {
      return res.status(400).json({ error: 'Unsupported file type' });
    }

    // Create message from analysis
    const message = {
      id: uuidv4(),
      from: 'patient',
      subtype: type === 'audio' ? 'symptom' : 'image',
      payload: {
        ...analysis,
        filename,
        url: fileUrl,
        mock: true
      },
      timestamp: new Date().toISOString()
    };

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Broadcast to all connected clients
    io.emit('newMessage', message);

    res.json({
      type,
      filename,
      url: fileUrl,
      analysis,
      message
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

app.post('/message', async (req, res) => {
  try {
    const { from, subtype, payload, patientId = 'default-patient' } = req.body;

    const message = {
      id: uuidv4(),
      from,
      subtype,
      payload,
      timestamp: new Date().toISOString(),
      patientId
    };

    // Add to temporal context
    const layerType = subtype === 'symptom' ? 'symptom_report' :
                     subtype === 'recipe' ? 'treatment_start' : 'interaction';

    const temporalLayer = temporalEngine.addTemporalLayer(patientId, {
      type: layerType,
      data: {
        message_id: message.id,
        text: payload.text,
        symptoms: payload.symptoms || (payload.text ? [{ name: 'general', severity: 5 }] : []),
        treatment: payload.medication,
        source: from
      },
      severity: payload.severity || 'normal',
      source: from
    });

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Generate doctor summary if needed
    let doctorSummary = null;
    if (from === 'patient') {
      doctorSummary = temporalEngine.generateDoctorSummary(patientId);
    }

    // Broadcast to all connected clients
    io.emit('newMessage', {
      message,
      temporalLayer,
      doctorSummary: doctorSummary?.doctor_attention_required ? doctorSummary : null
    });

    res.json({ message, temporalLayer, doctorSummary });
  } catch (error) {
    console.error('Message error:', error);
    res.status(500).json({ error: 'Message failed' });
  }
});

app.get('/messages', async (req, res) => {
  try {
    await db.read();
    res.json(db.data.messages || []);
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// Get patient temporal context
app.get('/context/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const { layersLimit } = req.query;

    const context = temporalEngine.getPatientContext(patientId, {
      layersLimit: layersLimit ? parseInt(layersLimit) : undefined
    });

    if (!context) {
      return res.status(404).json({ error: 'Patient context not found' });
    }

    res.json(context);
  } catch (error) {
    console.error('Get context error:', error);
    res.status(500).json({ error: 'Failed to get context' });
  }
});

// Get doctor summary
app.get('/doctor-summary/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const summary = temporalEngine.generateDoctorSummary(patientId);

    if (!summary) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json(summary);
  } catch (error) {
    console.error('Get doctor summary error:', error);
    res.status(500).json({ error: 'Failed to generate summary' });
  }
});

// Add temporal layer manually (for testing)
app.post('/context/:patientId/layer', async (req, res) => {
  try {
    const { patientId } = req.params;
    const layerData = req.body;

    const layer = temporalEngine.addTemporalLayer(patientId, layerData);
    res.json(layer);
  } catch (error) {
    console.error('Add layer error:', error);
    res.status(500).json({ error: 'Failed to add temporal layer' });
  }
});

// LLM Chat endpoint for patient companion
app.post('/chat/patient/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get patient temporal context
    const temporalContext = temporalEngine.getPatientContext(patientId);

    // Generate AI response
    const aiResponse = await llmService.generatePatientResponse(
      patientId,
      message,
      temporalContext
    );

    // Create message record for the patient's message
    const patientMessage = {
      id: uuidv4(),
      from: 'patient',
      subtype: 'text',
      payload: { text: message },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Create AI response message
    const aiMessage = {
      id: uuidv4(),
      from: 'ai_companion',
      subtype: 'text',
      payload: {
        text: aiResponse.response,
        model: aiResponse.model,
        type: aiResponse.type
      },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Save both messages to database
    await db.read();
    db.data.messages.push(patientMessage, aiMessage);
    await db.write();

    // Add temporal layers
    temporalEngine.addTemporalLayer(patientId, {
      type: 'interaction',
      data: {
        message_id: patientMessage.id,
        text: message,
        source: 'patient'
      },
      source: 'patient'
    });

    temporalEngine.addTemporalLayer(patientId, {
      type: 'ai_response',
      data: {
        message_id: aiMessage.id,
        text: aiResponse.response,
        source: 'ai_companion',
        model: aiResponse.model
      },
      source: 'ai_companion'
    });

    // Broadcast to all connected clients
    io.emit('newMessage', { message: patientMessage });
    io.emit('newMessage', { message: aiMessage });

    res.json({
      patientMessage,
      aiResponse: aiMessage,
      llmMetadata: {
        model: aiResponse.model,
        type: aiResponse.type,
        timestamp: aiResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Patient chat error:', error);
    res.status(500).json({ error: 'Failed to process chat message' });
  }
});

// LLM Research endpoint for doctors
app.post('/research/doctor', async (req, res) => {
  try {
    const { query, patientId } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Research query is required' });
    }

    // Get patient context if provided
    let patientContext = null;
    if (patientId) {
      const temporalContext = temporalEngine.getPatientContext(patientId);
      patientContext = temporalEngine.generateDoctorSummary(patientId);
    }

    // Generate research response
    const researchResponse = await llmService.generateDoctorResearch(query, patientContext);

    res.json({
      query,
      response: researchResponse.response,
      patientContext: patientContext ? {
        patientId,
        coreSymptoms: patientContext.core_symptoms,
        recentInteractions: patientContext.recent_interactions
      } : null,
      metadata: {
        model: researchResponse.model,
        type: researchResponse.type,
        timestamp: researchResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Doctor research error:', error);
    res.status(500).json({ error: 'Failed to process research query' });
  }
});

// LLM Health check endpoint
app.get('/llm/health', async (req, res) => {
  try {
    const health = await llmService.healthCheck();
    res.json(health);
  } catch (error) {
    console.error('LLM health check error:', error);
    res.status(500).json({ error: 'Failed to check LLM health' });
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  // Send safety alert (mock)
  setTimeout(() => {
    socket.emit('safetyAlert', { 
      level: 'green', 
      message: 'All systems normal' 
    });
  }, 2000);
});

const PORT = process.env.PORT || 3000;

// Initialize and start server
initDB().then(() => {
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
});
