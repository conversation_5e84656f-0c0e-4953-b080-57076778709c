const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { Low } = require('lowdb');
const { JSONFile } = require('lowdb/node');
const TemporalContextEngine = require('./temporal-context');
const LLMService = require('./llm-service');
const WatsonService = require('./watson-service');

// Load environment variables
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Database setup
const adapter = new JSONFile('demo_store.json');
const defaultData = { messages: [], patients: [], doctors: [] };
const db = new Low(adapter, defaultData);

// Initialize Temporal Context Engine, LLM Service, and Watson Service
const temporalEngine = new TemporalContextEngine();
const llmService = new LLMService();
const watsonService = new WatsonService();

// Initialize database and temporal context
async function initDB() {
  await db.read();
  db.data ||= { messages: [], patients: [], doctors: [] };
  await db.write();

  // Initialize default patient context
  temporalEngine.initializePatient('default-patient', {
    coreSymptoms: [],
    chronicConditions: ['hypertension'],
    medications: ['lisinopril']
  });

  // Initialize test patient for integration tests
  temporalEngine.initializePatient('test-patient', {
    coreSymptoms: [],
    chronicConditions: [],
    medications: []
  });

  // Check LLM service health
  const llmHealth = await llmService.healthCheck();
  console.log('LLM Service Status:', llmHealth);
  
  // Check Watson service health
  const watsonHealth = await watsonService.healthCheck();
  console.log('Watson Service Status:', watsonHealth);
}

// Middleware
app.use(cors());
app.use(express.json());
app.use('/uploads', express.static('uploads'));

// Multer setup for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({ storage });

// Mock AI analysis functions
function mockAudioAnalysis(filename) {
  const mockResponses = [
    { text: "I have stomach pain level 8", confidence: 0.87 },
    { text: "My headache is getting worse", confidence: 0.92 },
    { text: "I feel nauseous and dizzy", confidence: 0.85 },
    { text: "The medication isn't helping", confidence: 0.89 }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

function mockImageAnalysis(filename) {
  const mockResponses = [
    { caption: "Blister on left arm", ocr: "Amoxicillin 500 mg" },
    { caption: "Rash on skin", ocr: "Apply twice daily" },
    { caption: "Pill bottle", ocr: "Ibuprofen 400mg" },
    { caption: "Medical prescription", ocr: "Take with food" }
  ];
  return mockResponses[Math.floor(Math.random() * mockResponses.length)];
}

// Routes
app.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { filename, mimetype } = req.file;
    const fileUrl = `/uploads/${filename}`;
    
    let analysis;
    let type;

    if (mimetype.startsWith('audio/')) {
      type = 'audio';
      // Use Watson Speech-to-Text for real audio analysis
      const audioPath = path.join(__dirname, 'uploads', filename);
      analysis = await watsonService.speechToText(audioPath, mimetype);
    } else if (mimetype.startsWith('image/')) {
      type = 'image';
      // Use mock image analysis for now (Visual Recognition deprecated)
      analysis = mockImageAnalysis(filename);
    } else {
      return res.status(400).json({ error: 'Unsupported file type' });
    }

    // Create message from analysis
    const message = {
      id: uuidv4(),
      from: 'patient',
      subtype: type === 'audio' ? 'symptom' : 'image',
      payload: {
        ...analysis,
        filename,
        url: fileUrl,
        mock: analysis.mock || false
      },
      timestamp: new Date().toISOString()
    };

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Broadcast to all connected clients
    io.emit('newMessage', message);

    res.json({
      type,
      filename,
      url: fileUrl,
      analysis,
      message
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

app.post('/message', async (req, res) => {
  try {
    const { from, subtype, payload, patientId = 'default-patient' } = req.body;

    const message = {
      id: uuidv4(),
      from,
      subtype,
      payload,
      timestamp: new Date().toISOString(),
      patientId
    };

    // Add to temporal context
    const layerType = subtype === 'symptom' ? 'symptom_report' :
                     subtype === 'recipe' ? 'treatment_start' : 'interaction';

    const temporalLayer = temporalEngine.addTemporalLayer(patientId, {
      type: layerType,
      data: {
        message_id: message.id,
        text: payload.text,
        symptoms: payload.symptoms || (payload.text ? [{ name: 'general', severity: 5 }] : []),
        treatment: payload.medication,
        source: from
      },
      severity: payload.severity || 'normal',
      source: from
    });

    // Save to database
    await db.read();
    db.data.messages.push(message);
    await db.write();

    // Generate doctor summary if needed
    let doctorSummary = null;
    if (from === 'patient') {
      doctorSummary = temporalEngine.generateDoctorSummary(patientId);
    }

    // Broadcast to all connected clients
    io.emit('newMessage', {
      message,
      temporalLayer,
      doctorSummary: doctorSummary?.doctor_attention_required ? doctorSummary : null
    });

    res.json({ message, temporalLayer, doctorSummary });
  } catch (error) {
    console.error('Message error:', error);
    res.status(500).json({ error: 'Message failed' });
  }
});

app.get('/messages', async (req, res) => {
  try {
    await db.read();
    res.json(db.data.messages || []);
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ error: 'Failed to get messages' });
  }
});

// Get patient temporal context
app.get('/context/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const { layersLimit } = req.query;

    const context = temporalEngine.getPatientContext(patientId, {
      layersLimit: layersLimit ? parseInt(layersLimit) : undefined
    });

    if (!context) {
      return res.status(404).json({ error: 'Patient context not found' });
    }

    res.json(context);
  } catch (error) {
    console.error('Get context error:', error);
    res.status(500).json({ error: 'Failed to get context' });
  }
});

// Get doctor summary
app.get('/doctor-summary/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const summary = temporalEngine.generateDoctorSummary(patientId);

    if (!summary) {
      return res.status(404).json({ error: 'Patient not found' });
    }

    res.json(summary);
  } catch (error) {
    console.error('Get doctor summary error:', error);
    res.status(500).json({ error: 'Failed to generate summary' });
  }
});

// Add temporal layer manually (for testing)
app.post('/context/:patientId/layer', async (req, res) => {
  try {
    const { patientId } = req.params;
    const layerData = req.body;

    const layer = temporalEngine.addTemporalLayer(patientId, layerData);
    res.json(layer);
  } catch (error) {
    console.error('Add layer error:', error);
    res.status(500).json({ error: 'Failed to add temporal layer' });
  }
});

// LLM Chat endpoint for patient companion
app.post('/chat/patient/:patientId', async (req, res) => {
  try {
    const { patientId } = req.params;
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get patient temporal context
    const temporalContext = temporalEngine.getPatientContext(patientId);

    // Generate AI response
    const aiResponse = await llmService.generatePatientResponse(
      patientId,
      message,
      temporalContext
    );

    // Create message record for the patient's message
    const patientMessage = {
      id: uuidv4(),
      from: 'patient',
      subtype: 'text',
      payload: { text: message },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Create AI response message
    const aiMessage = {
      id: uuidv4(),
      from: 'ai_companion',
      subtype: 'text',
      payload: {
        text: aiResponse.response,
        model: aiResponse.model,
        type: aiResponse.type
      },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Save both messages to database
    await db.read();
    db.data.messages.push(patientMessage, aiMessage);
    await db.write();

    // Add temporal layers
    temporalEngine.addTemporalLayer(patientId, {
      type: 'interaction',
      data: {
        message_id: patientMessage.id,
        text: message,
        source: 'patient'
      },
      source: 'patient'
    });

    temporalEngine.addTemporalLayer(patientId, {
      type: 'ai_response',
      data: {
        message_id: aiMessage.id,
        text: aiResponse.response,
        source: 'ai_companion',
        model: aiResponse.model
      },
      source: 'ai_companion'
    });

    // Broadcast to all connected clients
    io.emit('newMessage', { message: patientMessage });
    io.emit('newMessage', { message: aiMessage });

    res.json({
      patientMessage,
      aiResponse: aiMessage,
      llmMetadata: {
        model: aiResponse.model,
        type: aiResponse.type,
        timestamp: aiResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Patient chat error:', error);
    res.status(500).json({ error: 'Failed to process chat message' });
  }
});

// LLM Research endpoint for doctors
app.post('/research/doctor', async (req, res) => {
  try {
    const { query, patientId } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Research query is required' });
    }

    // Get patient context if provided
    let patientContext = null;
    if (patientId) {
      const temporalContext = temporalEngine.getPatientContext(patientId);
      patientContext = temporalEngine.generateDoctorSummary(patientId);
    }

    // Generate research response
    const researchResponse = await llmService.generateDoctorResearch(query, patientContext);

    res.json({
      query,
      response: researchResponse.response,
      patientContext: patientContext ? {
        patientId,
        coreSymptoms: patientContext.core_symptoms,
        recentInteractions: patientContext.recent_interactions
      } : null,
      metadata: {
        model: researchResponse.model,
        type: researchResponse.type,
        timestamp: researchResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Doctor research error:', error);
    res.status(500).json({ error: 'Failed to process research query' });
  }
});

// LLM Health check endpoint
app.get('/llm/health', async (req, res) => {
  try {
    const health = await llmService.healthCheck();
    res.json(health);
  } catch (error) {
    console.error('LLM health check error:', error);
    res.status(500).json({ error: 'Failed to check LLM health' });
  }
});

// Watson Text-to-Speech endpoint
app.post('/watson/tts', async (req, res) => {
  try {
    const { text, voice = 'en-US_AllisonV3Voice', patientId } = req.body;

    if (!text) {
      return res.status(400).json({ error: 'Text is required for speech synthesis' });
    }

    // Generate speech
    const speechResult = await watsonService.synthesizeSpeech(text, voice);
    
    if (!speechResult.success) {
      return res.status(500).json({ 
        error: 'Speech synthesis failed', 
        details: speechResult.error 
      });
    }

    // Create audio message record
    const audioMessage = {
      id: uuidv4(),
      from: 'ai_companion',
      subtype: 'audio_response',
      payload: {
        text: text,
        voice: voice,
        audioPath: speechResult.audioPath,
        audioUrl: `/uploads/${path.basename(speechResult.audioPath)}`,
        service: speechResult.service,
        mock: speechResult.mock || false
      },
      timestamp: new Date().toISOString(),
      patientId: patientId || 'default-patient'
    };

    // Save to database
    await db.read();
    db.data.messages.push(audioMessage);
    await db.write();

    // Broadcast to connected clients
    io.emit('newMessage', { message: audioMessage });

    res.json({
      success: true,
      message: audioMessage,
      audioUrl: audioMessage.payload.audioUrl,
      metadata: {
        voice: voice,
        textLength: text.length,
        audioSize: speechResult.audioSize,
        service: speechResult.service
      }
    });
  } catch (error) {
    console.error('Watson TTS error:', error);
    res.status(500).json({ error: 'Failed to synthesize speech' });
  }
});

// Watson Speech-to-Text endpoint (for direct audio processing)
app.post('/watson/stt', upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'Audio file is required' });
    }

    const { filename, mimetype } = req.file;
    const audioPath = path.join(__dirname, 'uploads', filename);

    // Process audio with Watson STT
    const transcriptionResult = await watsonService.speechToText(audioPath, mimetype);

    res.json({
      success: transcriptionResult.success,
      transcription: transcriptionResult.text,
      confidence: transcriptionResult.confidence,
      alternatives: transcriptionResult.alternatives,
      keywords: transcriptionResult.keywords,
      metadata: {
        filename: filename,
        service: transcriptionResult.service,
        model: transcriptionResult.model,
        mock: transcriptionResult.mock || false
      },
      error: transcriptionResult.error
    });
  } catch (error) {
    console.error('Watson STT error:', error);
    res.status(500).json({ error: 'Failed to transcribe audio' });
  }
});

// Watson available voices endpoint
app.get('/watson/voices', async (req, res) => {
  try {
    const voicesResult = await watsonService.getAvailableVoices();
    res.json(voicesResult);
  } catch (error) {
    console.error('Watson voices error:', error);
    res.status(500).json({ error: 'Failed to get available voices' });
  }
});

// Watson health check endpoint
app.get('/watson/health', async (req, res) => {
  try {
    const health = await watsonService.healthCheck();
    res.json(health);
  } catch (error) {
    console.error('Watson health check error:', error);
    res.status(500).json({ error: 'Failed to check Watson health' });
  }
});

// Bridge communication endpoint (Doctor -> Patient translation)
app.post('/bridge/doctor-to-patient', async (req, res) => {
  try {
    const { message, patientId = 'default-patient' } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get patient context
    const patientContext = temporalEngine.getPatientContext(patientId);

    // Generate patient-friendly translation
    const bridgeResponse = await llmService.generateBridgeResponse(
      message,
      'doctor',
      'patient',
      patientContext
    );

    // Create bridged message record
    const bridgedMessage = {
      id: uuidv4(),
      from: 'ai_bridge',
      subtype: 'doctor_translation',
      payload: {
        originalMessage: message,
        translatedMessage: bridgeResponse.response,
        sourceType: 'doctor',
        targetType: 'patient',
        approved: false // Requires doctor approval
      },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Save to database
    await db.read();
    db.data.messages.push(bridgedMessage);
    await db.write();

    // Broadcast to connected clients for approval workflow
    io.emit('bridgeMessage', {
      message: bridgedMessage,
      requiresApproval: true
    });

    res.json({
      success: true,
      originalMessage: message,
      translatedMessage: bridgeResponse.response,
      bridgeMessage: bridgedMessage,
      metadata: {
        model: bridgeResponse.model,
        type: bridgeResponse.type,
        timestamp: bridgeResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Doctor-to-patient bridge error:', error);
    res.status(500).json({ error: 'Failed to bridge doctor message' });
  }
});

// Bridge communication endpoint (Patient -> Doctor translation)
app.post('/bridge/patient-to-doctor', async (req, res) => {
  try {
    const { message, patientId = 'default-patient' } = req.body;

    if (!message) {
      return res.status(400).json({ error: 'Message is required' });
    }

    // Get patient context
    const patientContext = temporalEngine.getPatientContext(patientId);

    // Generate clinical summary for doctor
    const bridgeResponse = await llmService.generateBridgeResponse(
      message,
      'patient',
      'doctor',
      patientContext
    );

    // Create bridged message record
    const bridgedMessage = {
      id: uuidv4(),
      from: 'ai_bridge',
      subtype: 'patient_analysis',
      payload: {
        originalMessage: message,
        clinicalSummary: bridgeResponse.response,
        sourceType: 'patient',
        targetType: 'doctor',
        urgencyLevel: 'normal' // Could be extracted from AI analysis
      },
      timestamp: new Date().toISOString(),
      patientId
    };

    // Save to database
    await db.read();
    db.data.messages.push(bridgedMessage);
    await db.write();

    // Broadcast to doctor dashboard
    io.emit('clinicalSummary', {
      message: bridgedMessage,
      patientId,
      urgency: bridgedMessage.payload.urgencyLevel
    });

    res.json({
      success: true,
      originalMessage: message,
      clinicalSummary: bridgeResponse.response,
      bridgeMessage: bridgedMessage,
      metadata: {
        model: bridgeResponse.model,
        type: bridgeResponse.type,
        timestamp: bridgeResponse.timestamp
      }
    });
  } catch (error) {
    console.error('Patient-to-doctor bridge error:', error);
    res.status(500).json({ error: 'Failed to bridge patient message' });
  }
});

// Approve bridged message endpoint
app.post('/bridge/approve/:messageId', async (req, res) => {
  try {
    const { messageId } = req.params;
    const { approved, modifications } = req.body;

    await db.read();
    const messageIndex = db.data.messages.findIndex(msg => msg.id === messageId);
    
    if (messageIndex === -1) {
      return res.status(404).json({ error: 'Message not found' });
    }

    const message = db.data.messages[messageIndex];
    
    if (message.from !== 'ai_bridge') {
      return res.status(400).json({ error: 'Message is not a bridge message' });
    }

    // Update message approval status
    message.payload.approved = approved;
    if (modifications) {
      message.payload.finalMessage = modifications;
    }
    message.payload.approvalTimestamp = new Date().toISOString();

    await db.write();

    // If approved, send final message to patient
    if (approved) {
      const finalMessage = {
        id: uuidv4(),
        from: 'doctor',
        subtype: 'approved_response',
        payload: {
          text: modifications || message.payload.translatedMessage,
          originalBridgeId: messageId
        },
        timestamp: new Date().toISOString(),
        patientId: message.patientId
      };

      db.data.messages.push(finalMessage);
      await db.write();

      // Broadcast approved message
      io.emit('newMessage', { message: finalMessage });
      
      // Generate TTS for patient if enabled
      try {
        const ttsResult = await watsonService.synthesizeSpeech(
          finalMessage.payload.text,
          'en-US_AllisonV3Voice'
        );
        
        if (ttsResult.success) {
          io.emit('audioResponse', {
            messageId: finalMessage.id,
            audioUrl: `/uploads/${path.basename(ttsResult.audioPath)}`
          });
        }
      } catch (ttsError) {
        console.log('TTS generation failed for approved message:', ttsError.message);
      }
    }

    res.json({
      success: true,
      approved,
      messageId,
      finalMessage: approved ? (modifications || message.payload.translatedMessage) : null
    });
  } catch (error) {
    console.error('Bridge approval error:', error);
    res.status(500).json({ error: 'Failed to approve bridge message' });
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });

  // Send safety alert (mock)
  setTimeout(() => {
    socket.emit('safetyAlert', { 
      level: 'green', 
      message: 'All systems normal' 
    });
  }, 2000);
});

const PORT = process.env.PORT || 3000;

// Initialize and start server
initDB().then(() => {
  server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
});
