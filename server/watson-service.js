/**
 * IBM WATSON SERVICE - SPEECH & VISION INTEGRATION
 * "The superior man is easy to serve but difficult to please." - Confucius
 * 
 * This module integrates IBM Watson Speech-to-Text, Text-to-Speech services
 * for voice interaction capabilities in our medical AI system.
 */

const fs = require('fs');
const path = require('path');
const SpeechToTextV1 = require('ibm-watson/speech-to-text/v1');
const TextToSpeechV1 = require('ibm-watson/text-to-speech/v1');
const { IamAuthenticator } = require('ibm-watson/auth');

class WatsonService {
  constructor() {
    this.speechToText = null;
    this.textToSpeech = null;
    this.isInitialized = false;
    
    // Initialize Watson services with environment variables
    this.initializeServices();
  }

  /**
   * Initialize IBM Watson services with API credentials
   */
  initializeServices() {
    try {
      // Speech to Text service
      if (process.env.WATSON_STT_API_KEY && process.env.WATSON_STT_URL) {
        this.speechToText = new SpeechToTextV1({
          authenticator: new IamAuthenticator({
            apikey: process.env.WATSON_STT_API_KEY,
          }),
          serviceUrl: process.env.WATSON_STT_URL,
          headers: {
            'X-Watson-Learning-Opt-Out': 'true' // Opt out of data collection for privacy
          }
        });
        console.log('✅ Watson Speech-to-Text service initialized');
      } else {
        console.log('⚠️  Watson STT credentials not found - using mock responses');
      }

      // Text to Speech service
      if (process.env.WATSON_TTS_API_KEY && process.env.WATSON_TTS_URL) {
        this.textToSpeech = new TextToSpeechV1({
          authenticator: new IamAuthenticator({
            apikey: process.env.WATSON_TTS_API_KEY,
          }),
          serviceUrl: process.env.WATSON_TTS_URL,
          headers: {
            'X-Watson-Learning-Opt-Out': 'true' // Opt out of data collection for privacy
          }
        });
        console.log('✅ Watson Text-to-Speech service initialized');
      } else {
        console.log('⚠️  Watson TTS credentials not found - using mock responses');
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Watson service initialization failed:', error);
      this.isInitialized = false;
    }
  }

  /**
   * Convert audio file to text using Watson Speech-to-Text
   * @param {string} audioFilePath - Path to the audio file
   * @param {string} contentType - MIME type of the audio file (e.g., 'audio/wav', 'audio/mp3')
   * @returns {Promise<Object>} Transcription result with confidence scores
   */
  async speechToText(audioFilePath, contentType = 'audio/wav') {
    try {
      // Check if service is available
      if (!this.speechToText) {
        return this.mockSpeechToText(audioFilePath);
      }

      // Check if file exists
      if (!fs.existsSync(audioFilePath)) {
        throw new Error(`Audio file not found: ${audioFilePath}`);
      }

      // Read audio file
      const audioBuffer = fs.readFileSync(audioFilePath);

      // Configure recognition parameters for medical audio
      const recognizeParams = {
        audio: audioBuffer,
        contentType: contentType,
        model: 'en-US_BroadbandModel', // Best for general speech
        keywords: [
          'pain', 'headache', 'nausea', 'fever', 'dizziness', 'medication', 
          'doctor', 'hospital', 'emergency', 'symptoms', 'treatment', 'pharmacy'
        ],
        keywordsThreshold: 0.5,
        maxAlternatives: 3,
        wordAlternativesThreshold: 0.9,
        wordConfidence: true,
        timestamps: true,
        speakerLabels: false,
        smartFormatting: true,
        profanityFilter: false // Medical terms might be filtered
      };

      const response = await this.speechToText.recognize(recognizeParams);
      
      // Process results
      const results = response.result.results;
      if (!results || results.length === 0) {
        return {
          success: false,
          text: '',
          confidence: 0,
          alternatives: [],
          keywords: [],
          error: 'No speech detected in audio'
        };
      }

      // Get best transcription
      const bestResult = results[0];
      const bestAlternative = bestResult.alternatives[0];
      
      // Extract keywords found
      const detectedKeywords = response.result.keywords || {};
      const medicalKeywords = Object.keys(detectedKeywords).map(keyword => ({
        word: keyword,
        confidence: detectedKeywords[keyword][0].confidence,
        startTime: detectedKeywords[keyword][0].start_time,
        endTime: detectedKeywords[keyword][0].end_time
      }));

      return {
        success: true,
        text: bestAlternative.transcript.trim(),
        confidence: bestAlternative.confidence,
        alternatives: bestResult.alternatives.slice(1).map(alt => ({
          text: alt.transcript.trim(),
          confidence: alt.confidence
        })),
        keywords: medicalKeywords,
        wordDetails: bestAlternative.word_confidence || [],
        timestamps: bestAlternative.timestamps || [],
        service: 'watson_stt',
        model: 'en-US_BroadbandModel',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Watson Speech-to-Text error:', error);
      
      // Fallback to mock if Watson fails
      return this.mockSpeechToText(audioFilePath, error.message);
    }
  }

  /**
   * Convert text to speech using Watson Text-to-Speech
   * @param {string} text - Text to convert to speech
   * @param {string} voice - Voice to use (default: 'en-US_AllisonV3Voice')
   * @param {string} outputPath - Path to save the audio file
   * @returns {Promise<Object>} Audio synthesis result
   */
  async synthesizeSpeech(text, voice = 'en-US_AllisonV3Voice', outputPath = null) {
    try {
      // Check if service is available
      if (!this.textToSpeech) {
        return this.mockTextToSpeech(text, outputPath);
      }

      // Validate input
      if (!text || typeof text !== 'string') {
        throw new Error('Text input is required and must be a string');
      }

      // Configure synthesis parameters for medical content
      const synthesizeParams = {
        text: text,
        accept: 'audio/wav',
        voice: voice // Professional, clear voice for medical content
      };

      const response = await this.textToSpeech.synthesize(synthesizeParams);
      
      // Repair WAV header for proper audio playback
      const audioBuffer = await this.textToSpeech.repairWavHeaderStream(response.result);

      // Save to file if output path specified
      let filePath = null;
      if (outputPath) {
        filePath = outputPath;
        fs.writeFileSync(filePath, audioBuffer);
      } else {
        // Generate unique filename
        const timestamp = Date.now();
        filePath = path.join(__dirname, 'uploads', `tts_${timestamp}.wav`);
        
        // Ensure uploads directory exists
        const uploadsDir = path.dirname(filePath);
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }
        
        fs.writeFileSync(filePath, audioBuffer);
      }

      return {
        success: true,
        audioPath: filePath,
        audioBuffer: audioBuffer,
        voice: voice,
        textLength: text.length,
        audioSize: audioBuffer.length,
        service: 'watson_tts',
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Watson Text-to-Speech error:', error);
      
      // Fallback to mock if Watson fails
      return this.mockTextToSpeech(text, outputPath, error.message);
    }
  }

  /**
   * Get available voices from Watson Text-to-Speech
   * @returns {Promise<Array>} List of available voices
   */
  async getAvailableVoices() {
    try {
      if (!this.textToSpeech) {
        return this.getMockVoices();
      }

      const response = await this.textToSpeech.listVoices();
      const voices = response.result.voices;

      // Filter for English voices suitable for medical content
      const medicalVoices = voices.filter(voice => 
        voice.language.startsWith('en-') && 
        (voice.gender === 'female' || voice.gender === 'male')
      ).map(voice => ({
        name: voice.name,
        language: voice.language,
        gender: voice.gender,
        description: voice.description,
        supported_features: voice.supported_features
      }));

      return {
        success: true,
        voices: medicalVoices,
        service: 'watson_tts'
      };

    } catch (error) {
      console.error('Get voices error:', error);
      return this.getMockVoices();
    }
  }

  /**
   * Health check for Watson services
   * @returns {Promise<Object>} Service health status
   */
  async healthCheck() {
    const health = {
      initialized: this.isInitialized,
      speechToText: {
        available: !!this.speechToText,
        status: 'unknown'
      },
      textToSpeech: {
        available: !!this.textToSpeech,
        status: 'unknown'
      },
      timestamp: new Date().toISOString()
    };

    // Test Speech-to-Text service
    if (this.speechToText) {
      try {
        await this.speechToText.listModels();
        health.speechToText.status = 'healthy';
      } catch (error) {
        health.speechToText.status = 'error';
        health.speechToText.error = error.message;
      }
    }

    // Test Text-to-Speech service
    if (this.textToSpeech) {
      try {
        await this.textToSpeech.listVoices();
        health.textToSpeech.status = 'healthy';
      } catch (error) {
        health.textToSpeech.status = 'error';
        health.textToSpeech.error = error.message;
      }
    }

    return health;
  }

  // MOCK FUNCTIONS FOR FALLBACK WHEN WATSON IS NOT AVAILABLE

  /**
   * Mock speech-to-text for development/fallback
   */
  mockSpeechToText(audioFilePath, errorMessage = null) {
    const mockResponses = [
      {
        text: "I have severe stomach pain, it's about level 8 out of 10",
        confidence: 0.87,
        keywords: [{ word: 'pain', confidence: 0.95, startTime: 2.1, endTime: 2.4 }]
      },
      {
        text: "My headache is getting worse and I feel nauseous",
        confidence: 0.92,
        keywords: [
          { word: 'headache', confidence: 0.89, startTime: 0.3, endTime: 0.8 },
          { word: 'nauseous', confidence: 0.76, startTime: 2.8, endTime: 3.2 }
        ]
      },
      {
        text: "The medication isn't helping with my dizziness",
        confidence: 0.85,
        keywords: [
          { word: 'medication', confidence: 0.91, startTime: 0.1, endTime: 0.6 },
          { word: 'dizziness', confidence: 0.83, startTime: 2.1, endTime: 2.6 }
        ]
      },
      {
        text: "I need to see a doctor about these symptoms",
        confidence: 0.89,
        keywords: [
          { word: 'doctor', confidence: 0.95, startTime: 1.2, endTime: 1.5 },
          { word: 'symptoms', confidence: 0.87, startTime: 2.8, endTime: 3.3 }
        ]
      }
    ];

    const mockResult = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    
    return {
      success: !errorMessage,
      text: mockResult.text,
      confidence: mockResult.confidence,
      alternatives: [
        { text: mockResult.text + " maybe", confidence: mockResult.confidence - 0.1 }
      ],
      keywords: mockResult.keywords,
      service: 'mock_stt',
      mock: true,
      error: errorMessage,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Mock text-to-speech for development/fallback
   */
  mockTextToSpeech(text, outputPath = null, errorMessage = null) {
    // Create a placeholder audio file path
    const timestamp = Date.now();
    const filePath = outputPath || path.join(__dirname, 'uploads', `mock_tts_${timestamp}.wav`);
    
    // Create a small mock WAV file (just headers, no actual audio)
    const mockWavBuffer = Buffer.from([
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      0x24, 0x00, 0x00, 0x00, // File size
      0x57, 0x41, 0x56, 0x45, // "WAVE"
      0x66, 0x6D, 0x74, 0x20, // "fmt "
      0x10, 0x00, 0x00, 0x00, // Subchunk size
      0x01, 0x00, 0x01, 0x00, // Audio format, channels
      0x44, 0xAC, 0x00, 0x00, // Sample rate
      0x88, 0x58, 0x01, 0x00, // Byte rate
      0x02, 0x00, 0x10, 0x00, // Block align, bits per sample
      0x64, 0x61, 0x74, 0x61, // "data"
      0x00, 0x00, 0x00, 0x00  // Data size
    ]);

    if (outputPath) {
      try {
        // Ensure directory exists
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, mockWavBuffer);
      } catch (error) {
        console.error('Mock TTS file write error:', error);
      }
    }

    return {
      success: !errorMessage,
      audioPath: filePath,
      audioBuffer: mockWavBuffer,
      voice: 'mock_voice',
      textLength: text.length,
      audioSize: mockWavBuffer.length,
      service: 'mock_tts',
      mock: true,
      error: errorMessage,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Mock voices list for development/fallback
   */
  getMockVoices() {
    return {
      success: true,
      voices: [
        {
          name: 'en-US_AllisonV3Voice',
          language: 'en-US',
          gender: 'female',
          description: 'Allison: American English female voice'
        },
        {
          name: 'en-US_MichaelV3Voice',
          language: 'en-US',
          gender: 'male',
          description: 'Michael: American English male voice'
        },
        {
          name: 'en-GB_KateV3Voice',
          language: 'en-GB',
          gender: 'female',
          description: 'Kate: British English female voice'
        }
      ],
      service: 'mock_tts',
      mock: true
    };
  }
}

module.exports = WatsonService;