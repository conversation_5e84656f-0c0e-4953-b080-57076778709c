{"messages": [{"id": "edaef8fe-0c8c-44bc-9bae-68cb68d50ec4", "patientId": "demo-patient-001", "from": "patient", "subtype": "symptom", "payload": {"text": "I have been experiencing severe headaches for the past 3 days. The pain is throbbing and gets worse with light.", "symptoms": [{"name": "headaches", "severity": 8}], "confidence": 0.92, "mock": true}, "timestamp": "2025-06-23T02:26:59.160Z"}, {"id": "4691a073-9078-443b-b90a-ef28e7ad347a", "patientId": "demo-patient-001", "from": "doctor", "subtype": "recipe", "payload": {"medication": "Ibuprofen", "dosage": "400mg", "frequency": "Every 6 hours", "instructions": "Take with food. Monitor symptoms.", "mock": true}, "timestamp": "2025-06-24T04:05:59.160Z"}, {"id": "9c182e23-dbb3-4ca4-bc3c-07b6831c1f77", "patientId": "demo-patient-001", "from": "patient", "subtype": "symptom", "payload": {"text": "The ibuprofen helped a bit, but I still have mild headaches. Also feeling more tired than usual.", "symptoms": [{"name": "headaches", "severity": 4}, {"name": "fatigue", "severity": 6}], "confidence": 0.88, "mock": true}, "timestamp": "2025-06-25T12:44:59.160Z"}, {"id": "ea099d95-763a-4a2a-977d-ca94e2911b09", "patientId": "demo-patient-001", "from": "patient", "subtype": "text", "payload": {"text": "Should I be concerned about the fatigue? It's been getting worse.", "mock": true}, "timestamp": "2025-06-26T04:05:59.160Z"}, {"id": "c114aab5-9820-4b36-bbd7-6e6725f05e1f", "patientId": "demo-patient-001", "from": "doctor", "subtype": "text", "payload": {"text": "Let's monitor the fatigue. Please track your sleep and stress levels. Continue the ibuprofen as needed.", "mock": true}, "timestamp": "2025-06-26T15:10:59.160Z"}, {"id": "00bc1948-aa6f-4265-890e-b771ac2962f3", "patientId": "demo-patient-001", "from": "patient", "subtype": "image", "payload": {"caption": "My medication bottle - want to make sure I'm taking the right dose", "ocr": "Ibuprofen 400mg", "filename": "mock-pill.jpg", "url": "/uploads/mock-pill.jpg", "mock": true}, "timestamp": "2025-06-27T13:55:59.160Z"}, {"id": "00a8c5e0-5820-4b14-a887-3c6873a46ab0", "patientId": "demo-patient-001", "from": "patient", "subtype": "symptom", "payload": {"text": "Headaches are much better now, but the fatigue persists. Sleep has been poor.", "symptoms": [{"name": "headaches", "severity": 2}, {"name": "fatigue", "severity": 7}, {"name": "insomnia", "severity": 5}], "confidence": 0.91, "mock": true}, "timestamp": "2025-06-28T11:49:59.160Z"}, {"id": "52ab73e3-4ad6-4ca2-96b7-f257e553270d", "patientId": "demo-patient-001", "from": "doctor", "subtype": "text", "payload": {"text": "Good progress on headaches. Let's address the sleep issues. I'm scheduling a follow-up for next week.", "mock": true}, "timestamp": "2025-06-29T00:16:59.160Z"}, {"id": "c27ada97-2383-4daa-b021-ce0e970aaa01", "patientId": "demo-patient-001", "from": "patient", "subtype": "text", "payload": {"text": "Thank you doctor. I appreciate your help with this.", "mock": true}, "timestamp": "2025-06-29T16:22:59.160Z"}, {"id": "96445021-b6e7-4544-85ea-c2921ed8b84b", "from": "patient", "subtype": "symptom", "payload": {"text": "I have a severe headache and feel dizzy", "symptoms": [{"name": "headache", "severity": 8}, {"name": "dizziness", "severity": 6}], "severity": "high"}, "timestamp": "2025-06-29T01:16:39.713Z", "patientId": "default-patient"}, {"id": "277be898-14d6-4620-8352-dc16198b41f4", "from": "doctor", "subtype": "text", "payload": {"text": "hello"}, "timestamp": "2025-06-29T01:26:28.350Z", "patientId": "default-patient"}, {"id": "128dcefc-abbe-4268-ac3d-8bf1de770ea1", "from": "patient", "subtype": "symptom", "payload": {"text": "I have been experiencing severe migraines with nausea for the past 3 days. The pain is getting worse.", "symptoms": [{"name": "migraine", "severity": 9}, {"name": "nausea", "severity": 7}], "severity": "high"}, "timestamp": "2025-06-29T01:31:02.069Z", "patientId": "default-patient"}], "patients": [{"id": "7f8d5711-10b5-48b3-bdab-bae3ffba954f", "name": "<PERSON>", "age": 35, "conditions": ["Hypertension", "Diabetes Type 2"]}], "doctors": [{"id": "289d9372-b3f9-4ef2-a27e-e4e53e6f5061", "name": "Dr. <PERSON>", "specialty": "Internal Medicine", "license": "MD12345"}]}