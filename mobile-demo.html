<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Symptom-OS Mobile Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #F8F9FA;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .phone-container {
            width: 375px;
            height: 667px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .app-content {
            height: calc(100% - 44px - 80px);
            display: flex;
            flex-direction: column;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #1A1A1A;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .blob-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .blob {
            width: 200px;
            height: 200px;
            background: linear-gradient(45deg, #E3F2FD, #BBDEFB);
            border-radius: 50%;
            border: 3px solid #2196F3;
            animation: breathe 3s ease-in-out infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        @keyframes breathe {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .blob.recording {
            background: linear-gradient(45deg, #FFEBEE, #FFCDD2);
            border-color: #F44336;
            animation: pulse 0.6s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            100% { transform: scale(1.2); }
        }
        
        .state-text {
            font-size: 16px;
            color: #666;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .mic-button {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: #007AFF;
            border: none;
            color: white;
            font-size: 32px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,122,255,0.3);
            transition: all 0.2s;
        }
        
        .mic-button:hover {
            transform: scale(1.05);
        }
        
        .mic-button.recording {
            background: #FF3B30;
        }
        
        .image-button {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: #F0F0F0;
            border: 1px solid #E0E0E0;
            font-size: 24px;
            cursor: pointer;
        }
        
        .text-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #E0E0E0;
            border-radius: 20px;
            font-size: 16px;
            outline: none;
        }
        
        .tab-bar {
            height: 80px;
            background: #F8F8F8;
            border-top: 1px solid #E5E5E7;
            display: flex;
            padding-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #8E8E93;
        }
        
        .tab.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .tab-text {
            font-size: 12px;
            font-weight: 600;
        }
        
        .demo-note {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="demo-note">📱 Mobile App Demo - Click mic to simulate recording</div>
    
    <div class="phone-container">
        <div class="status-bar">
            <span>9:41</span>
            <span>📶 📶 📶 🔋</span>
        </div>
        
        <div class="app-content">
            <div class="header">
                <div class="title">Symptom-OS</div>
                <div class="subtitle">How are you feeling today?</div>
            </div>
            
            <div class="blob-container">
                <div class="blob" id="blob">🎤</div>
                <div class="state-text" id="stateText">Tap to record your symptoms</div>
            </div>
            
            <div class="controls">
                <button class="mic-button" id="micButton" onclick="toggleRecording()">🎤</button>
                <button class="image-button" onclick="simulateImageUpload()">📷</button>
            </div>
            
            <input type="text" class="text-input" placeholder="Or type your message here..." onkeypress="handleTextInput(event)">
        </div>
        
        <div class="tab-bar">
            <div class="tab active">
                <div class="tab-icon">🎤</div>
                <div class="tab-text">Agent</div>
            </div>
            <div class="tab" onclick="showInbox()">
                <div class="tab-icon">💬</div>
                <div class="tab-text">Inbox</div>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingTimeout;
        
        function toggleRecording() {
            const blob = document.getElementById('blob');
            const stateText = document.getElementById('stateText');
            const micButton = document.getElementById('micButton');
            
            if (!isRecording) {
                // Start recording
                isRecording = true;
                blob.classList.add('recording');
                blob.innerHTML = '🔴';
                stateText.textContent = 'Recording... speak now';
                micButton.classList.add('recording');
                micButton.innerHTML = '⏹️';
                
                // Auto-stop after 3 seconds
                recordingTimeout = setTimeout(() => {
                    stopRecording();
                }, 3000);
            } else {
                stopRecording();
            }
        }
        
        function stopRecording() {
            const blob = document.getElementById('blob');
            const stateText = document.getElementById('stateText');
            const micButton = document.getElementById('micButton');
            
            isRecording = false;
            clearTimeout(recordingTimeout);
            
            // Show thinking state
            blob.classList.remove('recording');
            blob.innerHTML = '🤔';
            stateText.textContent = 'Processing your input...';
            micButton.classList.remove('recording');
            micButton.innerHTML = '🎤';
            
            // Simulate processing and return to idle
            setTimeout(() => {
                blob.innerHTML = '🎤';
                stateText.textContent = 'Tap to record your symptoms';
                
                // Show success message
                alert('✅ Symptom recorded: "I have stomach pain level 8" (Mock AI Response)');
            }, 2000);
        }
        
        function simulateImageUpload() {
            alert('📷 Image upload simulated!\n\nMock Analysis:\n• Caption: "Medication bottle"\n• OCR: "Ibuprofen 400mg"');
        }
        
        function handleTextInput(event) {
            if (event.key === 'Enter' && event.target.value.trim()) {
                alert('💬 Message sent: "' + event.target.value + '"');
                event.target.value = '';
            }
        }
        
        function showInbox() {
            alert('📥 Inbox View\n\nRecent Messages:\n• You: "I have stomach pain level 8"\n• Dr. Smith: "Take Ibuprofen 400mg with food"\n• You: "Thank you doctor"');
        }
    </script>
</body>
</html>
