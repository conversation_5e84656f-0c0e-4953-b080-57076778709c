<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Joy Division Style Audio Visualizer - Symptom-OS</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Courier New', monospace;
            color: white;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }

        .container {
            text-align: center;
            z-index: 10;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #ffffff, #a0a0ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.7;
            margin-bottom: 2rem;
        }

        .visualizer-container {
            position: relative;
            margin: 2rem 0;
        }

        .controls {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 0.5rem;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .state-indicator {
            margin-top: 1rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        canvas {
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">SYMPTOM-OS</h1>
        <p class="subtitle">Joy Division Style Medical AI Visualizer</p>
        
        <div class="visualizer-container">
            <canvas id="visualizer" width="600" height="400"></canvas>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="setAgentState('idle')">Idle</button>
            <button class="btn" onclick="setAgentState('listening')">Listening</button>
            <button class="btn" onclick="setAgentState('thinking')">Thinking</button>
            <button class="btn" onclick="setAgentState('speaking')">Speaking</button>
        </div>
        
        <div class="state-indicator" id="stateIndicator">
            Current State: <span id="currentState">idle</span>
        </div>
    </div>

    <script>
        // Joy Division Style Visualizer
        class JoyDivisionVisualizer {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.width = canvas.width;
                this.height = canvas.height;
                
                this.centerX = this.width / 2;
                this.centerY = this.height / 2;
                this.maxRadius = Math.min(this.width, this.height) * 0.35;
                
                this.numRings = 12;
                this.numPoints = 128;
                this.time = 0;
                
                this.frequencyData = new Array(64).fill(0);
                this.smoothedData = new Array(64).fill(0);
                
                this.agentState = 'idle';
                this.audioLevel = 0;
                
                this.animate();
            }
            
            setAgentState(state) {
                this.agentState = state;
                document.getElementById('currentState').textContent = state;
                
                // Update button states
                document.querySelectorAll('.btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                event.target.classList.add('active');
            }
            
            generateFrequencyData() {
                const baseAmplitude = this.getBaseAmplitude();
                const noiseLevel = this.getNoiseLevel();
                
                for (let i = 0; i < this.frequencyData.length; i++) {
                    let value = 0;
                    
                    switch (this.agentState) {
                        case 'listening':
                            value = (this.audioLevel * 0.9 + Math.random() * 0.1) * 
                                    (1 + Math.sin(this.time * 4 + i * 0.15) * 0.4) *
                                    Math.pow(i / this.frequencyData.length, 0.7);
                            break;
                            
                        case 'thinking':
                            const wave1 = Math.sin(this.time * 2.3 + i * 0.18);
                            const wave2 = Math.sin(this.time * 3.7 + i * 0.12);
                            const wave3 = Math.sin(this.time * 1.1 + i * 0.25);
                            value = baseAmplitude * 
                                    (0.6 + 0.4 * wave1) *
                                    (0.7 + 0.3 * wave2) *
                                    (0.8 + 0.2 * wave3) *
                                    Math.exp(-Math.pow(i - 32, 2) / 400);
                            break;
                            
                        case 'speaking':
                            const f1 = Math.exp(-Math.pow(i - 12, 2) / 80);
                            const f2 = Math.exp(-Math.pow(i - 28, 2) / 120);
                            const f3 = Math.exp(-Math.pow(i - 48, 2) / 160);
                            value = baseAmplitude * 0.9 * 
                                    (f1 + f2 * 0.8 + f3 * 0.6) *
                                    (0.7 + 0.3 * Math.sin(this.time * 12 + i * 0.4));
                            break;
                            
                        default: // idle
                            value = noiseLevel * 0.15 * 
                                    (0.5 + 0.5 * Math.sin(this.time * 0.8 + i * 0.08));
                    }
                    
                    value += (Math.random() - 0.5) * noiseLevel * 0.08;
                    this.smoothedData[i] = this.smoothedData[i] * 0.85 + value * 0.15;
                    this.frequencyData[i] = Math.max(0, Math.min(1, this.smoothedData[i]));
                }
                
                // Simulate audio level changes
                if (this.agentState === 'listening') {
                    this.audioLevel = 0.3 + 0.7 * (0.5 + 0.5 * Math.sin(this.time * 3));
                } else {
                    this.audioLevel *= 0.95;
                }
            }
            
            getBaseAmplitude() {
                switch (this.agentState) {
                    case 'listening': return 0.7 + this.audioLevel * 0.3;
                    case 'thinking': return 0.8;
                    case 'speaking': return 0.9;
                    default: return 0.25;
                }
            }
            
            getNoiseLevel() {
                switch (this.agentState) {
                    case 'listening': return 0.4;
                    case 'thinking': return 0.3;
                    case 'speaking': return 0.5;
                    default: return 0.15;
                }
            }
            
            drawBackground() {
                // Radial gradient background
                for (let r = this.maxRadius * 2; r > 0; r--) {
                    const alpha = (r / (this.maxRadius * 2)) * 0.02;
                    this.ctx.fillStyle = `rgba(10, 10, 15, ${alpha})`;
                    this.ctx.beginPath();
                    this.ctx.arc(this.centerX, this.centerY, r, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }
            
            drawFrequencyRings() {
                const ringSpacing = this.maxRadius / this.numRings;
                
                for (let ring = 0; ring < this.numRings; ring++) {
                    const baseRadius = ringSpacing * (ring + 1);
                    const opacity = 1 - (ring / this.numRings) * 0.6;
                    const strokeWeight = Math.max(0.8, 2.5 - ring * 0.15);
                    
                    this.ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
                    this.ctx.lineWidth = strokeWeight;
                    this.ctx.lineCap = 'round';
                    this.ctx.lineJoin = 'round';
                    
                    this.ctx.beginPath();
                    
                    for (let i = 0; i <= this.numPoints; i++) {
                        const angle = (i / this.numPoints) * Math.PI * 2;
                        const freqIndex = Math.floor((i / this.numPoints) * this.frequencyData.length);
                        const amplitude = this.frequencyData[freqIndex] || 0;
                        
                        const ringMod = 1 + Math.sin(this.time * 2 + ring * 0.7) * 0.08;
                        const radiusVar = amplitude * 35 * ringMod;
                        
                        const phaseOffset = ring * 0.3;
                        const finalRadius = baseRadius + radiusVar + 
                                           Math.sin(this.time * 1.5 + angle * 3 + phaseOffset) * 2;
                        
                        const x = this.centerX + Math.cos(angle) * finalRadius;
                        const y = this.centerY + Math.sin(angle) * finalRadius;
                        
                        if (i === 0) {
                            this.ctx.moveTo(x, y);
                        } else {
                            this.ctx.lineTo(x, y);
                        }
                    }
                    
                    this.ctx.closePath();
                    this.ctx.stroke();
                }
            }
            
            drawCenterElement() {
                const pulseSize = 6 + this.getBaseAmplitude() * 10;
                const glowSize = pulseSize * 3;
                
                // Outer glow
                const gradient = this.ctx.createRadialGradient(
                    this.centerX, this.centerY, 0,
                    this.centerX, this.centerY, glowSize
                );
                gradient.addColorStop(0, `rgba(255, 255, 255, ${0.3 + this.getBaseAmplitude() * 0.4})`);
                gradient.addColorStop(0.5, `rgba(255, 255, 255, ${0.1 + this.getBaseAmplitude() * 0.2})`);
                gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                
                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY, glowSize, 0, Math.PI * 2);
                this.ctx.fill();
                
                // Center dot
                this.ctx.fillStyle = `rgba(255, 255, 255, ${0.8 + this.getBaseAmplitude() * 0.2})`;
                this.ctx.beginPath();
                this.ctx.arc(this.centerX, this.centerY, pulseSize, 0, Math.PI * 2);
                this.ctx.fill();
            }
            
            animate() {
                this.time += 0.016;
                
                // Clear canvas
                this.ctx.fillStyle = '#0a0a0f';
                this.ctx.fillRect(0, 0, this.width, this.height);
                
                this.drawBackground();
                this.generateFrequencyData();
                this.drawFrequencyRings();
                this.drawCenterElement();
                
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // Initialize visualizer
        const canvas = document.getElementById('visualizer');
        const visualizer = new JoyDivisionVisualizer(canvas);
        
        // Global function for buttons
        function setAgentState(state) {
            visualizer.setAgentState(state);
        }
        
        // Auto-cycle through states for demo
        let autoDemo = false;
        const states = ['idle', 'listening', 'thinking', 'speaking'];
        let currentStateIndex = 0;
        
        setInterval(() => {
            if (autoDemo) {
                currentStateIndex = (currentStateIndex + 1) % states.length;
                setAgentState(states[currentStateIndex]);
            }
        }, 3000);
        
        // Start auto demo after 5 seconds
        setTimeout(() => {
            autoDemo = true;
        }, 5000);
    </script>
</body>
</html>
